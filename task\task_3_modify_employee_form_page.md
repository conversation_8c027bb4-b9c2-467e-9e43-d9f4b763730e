# Подзадача: Изменить страницу формы сотрудника

**Статус:** Выполнено

## Описание проблемы
В форме создания и редактирования сотрудника (EmployeeFormPage) использовался выпадающий список (`FormBuilderField<int>`) для выбора должности, что отличало логику от страницы деталей и не обеспечивало удобного поиска. Диалог выбора растягивался на весь экран.

## Решение
1. Удалить блок `FormBuilderField<int>` с выпадающим списком для `positionId`.
2. Добавить `Card` с отображением текущей должности и кнопкой "Найти и назначить", аналогично `EmployeeDetailPage`.
3. Добавить импорт `PositionAssignmentDialog`.
4. Реализовать метод `_showPositionSearchDialogForForm`, открывающий диалог поиска (фиксированный размер `width: 500`, `height: 400`) и после выбора должности вызывающий `PositionAssignmentDialog` с переданным `preselectedPositionId`.
5. Обновить метод `_loadCurrentPosition`, чтобы после назначения через диалог обновлялось `_currentPositionId` и отображалось название должности в карточке.

## Внесенные изменения
```dart
// frontend/lib/pages/employee/employee_form_page.dart

// Удален блок FormBuilderField<int> для поля 'positionId'.
// Добавлен импорт:
import 'package:frontend/widgets/position_assignment_dialog.dart';

// Добавлен Card для отображения текущей должности и кнопки:
Card(
  child: Padding(
    padding: const EdgeInsets.all(16.0),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text('Текущая должность'),
            if (_isEdit && _employee != null)
              OutlinedButton.icon(
                icon: const Icon(Icons.search),
                label: const Text('Найти и назначить'),
                onPressed: _showPositionSearchDialogForForm,
              ),
          ],
        ),
        const SizedBox(height: 16),
        Text(
          _currentPositionId != null
              ? _positions
                  .firstWhere((p) => p.id == _currentPositionId)
                  .fullTitle
              : 'Не назначена должность',
        ),
      ],
    ),
  ),
);

// Добавлен метод _showPositionSearchDialogForForm():
Future<void> _showPositionSearchDialogForForm() async { /* ... реализация с диалогом SizedBox(width:500, height:400), фильтрацией, ListTile.onTap Navigator.pop + PositionAssignmentDialog(...) */ }
```

## Обоснование
Единообразный, интуитивный интерфейс поиска и назначения должности в форме и на странице деталей упрощает работу пользователя и поддержку кода. 