import 'package:json_annotation/json_annotation.dart';

part 'rank_type.g.dart';

@JsonSerializable()
class RankType {
  final int? id;
  final String name;
  final int? serviceYearsRequired;
  final String? category;
  final String? createdAt;
  final String? updatedAt;

  RankType({
    this.id,
    required this.name,
    this.serviceYearsRequired,
    this.category,
    this.createdAt,
    this.updatedAt,
  });

  factory RankType.fromJson(Map<String, dynamic> json) => _$RankTypeFromJson(json);

  Map<String, dynamic> toJson() => _$RankTypeToJson(this);
}
