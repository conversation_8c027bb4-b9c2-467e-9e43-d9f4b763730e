import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:test/test.dart';

void main() {
  const baseUrl = 'http://localhost:8080/api';
  int? positionId;

  group('Position API Tests', () {
    test('Create Position', () async {
      final positionData = {
        'title': 'Старший инженер',
        'department': 'Технический отдел',
        'unitName': 'Отдел разработки',
        'womenAllowed': 1,
        'vus': '123456',
        'vusPss': 'ABC-123',
        'positionCodePss': 'POS-123',
        'tariffCategory': '14',
        'isFlightCrew': 0,
        'antiCorruption': 1,
        'requiredEducationLevel': 4
      };
      
      final response = await http.post(
        Uri.parse('$baseUrl/positions'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(positionData),
      );
      
      expect(response.statusCode, equals(200));
      
      final result = jsonDecode(response.body);
      expect(result['success'], isTrue);
      expect(result['data'], isNotNull);
      expect(result['data']['id'], isNotNull);
      
      positionId = result['data']['id'];
    });
    
    test('Get All Positions', () async {
      final response = await http.get(Uri.parse('$baseUrl/positions'));
      
      expect(response.statusCode, equals(200));
      
      final result = jsonDecode(response.body);
      expect(result['success'], isTrue);
      expect(result['data'], isA<List>());
    });
    
    test('Get Position By ID', () async {
      if (positionId == null) {
        fail('Position ID is null. Create Position test failed.');
      }
      
      final response = await http.get(Uri.parse('$baseUrl/positions/$positionId'));
      
      expect(response.statusCode, equals(200));
      
      final result = jsonDecode(response.body);
      expect(result['success'], isTrue);
      expect(result['data'], isNotNull);
      expect(result['data']['id'], equals(positionId));
      expect(result['data']['title'], equals('Старший инженер'));
      expect(result['data']['department'], equals('Технический отдел'));
    });
    
    test('Update Position', () async {
      if (positionId == null) {
        fail('Position ID is null. Create Position test failed.');
      }
      
      final updateData = {
        'title': 'Ведущий инженер', // Изменено название
        'department': 'Технический отдел',
        'tariffCategory': '15', // Изменена тарифная категория
      };
      
      final response = await http.put(
        Uri.parse('$baseUrl/positions/$positionId'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(updateData),
      );
      
      expect(response.statusCode, equals(200));
      
      final result = jsonDecode(response.body);
      expect(result['success'], isTrue);
      
      // Проверяем, что данные действительно обновились
      final getResponse = await http.get(Uri.parse('$baseUrl/positions/$positionId'));
      final getResult = jsonDecode(getResponse.body);
      
      expect(getResult['data']['title'], equals('Ведущий инженер'));
      expect(getResult['data']['tariffCategory'], equals('15'));
    });
    
    test('Validation Error - Missing Required Fields', () async {
      final invalidData = {
        // Отсутствует обязательное поле title
        'department': 'Тестовый отдел',
        'unitName': 'Тестовое подразделение'
      };
      
      final response = await http.post(
        Uri.parse('$baseUrl/positions'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(invalidData),
      );
      
      expect(response.statusCode, equals(400));
      
      final result = jsonDecode(response.body);
      expect(result['success'], isFalse);
      expect(result['error'], equals('Bad Request'));
    });
    
    test('Validation Error - Invalid Data Types', () async {
      final invalidData = {
        'title': 'Тестовая должность',
        'womenAllowed': 'да', // Должно быть числом (1 или 0)
        'isFlightCrew': 'нет' // Должно быть числом (1 или 0)
      };
      
      final response = await http.post(
        Uri.parse('$baseUrl/positions'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(invalidData),
      );
      
      expect(response.statusCode, equals(400));
      
      final result = jsonDecode(response.body);
      expect(result['success'], isFalse);
    });
    
    test('Delete Position', () async {
      if (positionId == null) {
        fail('Position ID is null. Create Position test failed.');
      }
      
      final response = await http.delete(Uri.parse('$baseUrl/positions/$positionId'));
      
      expect(response.statusCode, equals(200));
      
      final result = jsonDecode(response.body);
      expect(result['success'], isTrue);
      
      // Проверяем, что должность действительно удалена
      final getResponse = await http.get(Uri.parse('$baseUrl/positions/$positionId'));
      expect(getResponse.statusCode, equals(404));
    });
  });
}
