# Task: Рефакторинг настроек AdminJS и обновление поля "Пол" (gender)

**Goal:** Вынести конфигурацию ресурсов AdminJS в отдельный файл для улучшения читаемости `ok_admin/index.js` и изменить представление и хранение поля "Пол" для сотрудников.

**Sub-tasks:**

1.  **[X] `task_1_create_main_task_file.md`**: Create this task tracking file.
    *   Status: Completed
2.  **[X] `task_2_create_adminjs_options_file.md`**: Создать отдельный файл (например, `ok_admin/adminjs-resource-options.js`) для хранения конфигураций ресурсов AdminJS.
    *   Status: Completed
3.  **[X] `task_3_move_employee_options.md`**: Переместить конфигурацию ресурса `Employee` из `ok_admin/index.js` в новый файл `adminjs-resource-options.js`.
    *   Status: Completed
4.  **[X] `task_4_update_employee_model_gender.md`**: Обновить модель `Employee` (`ok_admin/models/employee.js`): изменить тип поля `gender` на INTEGER (1 для мужского, 0 для женского) и установить значение по умолчанию 1.
    *   Status: Completed
5.  **[X] `task_5_update_employee_migration_gender.md`**: Обновить файл миграции для таблицы `employees` (`ok_admin/migrations/*-create-employees.js`), чтобы отразить изменения в поле `gender` (тип INTEGER, значение по умолчанию 1).
    *   Status: Completed
6.  **[X] `task_6_update_adminjs_employee_gender_options.md`**: В файле `adminjs-resource-options.js` обновить опции для поля `gender` ресурса `Employee`: `availableValues` должны использовать `{ value: 1, label: 'Мужской' }` и `{ value: 0, label: 'Женский' }`, `defaultValue` должен быть `1`.
    *   Status: Completed
7.  **[X] `task_7_update_db_schema_gender.md`**: Обновить `db_schema.md` для отражения изменений в поле `gender` таблицы `employees`.
    *   Status: Completed
8.  **[X] `task_8_integrate_options_file_in_indexjs.md`**: Обновить `ok_admin/index.js` для импорта и использования конфигураций ресурсов из нового файла `adminjs-resource-options.js`.
    *   Status: Completed

**Desired Outcome:**
*   Конфигурация ресурсов AdminJS (на данный момент для `Employee`) находится в отдельном файле.
*   `ok_admin/index.js` импортирует эту конфигурацию.
*   Поле `gender` в таблице `employees` и модели `Employee` сохраняет значения `1` (мужской) или `0` (женский).
*   По умолчанию для новых сотрудников устанавливается мужской пол (`1`).
*   В интерфейсе AdminJS для поля `gender` отображается дропдаун с "Мужской" (значение 1) и "Женский" (значение 0), с "Мужской" по умолчанию.
*   Файл миграции для `employees` и `db_schema.md` обновлены. 