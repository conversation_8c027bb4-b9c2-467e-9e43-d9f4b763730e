# Task: Настройка фронтенда на Flutter

**Goal:** Настроить фронтенд для системы отдела кадров, используя Flutter Web.

**Steps:**

1. Проанализировать текущую структуру фронтенда в папке `frontend`
2. Обновить `pubspec.yaml` для добавления необходимых зависимостей:
   - `http` - для выполнения HTTP-запросов к API
   - `provider` или `flutter_bloc` - для управления состоянием
   - `go_router` - для маршрутизации
   - `shared_preferences` - для хранения настроек
   - `intl` - для локализации и форматирования
   - `flutter_form_builder` - для создания форм
3. Создать структуру директорий в папке `lib`:
   ```
   frontend/lib/
   ├── components/         # Переиспользуемые компоненты
   ├── pages/              # Страницы приложения
   ├── services/           # Сервисы для работы с API
   ├── utils/              # Вспомогательные функции
   ├── models/             # Модели данных
   ├── app.dart            # Корневой компонент
   └── main.dart           # Точка входа
   ```
4. Создать базовый макет приложения в `lib/app.dart`
5. Настроить маршрутизацию с помощью `go_router`
6. Создать сервис для взаимодействия с API бэкенда в `lib/services/api_service.dart`
7. Создать базовые модели данных в `lib/models/`
8. Настроить тему приложения с поддержкой светлой и темной темы

**Expected Result:**
- Работающее Flutter-приложение с базовой структурой
- Настроенная маршрутизация между страницами
- Базовый сервис для взаимодействия с API бэкенда
- Базовый макет приложения с навигацией
- Настроенная тема приложения

**Status:** Completed
