# Task: Доработка системы управления должностями сотрудников

**Goal:** Улучшить систему управления должностями сотрудников, добавив прямую связь между сотрудниками и должностями, а также реализовать интерфейс для управления назначениями.

**Sub-tasks:**

1. **[X] `task_1_create_main_task_file.md`**: Создать этот файл задачи.
   * Status: Completed
2. **[X] `task_2_update_database_structure.md`**: Обновить структуру базы данных, добавив поле position_id в таблицу employees.
   * Status: Completed
3. **[X] `task_3_update_employee_model.md`**: Обновить модели Employee в бэкенде и фронтенде для работы с прямой ссылкой на должность.
   * Status: Completed
4. **[X] `task_4_update_repositories.md`**: Обновить репозитории для работы с новой структурой данных и добавить методы для проверки занятости должности.
   * Status: Completed
5. **[X] `task_5_update_controllers.md`**: Доработать контроллеры для реализации новой логики назначения сотрудников на должности.
   * Status: Completed
6. **[X] `task_6_create_position_assignment_dialog.md`**: Создать диалоговое окно для перемещения сотрудника на новую должность с дополнительными полями.
   * Status: Completed
7. **[X] `task_7_implement_position_search.md`**: Реализовать поиск должностей с частичным совпадением слов.
   * Status: Completed
8. **[X] `task_8_update_employee_form.md`**: Обновить форму создания/редактирования сотрудника для работы с прямой ссылкой на должность.
   * Status: Completed
9. **[ ] `task_9_update_tests.md`**: Обновить существующие тесты и создать новые для проверки логики назначения сотрудников на должности.
   * Status: Pending

**Desired Outcome:**
* Система корректно обрабатывает назначение сотрудников на должности
* Существует прямая связь между таблицами должностей и сотрудников
* Сохраняется история назначений в таблице послужного списка
* Реализован интерфейс для ввода дополнительной информации при смене должности
* Система предупреждает о конфликтах при назначении на занятую должность
* Автоматически устанавливается дата окончания предыдущей должности
