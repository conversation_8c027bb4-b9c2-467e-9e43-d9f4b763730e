import 'package:flutter/material.dart';
import 'package:frontend/models/position.dart';
import 'package:frontend/services/service_history_service.dart';

class PositionSearchDialog extends StatefulWidget {
  final List<Position> positions;
  final int? currentPositionId;

  const PositionSearchDialog({
    Key? key,
    required this.positions,
    this.currentPositionId,
  }) : super(key: key);

  @override
  State<PositionSearchDialog> createState() => _PositionSearchDialogState();
}

class _PositionSearchDialogState extends State<PositionSearchDialog> {
  late TextEditingController _searchController;
  late List<Position> _filteredPositions;
  Set<int> _occupiedPositionIds = {};
  bool _isCheckingOccupancy = true;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _filteredPositions = List.from(widget.positions);
    _loadOccupiedPositions();
  }

  Future<void> _loadOccupiedPositions() async {
    try {
      final all = await ServiceHistoryService().getAll();
      final active =
          all.where((e) => e.endDate == null && e.positionId != null);
      setState(() {
        _occupiedPositionIds = active.map((e) => e.positionId!).toSet();
        _isCheckingOccupancy = false;
      });
    } catch (_) {
      setState(() => _isCheckingOccupancy = false);
    }
  }

  void _filterPositions(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredPositions = List.from(widget.positions);
      } else {
        _filteredPositions = widget.positions
            .where(
                (pos) => pos.title.toLowerCase().contains(query.toLowerCase()))
            .toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Найти должность'),
      content: SizedBox(
        width: 500,
        height: 400,
        child: Column(
          children: [
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                labelText: 'Поиск по названию',
                suffixIcon: IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    _filterPositions('');
                  },
                ),
                border: const OutlineInputBorder(),
              ),
              onChanged: _filterPositions,
            ),
            const SizedBox(height: 16),
            Expanded(
              child: ListView.builder(
                itemCount: _filteredPositions.length,
                itemBuilder: (context, index) {
                  final position = _filteredPositions[index];
                  final isCurrent = position.id == widget.currentPositionId;
                  final isOccupied = _occupiedPositionIds.contains(position.id);
                  return ListTile(
                    title: Text(position.title),
                    enabled: !isOccupied || isCurrent,
                    trailing: _isCheckingOccupancy
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : isCurrent
                            ? const Icon(Icons.check_circle,
                                color: Colors.green)
                            : isOccupied
                                ? const Icon(Icons.lock, color: Colors.red)
                                : null,
                    onTap: () {
                      if (!isOccupied || isCurrent) {
                        Navigator.of(context).pop(position.id);
                      }
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Отмена'),
        ),
      ],
    );
  }
}
