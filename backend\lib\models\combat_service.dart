import 'package:json_annotation/json_annotation.dart';

part 'combat_service.g.dart';

@JsonSerializable()
class CombatService {
  final int? id;
  final int employeeId;
  final String operationType;
  final String? location;
  final String startDate;
  final String? endDate;
  final String? notes;
  final String? createdAt;
  final String? updatedAt;

  CombatService({
    this.id,
    required this.employeeId,
    required this.operationType,
    this.location,
    required this.startDate,
    this.endDate,
    this.notes,
    this.createdAt,
    this.updatedAt,
  });

  factory CombatService.fromJson(Map<String, dynamic> json) => _$CombatServiceFromJson(json);

  Map<String, dynamic> toJson() => _$CombatServiceToJson(this);
}
