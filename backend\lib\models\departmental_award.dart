import 'package:json_annotation/json_annotation.dart';

part 'departmental_award.g.dart';

@JsonSerializable()
class DepartmentalAward {
  final int? id;
  final int employeeId;
  final String awardName;
  final String? dateAwarded;
  final int? orderId;
  final String? createdAt;
  final String? updatedAt;

  DepartmentalAward({
    this.id,
    required this.employeeId,
    required this.awardName,
    this.dateAwarded,
    this.orderId,
    this.createdAt,
    this.updatedAt,
  });

  factory DepartmentalAward.fromJson(Map<String, dynamic> json) => _$DepartmentalAwardFromJson(json);

  Map<String, dynamic> toJson() => _$DepartmentalAwardToJson(this);
}
