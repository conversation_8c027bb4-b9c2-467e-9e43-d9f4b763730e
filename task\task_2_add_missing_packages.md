# Подзадача: Установка пакетов во фронтенд-проект

## Описание
Необходимо установить пакеты, указанные в pubspec.yaml, выполнив команду flutter pub get для их установки.

## Анализ текущей ситуации
Проверка файла pubspec.yaml показала, что все необходимые пакеты уже добавлены в файл:
1. flutter_localizations - для поддержки локализации (строка 33-34)
2. provider - для управления состоянием (строка 41)
3. go_router - для навигации (строка 44)

Однако, судя по ошибкам, эти пакеты не были установлены или не были правильно импортированы в коде.

## Выполненные шаги
1. Перешли в директорию frontend
2. Выполнили команду `flutter pub get` для установки всех зависимостей
3. Обнаружили конфликт версий между пакетами form_builder_validators и intl
4. Обновили версию form_builder_validators с ^9.1.0 на ^11.1.2 в pubspec.yaml
5. Повторно выполнили команду `flutter pub get`, которая успешно завершилась

## Результат
- Все необходимые пакеты успешно установлены
- Ошибки, связанные с отсутствующими пакетами, устранены
- Задача выполнена успешно
