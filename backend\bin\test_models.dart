
import 'package:backend/database/database.dart';
import 'package:backend/database/repositories/employee_repository.dart';
import 'package:backend/database/repositories/position_repository.dart';
import 'package:backend/database/repositories/rank_type_repository.dart';
import 'package:backend/database/repositories/military_rank_repository.dart';
import 'package:backend/database/repositories/order_repository.dart';
import 'package:backend/database/repositories/contract_repository.dart';
import 'package:backend/models/employee.dart';
import 'package:backend/models/position.dart';
import 'package:backend/models/rank_type.dart';
import 'package:backend/models/military_rank.dart';
import 'package:backend/models/order.dart';
import 'package:backend/models/contract.dart';
import 'package:backend/utils/logger.dart';

/// Тестовый скрипт для проверки работы моделей и репозиториев
void main() async {
  // Инициализируем логгер
  initLogger();

  print('Инициализация базы данных...');
  await AppDatabase.initialize();

  print('Применение миграций...');
  await AppDatabase.applyMigrations();

  // Создаем репозитории
  final employeeRepository = EmployeeRepository();
  final positionRepository = PositionRepository();
  final rankTypeRepository = RankTypeRepository();
  final militaryRankRepository = MilitaryRankRepository();
  final orderRepository = OrderRepository();
  final contractRepository = ContractRepository();

  // Создаем тестовые данные
  await createTestData(
    employeeRepository,
    positionRepository,
    rankTypeRepository,
    militaryRankRepository,
    orderRepository,
    contractRepository,
  );

  // Получаем данные из базы данных
  await retrieveData(
    employeeRepository,
    positionRepository,
    rankTypeRepository,
    militaryRankRepository,
    orderRepository,
    contractRepository,
  );

  // Обновляем данные
  await updateData(
    employeeRepository,
    positionRepository,
    rankTypeRepository,
  );

  // Удаляем данные
  await deleteData(
    employeeRepository,
    positionRepository,
    rankTypeRepository,
  );

  // Закрываем соединение с базой данных
  AppDatabase.close();
  print('Тестирование завершено.');
}

/// Создание тестовых данных
Future<void> createTestData(
  EmployeeRepository employeeRepository,
  PositionRepository positionRepository,
  RankTypeRepository rankTypeRepository,
  MilitaryRankRepository militaryRankRepository,
  OrderRepository orderRepository,
  ContractRepository contractRepository,
) async {
  print('\n=== Создание тестовых данных ===');

  // Создаем приказ
  final order = Order(
    number: '123/ЛС',
    date: '2023-01-15',
    issuedBy: 'Командир части',
    description: 'О назначении на должность',
  );
  final orderId = orderRepository.create(order);
  print('Создан приказ с ID: $orderId');

  // Создаем сотрудника
  final employee = Employee(
    lastName: 'Иванов',
    firstName: 'Иван',
    middleName: 'Иванович',
    gender: 1,
    birthDate: '1985-05-15',
    placeOfBirth: 'г. Москва',
    nationality: 'Русский',
    personalNumber: 'AB-123456',
    childrenUnder16: 2,
    academicDegree: 'Кандидат наук',
    veteranSince: 2015,
  );
  final employeeId = employeeRepository.create(employee);
  print('Создан сотрудник с ID: $employeeId');

  // Создаем должность
  final position = Position(
    title: 'Начальник отдела',
    department: 'Отдел кадров',
    unitName: 'Штаб',
    womenAllowed: 1,
    militaryRankStaffId: null,
    vus: '123456',
    vusPss: 'А-123',
    positionCodePss: 'Б-456',
    tariffCategory: '16',
    isFlightCrew: 0,
    antiCorruption: 1,
  );
  final positionId = positionRepository.create(position);
  print('Создана должность с ID: $positionId');

  // Создаем тип звания
  final rankType = RankType(
    name: 'Майор',
    serviceYearsRequired: 5,
    category: 'Офицер',
  );
  final rankTypeId = rankTypeRepository.create(rankType);
  print('Создан тип звания с ID: $rankTypeId');

  // Создаем воинское звание
  final militaryRank = MilitaryRank(
    employeeId: employeeId,
    rankTypeId: rankTypeId,
    dateAssigned: '2020-05-10',
    orderId: orderId,
  );
  final militaryRankId = militaryRankRepository.create(militaryRank);
  print('Создано воинское звание с ID: $militaryRankId');

  // Создаем контракт
  final contract = Contract(
    employeeId: employeeId,
    startDate: '2020-01-01',
    endDate: '2025-01-01',
    orderId: orderId,
  );
  final contractId = contractRepository.create(contract);
  print('Создан контракт с ID: $contractId');
}

/// Получение данных из базы данных
Future<void> retrieveData(
  EmployeeRepository employeeRepository,
  PositionRepository positionRepository,
  RankTypeRepository rankTypeRepository,
  MilitaryRankRepository militaryRankRepository,
  OrderRepository orderRepository,
  ContractRepository contractRepository,
) async {
  print('\n=== Получение данных из базы данных ===');

  // Получаем всех сотрудников
  final employees = employeeRepository.getAll();
  print('Получено сотрудников: ${employees.length}');
  for (final employee in employees) {
    print('Сотрудник: ${employee.lastName} ${employee.firstName} ${employee.middleName}');
  }

  // Получаем все должности
  final positions = positionRepository.getAll();
  print('Получено должностей: ${positions.length}');
  for (final position in positions) {
    print('Должность: ${position.title} (${position.department})');
  }

  // Получаем все типы званий
  final rankTypes = rankTypeRepository.getAll();
  print('Получено типов званий: ${rankTypes.length}');
  for (final rankType in rankTypes) {
    print('Тип звания: ${rankType.name} (${rankType.category})');
  }

  // Получаем все воинские звания
  final militaryRanks = militaryRankRepository.getAll();
  print('Получено воинских званий: ${militaryRanks.length}');
  for (final militaryRank in militaryRanks) {
    print('Воинское звание: ID=${militaryRank.id}, Сотрудник ID=${militaryRank.employeeId}, Тип звания ID=${militaryRank.rankTypeId}');
  }

  // Получаем все приказы
  final orders = orderRepository.getAll();
  print('Получено приказов: ${orders.length}');
  for (final order in orders) {
    print('Приказ: ${order.number} от ${order.date}, ${order.issuedBy}');
  }

  // Получаем все контракты
  final contracts = contractRepository.getAll();
  print('Получено контрактов: ${contracts.length}');
  for (final contract in contracts) {
    print('Контракт: Сотрудник ID=${contract.employeeId}, с ${contract.startDate} по ${contract.endDate}');
  }
}

/// Обновление данных
Future<void> updateData(
  EmployeeRepository employeeRepository,
  PositionRepository positionRepository,
  RankTypeRepository rankTypeRepository,
) async {
  print('\n=== Обновление данных ===');

  // Получаем первого сотрудника
  final employees = employeeRepository.getAll();
  if (employees.isNotEmpty) {
    final employee = employees.first;
    final updatedEmployee = Employee(
      id: employee.id,
      lastName: employee.lastName,
      firstName: employee.firstName,
      middleName: employee.middleName,
      gender: employee.gender,
      birthDate: employee.birthDate,
      placeOfBirth: employee.placeOfBirth,
      nationality: employee.nationality,
      personalNumber: employee.personalNumber,
      childrenUnder16: 3, // Изменяем количество детей
      academicDegree: 'Доктор наук', // Изменяем ученую степень
      veteranSince: employee.veteranSince,
    );
    final success = employeeRepository.update(updatedEmployee);
    print('Обновление сотрудника: ${success ? 'успешно' : 'не удалось'}');

    // Проверяем, что данные обновились
    final updatedEmployeeFromDb = employeeRepository.getById(employee.id!);
    print('Обновленный сотрудник: ${updatedEmployeeFromDb?.lastName} ${updatedEmployeeFromDb?.firstName}, Ученая степень: ${updatedEmployeeFromDb?.academicDegree}, Детей до 16 лет: ${updatedEmployeeFromDb?.childrenUnder16}');
  }

  // Получаем первую должность
  final positions = positionRepository.getAll();
  if (positions.isNotEmpty) {
    final position = positions.first;
    final updatedPosition = Position(
      id: position.id,
      title: 'Старший начальник отдела', // Изменяем название должности
      department: position.department,
      unitName: position.unitName,
      womenAllowed: position.womenAllowed,
      militaryRankStaffId: position.militaryRankStaffId,
      vus: position.vus,
      vusPss: position.vusPss,
      positionCodePss: position.positionCodePss,
      tariffCategory: '17', // Изменяем тарифную категорию
      isFlightCrew: position.isFlightCrew,
      antiCorruption: position.antiCorruption,
    );
    final success = positionRepository.update(updatedPosition);
    print('Обновление должности: ${success ? 'успешно' : 'не удалось'}');

    // Проверяем, что данные обновились
    final updatedPositionFromDb = positionRepository.getById(position.id!);
    print('Обновленная должность: ${updatedPositionFromDb?.title}, Тарифная категория: ${updatedPositionFromDb?.tariffCategory}');
  }
}

/// Удаление данных
Future<void> deleteData(
  EmployeeRepository employeeRepository,
  PositionRepository positionRepository,
  RankTypeRepository rankTypeRepository,
) async {
  print('\n=== Удаление данных ===');

  // Получаем все типы званий
  final rankTypes = rankTypeRepository.getAll();
  if (rankTypes.isNotEmpty) {
    final rankType = rankTypes.first;
    final success = rankTypeRepository.delete(rankType.id!);
    print('Удаление типа звания: ${success ? 'успешно' : 'не удалось'}');

    // Проверяем, что данные удалились
    final rankTypesAfterDelete = rankTypeRepository.getAll();
    print('Количество типов званий после удаления: ${rankTypesAfterDelete.length}');
  }

  // Получаем все должности
  final positions = positionRepository.getAll();
  if (positions.isNotEmpty) {
    final position = positions.first;
    final success = positionRepository.delete(position.id!);
    print('Удаление должности: ${success ? 'успешно' : 'не удалось'}');

    // Проверяем, что данные удалились
    final positionsAfterDelete = positionRepository.getAll();
    print('Количество должностей после удаления: ${positionsAfterDelete.length}');
  }

  // Получаем всех сотрудников
  final employees = employeeRepository.getAll();
  if (employees.isNotEmpty) {
    final employee = employees.first;
    final success = employeeRepository.delete(employee.id!);
    print('Удаление сотрудника: ${success ? 'успешно' : 'не удалось'}');

    // Проверяем, что данные удалились
    final employeesAfterDelete = employeeRepository.getAll();
    print('Количество сотрудников после удаления: ${employeesAfterDelete.length}');
  }
}
