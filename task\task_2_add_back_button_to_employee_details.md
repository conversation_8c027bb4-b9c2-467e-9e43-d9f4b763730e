# Подзадача: Добавление кнопки "Назад" на страницу детального просмотра сотрудника

## Описание
Добавить кнопку "Назад" в AppBar на странице детального просмотра сотрудника для возврата к предыдущему экрану.

## Выполнено
- Добавлен элемент `leading` в AppBar компонента `EmployeeDetailsPage`
- Добавлена кнопка с иконкой стрелки назад
- Настроен обработчик нажатия с использованием `context.go('/employees')` для надежного возврата к списку сотрудников

## Изменения в коде
Файл: `frontend/lib/pages/employee/employee_details_page.dart`
```dart
appBar: AppBar(
  leading: IconButton(
    icon: const Icon(Icons.arrow_back),
    onPressed: () {
      // Используем context.go() вместо context.pop() для надежной навигации
      context.go('/employees');
    },
  ),
  title: Text(_employee != null ? _employee!.fullName : 'Детали сотрудника'),
  // ...
),
```

## Статус
Завершено
