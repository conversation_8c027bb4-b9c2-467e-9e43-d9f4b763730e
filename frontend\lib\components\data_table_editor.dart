import 'package:flutter/material.dart';
import 'package:frontend/models/column_metadata.dart';
import 'package:intl/intl.dart';

class DataTableEditor extends StatefulWidget {
  final List<String> columns;
  final List<Map<String, dynamic>> rows;
  final List<ColumnMetadata>? columnsMetadata;
  final Function(String, Map<String, dynamic>) onUpdateRow;
  final Function(Map<String, dynamic>) onAddRow;
  final Function(String) onDeleteRow;

  const DataTableEditor({
    super.key,
    required this.columns,
    required this.rows,
    this.columnsMetadata,
    required this.onUpdateRow,
    required this.onAddRow,
    required this.onDeleteRow,
  });

  @override
  State<DataTableEditor> createState() => _DataTableEditorState();
}

class _DataTableEditorState extends State<DataTableEditor> {
  late List<Map<String, dynamic>> _rows;
  Map<String, dynamic>? _editingRow;
  Map<String, dynamic> _controllers = {};
  Map<String, String?> _errors = {};
  int? _editingRowIndex;
  bool _isAddingRow = false;
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _rows = List.from(widget.rows);
  }

  @override
  void didUpdateWidget(DataTableEditor oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.rows != widget.rows) {
      setState(() {
        _rows = List.from(widget.rows);
        _editingRow = null;
        _editingRowIndex = null;
        _isAddingRow = false;
      });
    }
  }

  @override
  void dispose() {
    _disposeControllers();
    super.dispose();
  }

  void _disposeControllers() {
    _controllers.forEach((_, controller) {
      if (controller is TextEditingController) {
        controller.dispose();
      }
    });
    _controllers = {};
  }

  void _startEditing(int rowIndex) {
    final row = _rows[rowIndex];

    setState(() {
      _editingRow = Map.from(row);
      _editingRowIndex = rowIndex;
      _isAddingRow = false;
      _errors = {};

      // Создаем контроллеры для каждого поля
      _disposeControllers();
      _controllers = {};
      for (final column in widget.columns) {
        if (column != 'id') {
          _createControllerForColumn(column, row[column]);
        }
      }
    });
  }

  void _startAddingRow() {
    setState(() {
      _editingRow = {};
      _editingRowIndex = null;
      _isAddingRow = true;
      _errors = {};

      // Создаем контроллеры для каждого поля
      _disposeControllers();
      _controllers = {};
      for (final column in widget.columns) {
        if (column != 'id') {
          _createControllerForColumn(column, null);
        }
      }
    });
  }

  void _createControllerForColumn(String column, dynamic value) {
    final metadata = _getColumnMetadata(column);

    switch (metadata?.dataType) {
      case ColumnDataType.date:
        // Для даты создаем TextEditingController с форматированной датой
        final dateStr = value is String && value.isNotEmpty
            ? value
            : (value is DateTime ? DateFormat('yyyy-MM-dd').format(value) : '');
        _controllers[column] = TextEditingController(text: dateStr);
        break;
      case ColumnDataType.boolean:
        // Для булевых значений создаем bool
        bool boolValue = false;
        if (value is int) {
          boolValue = value > 0;
        } else if (value is bool) {
          boolValue = value;
        } else if (value is String) {
          boolValue = value.toLowerCase() == 'true' || value == '1';
        }
        _controllers[column] = boolValue;
        break;
      case ColumnDataType.enumType:
        // Для перечислений создаем String с текущим значением
        _controllers[column] = value?.toString() ?? '';
        break;
      default:
        // Для остальных типов создаем TextEditingController
        _controllers[column] = TextEditingController(text: value?.toString() ?? '');
    }
  }

  void _cancelEditing() {
    setState(() {
      _editingRow = null;
      _editingRowIndex = null;
      _isAddingRow = false;
      _errors = {};

      // Очищаем контроллеры
      _disposeControllers();
    });
  }

  void _saveRow() {
    if (_editingRow == null) return;

    // Проверяем валидность формы
    if (_validateForm() == false) {
      return;
    }

    // Собираем данные из контроллеров
    final Map<String, dynamic> rowData = {};
    for (final column in widget.columns) {
      if (column != 'id') {
        final metadata = _getColumnMetadata(column);
        final controller = _controllers[column];

        if (metadata != null) {
          switch (metadata.dataType) {
            case ColumnDataType.integer:
              if (controller is TextEditingController) {
                final value = controller.text;
                rowData[column] = value.isNotEmpty ? int.tryParse(value) : null;
              }
              break;
            case ColumnDataType.real:
              if (controller is TextEditingController) {
                final value = controller.text;
                rowData[column] = value.isNotEmpty ? double.tryParse(value) : null;
              }
              break;
            case ColumnDataType.boolean:
              if (controller is bool) {
                rowData[column] = controller ? 1 : 0;
              }
              break;
            case ColumnDataType.date:
              if (controller is TextEditingController) {
                rowData[column] = controller.text;
              }
              break;
            case ColumnDataType.enumType:
              rowData[column] = controller;
              break;
            default:
              if (controller is TextEditingController) {
                rowData[column] = controller.text;
              }
          }
        } else {
          if (controller is TextEditingController) {
            rowData[column] = controller.text;
          } else {
            rowData[column] = controller;
          }
        }
      }
    }

    if (_isAddingRow) {
      // Добавляем новую строку
      widget.onAddRow(rowData);
    } else if (_editingRowIndex != null) {
      // Обновляем существующую строку
      final rowId = _rows[_editingRowIndex!]['id'].toString();
      widget.onUpdateRow(rowId, rowData);
    }

    _cancelEditing();
  }

  void _deleteRow(int rowIndex) {
    final row = _rows[rowIndex];
    final rowId = row['id'].toString();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Подтверждение'),
        content: const Text('Вы уверены, что хотите удалить эту запись?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Отмена'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              widget.onDeleteRow(rowId);
            },
            child: const Text('Удалить'),
          ),
        ],
      ),
    );
  }

  ColumnMetadata? _getColumnMetadata(String columnName) {
    if (widget.columnsMetadata == null) return null;

    try {
      return widget.columnsMetadata!.firstWhere((metadata) => metadata.name == columnName);
    } catch (e) {
      return null;
    }
  }

  String? _validateField(String column, dynamic value) {
    final metadata = _getColumnMetadata(column);
    if (metadata == null) return null;

    // Проверка на обязательное поле
    if (metadata.isRequired) {
      if (value == null || (value is String && value.isEmpty)) {
        return 'Поле обязательно для заполнения';
      }
    }

    // Проверка типа данных
    if (value != null && value is String && value.isNotEmpty) {
      switch (metadata.dataType) {
        case ColumnDataType.integer:
          final intValue = int.tryParse(value);
          if (intValue == null) {
            return 'Введите целое число';
          }
          if (metadata.minValue != null && intValue < metadata.minValue!) {
            return 'Минимальное значение: ${metadata.minValue}';
          }
          if (metadata.maxValue != null && intValue > metadata.maxValue!) {
            return 'Максимальное значение: ${metadata.maxValue}';
          }
          break;
        case ColumnDataType.real:
          final doubleValue = double.tryParse(value);
          if (doubleValue == null) {
            return 'Введите число';
          }
          if (metadata.minValue != null && doubleValue < metadata.minValue!) {
            return 'Минимальное значение: ${metadata.minValue}';
          }
          if (metadata.maxValue != null && doubleValue > metadata.maxValue!) {
            return 'Максимальное значение: ${metadata.maxValue}';
          }
          break;
        case ColumnDataType.date:
          try {
            DateTime.parse(value);
          } catch (e) {
            return 'Введите дату в формате YYYY-MM-DD';
          }
          break;
        default:
          if (metadata.maxLength != null && value.length > metadata.maxLength!) {
            return 'Максимальная длина: ${metadata.maxLength}';
          }
      }
    }

    return null;
  }

  bool _validateForm() {
    _errors = {};
    bool isValid = true;

    for (final column in widget.columns) {
      if (column != 'id') {
        final controller = _controllers[column];
        String? value;

        if (controller is TextEditingController) {
          value = controller.text;
        } else if (controller is bool) {
          value = controller ? '1' : '0';
        } else if (controller != null) {
          value = controller.toString();
        }

        final error = _validateField(column, value);
        if (error != null) {
          _errors[column] = error;
          isValid = false;
        }
      }
    }

    // Обновляем состояние для отображения ошибок
    if (!isValid) {
      setState(() {});
    }

    return isValid;
  }

  Widget _buildFieldEditor(String column) {
    final metadata = _getColumnMetadata(column);
    final controller = _controllers[column];
    final error = _errors[column];

    switch (metadata?.dataType) {
      case ColumnDataType.boolean:
        if (controller is bool) {
          return SwitchListTile(
            title: Text(column),
            value: controller,
            onChanged: (value) {
              setState(() {
                _controllers[column] = value;
              });
            },
          );
        }
        break;
      case ColumnDataType.date:
        if (controller is TextEditingController) {
          return TextField(
            controller: controller,
            decoration: InputDecoration(
              labelText: column,
              border: const OutlineInputBorder(),
              errorText: error,
              helperText: 'Формат: YYYY-MM-DD',
              suffixIcon: IconButton(
                icon: const Icon(Icons.calendar_today),
                onPressed: () async {
                  final date = await showDatePicker(
                    context: context,
                    initialDate: DateTime.tryParse(controller.text) ?? DateTime.now(),
                    firstDate: DateTime(1900),
                    lastDate: DateTime(2100),
                  );
                  if (date != null) {
                    controller.text = DateFormat('yyyy-MM-dd').format(date);
                  }
                },
              ),
            ),
          );
        }
        break;
      case ColumnDataType.enumType:
        if (metadata?.enumValues != null && metadata!.enumValues!.isNotEmpty) {
          return DropdownButtonFormField<String>(
            value: controller as String? ?? metadata.enumValues!.first,
            decoration: InputDecoration(
              labelText: column,
              border: const OutlineInputBorder(),
              errorText: error,
            ),
            items: metadata.enumValues!.map((value) {
              return DropdownMenuItem<String>(
                value: value,
                child: Text(value),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _controllers[column] = value;
              });
            },
          );
        }
        break;
      default:
        if (controller is TextEditingController) {
          return TextField(
            controller: controller,
            decoration: InputDecoration(
              labelText: column,
              border: const OutlineInputBorder(),
              errorText: error,
            ),
            keyboardType: metadata?.dataType == ColumnDataType.integer ||
                          metadata?.dataType == ColumnDataType.real
                          ? TextInputType.number
                          : TextInputType.text,
          );
        }
    }

    // Fallback для неизвестных типов
    if (controller is TextEditingController) {
      return TextField(
        controller: controller,
        decoration: InputDecoration(
          labelText: column,
          border: const OutlineInputBorder(),
          errorText: error,
        ),
      );
    }

    return const SizedBox.shrink();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Кнопка добавления новой строки
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: ElevatedButton.icon(
            onPressed: _isAddingRow || _editingRowIndex != null
                ? null
                : _startAddingRow,
            icon: const Icon(Icons.add),
            label: const Text('Добавить запись'),
          ),
        ),

        // Форма для добавления/редактирования строки
        if (_editingRow != null)
          Card(
            margin: const EdgeInsets.all(8.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _isAddingRow ? 'Добавление записи' : 'Редактирование записи',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),
                    ...widget.columns.where((column) => column != 'id').map((column) {
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: _buildFieldEditor(column),
                      );
                    }).toList(),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          onPressed: _cancelEditing,
                          child: const Text('Отмена'),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: _saveRow,
                          child: const Text('Сохранить'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),

        // Таблица данных
        Expanded(
          child: SingleChildScrollView(
            scrollDirection: Axis.vertical,
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: [
                  const DataColumn(label: Text('Действия')),
                  ...widget.columns.map((column) => DataColumn(label: Text(column))),
                ],
                rows: _rows.asMap().entries.map((entry) {
                  final index = entry.key;
                  final row = entry.value;

                  return DataRow(
                    cells: [
                      DataCell(
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              icon: const Icon(Icons.edit),
                              onPressed: _isAddingRow || _editingRowIndex != null
                                  ? null
                                  : () => _startEditing(index),
                              tooltip: 'Редактировать',
                            ),
                            IconButton(
                              icon: const Icon(Icons.delete),
                              onPressed: _isAddingRow || _editingRowIndex != null
                                  ? null
                                  : () => _deleteRow(index),
                              tooltip: 'Удалить',
                            ),
                          ],
                        ),
                      ),
                      ...widget.columns.map((column) {
                        final value = row[column];
                        final metadata = _getColumnMetadata(column);

                        if (metadata?.dataType == ColumnDataType.boolean && value != null) {
                          final boolValue = value is int ? value > 0 : (value == true);
                          return DataCell(
                            Icon(boolValue ? Icons.check_circle : Icons.cancel,
                                 color: boolValue ? Colors.green : Colors.red),
                          );
                        } else if (metadata?.dataType == ColumnDataType.date && value != null) {
                          try {
                            final date = DateTime.parse(value.toString());
                            return DataCell(Text(DateFormat('dd.MM.yyyy').format(date)));
                          } catch (e) {
                            return DataCell(Text(value.toString()));
                          }
                        } else {
                          return DataCell(Text(value?.toString() ?? ''));
                        }
                      }),
                    ],
                  );
                }).toList(),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
