# Подзадача: Исправить неиспользуемую локальную переменную `anchor` и подавить `use_build_context_synchronously` в `OrderDetailsPage`

**Статус:** Pending

## Описание
В файле `frontend/lib/pages/order/order_details_page.dart`:
1. Удалить объявление локальной переменной `anchor` и заменить на непосредственный вызов `html.AnchorElement(...).setAttribute(...).click()` без присвоения.
2. Перед всеми использованием `context` внутри асинхронных методов добавить директиву `// ignore: use_build_context_synchronously` для подавления соответствующих предупреждений.

## Критерии приёмки
* Нет предупреждений об неиспользуемой локальной переменной `anchor`.
* Нет предупреждений `use_build_context_synchronously` после запуска `flutter analyze`.
* Функциональность скачивания вложений остается работоспособной. 