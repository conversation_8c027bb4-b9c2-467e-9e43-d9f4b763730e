# Task: Обновление структуры базы данных

**Goal:** Добавить поле position_id в таблицу employees для создания прямой связи с должностью.

## Описание
Необходимо обновить структуру базы данных, добавив поле position_id в таблицу employees, которое будет содержать ссылку на текущую должность сотрудника. Это позволит быстро получать информацию о текущей должности сотрудника без необходимости обращаться к таблице послужного списка.

## Шаги выполнения
1. Создать миграцию для добавления поля position_id в таблицу employees
2. Добавить внешний ключ, связывающий position_id с таблицей positions
3. Обновить существующие данные, заполнив position_id на основе активных записей в таблице service_history

## Технические детали
- Поле position_id должно быть типа INTEGER и может быть NULL (для сотрудников без должности)
- Необходимо добавить внешний ключ FOREIGN KEY (position_id) REFERENCES positions(id)
- При удалении должности, ссылка на неё в таблице employees должна становиться NULL (ON DELETE SET NULL)
- При обновлении ID должности, ссылка должна обновляться (ON UPDATE CASCADE)

## Критерии завершения
- Создана и применена миграция для добавления поля position_id в таблицу employees
- Существующие данные обновлены корректно
- Внешний ключ настроен правильно

## Статус: Pending
