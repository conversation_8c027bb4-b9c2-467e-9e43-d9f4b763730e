# Task: Разработка REST API на Dart/Shelf для работы с данными

**Goal:** Разработать полноценный REST API на Dart/Shelf для работы с данными системы отдела кадров.

**Steps:**

1. ✅ Создать базовую структуру маршрутов в директории `lib/routes/`:
   - ✅ `employee_routes.dart` - маршруты для работы с сотрудниками
   - ✅ `position_routes.dart` - маршруты для работы с должностями
   - ✅ `military_rank_routes.dart` - маршруты для работы с воинскими званиями
   - ✅ `rank_type_routes.dart` - маршруты для работы с типами званий
   - ✅ `order_routes.dart` - маршруты для работы с приказами
   - ✅ `contract_routes.dart` - маршруты для работы с контрактами
   - ✅ `service_history_routes.dart` - маршруты для работы с послужным списком

2. ✅ Создать контроллеры в директории `lib/controllers/`:
   - ✅ `employee_controller.dart` - контроллер для работы с сотрудниками
   - ✅ `position_controller.dart` - контроллер для работы с должностями
   - ✅ `military_rank_controller.dart` - контроллер для работы с воинскими званиями
   - ✅ `rank_type_controller.dart` - контроллер для работы с типами званий
   - ✅ `order_controller.dart` - контроллер для работы с приказами
   - ✅ `contract_controller.dart` - контроллер для работы с контрактами
   - ✅ `service_history_controller.dart` - контроллер для работы с послужным списком
   - ✅ `employee_operations_controller.dart` - контроллер для специфических операций с сотрудниками

3. ✅ Реализовать CRUD-операции для каждой модели:
   - ✅ Create (POST) - создание новой записи
   - ✅ Read (GET) - получение записи или списка записей
   - ✅ Update (PUT) - обновление существующей записи
   - ✅ Delete (DELETE) - удаление записи

4. ✅ Реализовать дополнительные эндпоинты для специфических операций:
   - ✅ Перемещение сотрудника на должность (`POST /api/employees/{id}/move`)
   - ✅ Присвоение звания сотруднику (`POST /api/employees/{id}/assign-rank`)
   - ✅ Получение контрактов сотрудника (`GET /api/contracts/employee/{employeeId}`)
   - ✅ Получение послужного списка сотрудника (`GET /api/service-history/employee/{employeeId}`)

5. ✅ Реализовать валидацию входных данных в `lib/utils/validator.dart`:
   - ✅ Создать класс `Validator` с методами для валидации различных типов данных
   - ✅ Реализовать валидацию для сотрудников
   - ✅ Реализовать валидацию для должностей
   - ✅ Реализовать валидацию для операций с сотрудниками

6. ✅ Обновить основной маршрутизатор в `lib/routes/router.dart`:
   - ✅ Добавить все новые маршруты
   - ✅ Настроить корректные пути для API

**Result:**
- ✅ Полноценный REST API для работы со всеми моделями данных
- ✅ Валидация входных данных для предотвращения ошибок
- ✅ Специфические эндпоинты для сложных операций
- ✅ Единый формат ответов API (success, data, error, message)

**Status:** Completed
