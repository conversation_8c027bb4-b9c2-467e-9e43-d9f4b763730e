/* Roboto Font - Local Version for Offline Mode */

/* Roboto Regular */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('roboto-regular.woff2') format('woff2'),
       url('roboto-regular.woff') format('woff');
}

/* Roboto Medium */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url('roboto-medium.woff2') format('woff2'),
       url('roboto-medium.woff') format('woff');
}

/* Roboto Bold */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url('roboto-bold.woff2') format('woff2'),
       url('roboto-bold.woff') format('woff');
}

/* Roboto Light */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url('roboto-light.woff2') format('woff2'),
       url('roboto-light.woff') format('woff');
}

/* Roboto Thin */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 100;
  font-display: swap;
  src: url('roboto-thin.woff2') format('woff2'),
       url('roboto-thin.woff') format('woff');
}

/* Apply Roboto as default font */
body, html {
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
}
