import 'package:shelf_router/shelf_router.dart';
import 'package:backend/controllers/military_rank_controller.dart';

/// Создает маршрутизатор для работы с воинскими званиями
Router militaryRankRoutes() {
  final controller = MilitaryRankController();
  final router = Router();

  // Получить все воинские звания
  router.get('/', controller.getAll);

  // Получить воинское звание по ID
  router.get('/<id>', controller.getById);

  // Создать новое воинское звание
  router.post('/', controller.create);

  // Обновить воинское звание
  router.put('/<id>', controller.update);

  // Удалить воинское звание
  router.delete('/<id>', controller.delete);

  return router;
}
