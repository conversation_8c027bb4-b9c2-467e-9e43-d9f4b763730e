name: backend
description: A server app using the shelf package and Docker.
version: 1.0.0
# repository: https://github.com/my_org/my_repo

environment:
  sdk: '>=3.3.0 <4.0.0'

dependencies:
  shelf: ^1.4.0
  shelf_router: ^1.1.0
  shelf_static: ^1.1.2
  shelf_cors_headers: ^0.1.5
  sqlite3: ^2.1.0
  sqflite_common_ffi: ^2.3.3
  path: ^1.8.3
  json_annotation: ^4.8.1
  logging: ^1.2.0

  http: any
dev_dependencies:
  lints: ^2.1.1
  test: ^1.24.0
  build_runner: ^2.4.6
  json_serializable: ^6.7.1
