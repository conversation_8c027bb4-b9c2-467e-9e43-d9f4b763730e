-- 1. Сотрудники
CREATE TABLE employees (
    id                   SERIAL PRIMARY KEY,                 -- Уникальный идентификатор сотрудника
    last_name            TEXT NOT NULL,                      -- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>я
    first_name           TEXT NOT NULL,                      -- <PERSON><PERSON>я
    middle_name          TEXT,                               -- Отчество (если есть)
    gender               INTEGER NOT NULL DEFAULT 1,         -- Пол (1 - мужской, 0 - женский)
    birth_date           DATE,                               -- Дата рождения
    place_of_birth       TEXT,                               -- Место рождения (город, страна)
    nationality          TEXT,                               -- Национальность
    personal_number      TEXT UNIQUE,                        -- <PERSON><PERSON><PERSON><PERSON><PERSON>й номер (уникальный)
    children_under_16    INTEGER,                            -- К<PERSON><PERSON>ичество детей до 16 лет
    academic_degree      TEXT,                               -- Учёная степень
    veteran_since        INTEGER                             -- Год получения статуса ветерана (было DATE)
);

-- 2. Типы званий
CREATE TABLE rank_types (
    id                       SERIAL PRIMARY KEY,             -- ИД типа звания
    name                     TEXT NOT NULL,                  -- Название звания
    service_years_required   INTEGER,                        -- Лет выслуги для перехода
    category                 TEXT                            -- Категория (офицер, прапорщик и т.п.)
);

-- 3. Воинские звания сотрудников
CREATE TABLE military_ranks (
    id             SERIAL PRIMARY KEY,                      -- ИД записи звания
    employee_id    INTEGER NOT NULL REFERENCES employees(id) ON DELETE CASCADE ON UPDATE CASCADE,
    rank_type_id   INTEGER NOT NULL REFERENCES rank_types(id) ON DELETE CASCADE ON UPDATE CASCADE,
    date_assigned  DATE,                                     -- Дата присвоения
    order_id       INTEGER REFERENCES orders(id) ON DELETE SET NULL ON UPDATE CASCADE
);

-- 4. Должности
CREATE TABLE positions (
    id                          SERIAL PRIMARY KEY,          -- ИД должности
    title                       TEXT NOT NULL,               -- Наименование должности
    department                  TEXT,                        -- Подразделение
    unit_name                   TEXT,                        -- Условное наименование части
    women_allowed               BOOLEAN,                     -- Комплектуется женщинами
    military_rank_staff_id      INTEGER REFERENCES rank_types(id) ON DELETE SET NULL ON UPDATE CASCADE,
    vus                         TEXT,                        -- ВУС
    vus_pss                     TEXT,                        -- ВУС ПСС
    position_code_pss           TEXT,                        -- Код ПСС
    tariff_category             TEXT,                        -- Тарифный разряд
    is_flight_crew              BOOLEAN,                     -- Летный состав
    anti_corruption             BOOLEAN,                     -- Антикоррупция
    preliminary_candidate_id    INTEGER REFERENCES preliminary_candidates(id) ON DELETE SET NULL ON UPDATE CASCADE,
    reserved_employee_id        INTEGER REFERENCES employees(id) ON DELETE SET NULL ON UPDATE CASCADE
);

-- 5. Кандидаты на должности
CREATE TABLE preliminary_candidates (
    id           SERIAL PRIMARY KEY,                       -- ИД кандидата
    last_name    TEXT NOT NULL,                            -- Фамилия
    first_name   TEXT NOT NULL,                            -- Имя
    middle_name  TEXT,                                     -- Отчество
    rank         TEXT,                                     -- Звание (если есть)
    notes        TEXT                                      -- Заметки
);

-- 6. Иерархия подразделений / должностей
CREATE TABLE hierarchy (
    id           SERIAL PRIMARY KEY,                       -- ИД узла
    parent_id    INTEGER REFERENCES hierarchy(id) ON DELETE SET NULL ON UPDATE CASCADE,
    entity_type  TEXT NOT NULL,                            -- 'department' или 'position'
    entity_id    INTEGER NOT NULL                          -- ID подразделения или должности
);

-- 7. Приказы
CREATE TABLE orders (
    id          SERIAL PRIMARY KEY,                        -- ИД приказа
    number      TEXT NOT NULL,                             -- Номер приказа
    date        DATE NOT NULL,                             -- Дата издания
    issued_by   TEXT NOT NULL,                             -- Кем издан
    description TEXT                                        -- Описание/комментарий
);

-- 8. Вложения к приказам (сканы, документы)
CREATE TABLE order_attachments (
    id          SERIAL PRIMARY KEY,                        -- ИД вложения
    order_id    INTEGER NOT NULL REFERENCES orders(id) ON DELETE CASCADE ON UPDATE CASCADE,
    file_name   TEXT NOT NULL,                             -- Оригинальное имя файла
    data        BYTEA NOT NULL,                            -- Содержимое файла
    uploaded_at TIMESTAMP NOT NULL DEFAULT NOW()           -- Время загрузки
);

-- 9. Контракты
CREATE TABLE contracts (
    id            SERIAL PRIMARY KEY,                      -- ИД контракта
    employee_id   INTEGER NOT NULL REFERENCES employees(id) ON DELETE CASCADE ON UPDATE CASCADE,
    start_date    DATE NOT NULL,                           -- Дата начала
    end_date      DATE,                                    -- Дата окончания
    order_id      INTEGER REFERENCES orders(id) ON DELETE SET NULL ON UPDATE CASCADE
);

-- 10. Аттестации
CREATE TABLE attestations (
    id            SERIAL PRIMARY KEY,                      -- ИД аттестации
    employee_id   INTEGER NOT NULL REFERENCES employees(id) ON DELETE CASCADE ON UPDATE CASCADE,
    date          DATE NOT NULL,                           -- Дата аттестации
    result        TEXT                                     -- Результат
);

-- 11. Образование
CREATE TABLE education_details (
    id               SERIAL PRIMARY KEY,                   -- ИД записи
    employee_id      INTEGER NOT NULL REFERENCES employees(id) ON DELETE CASCADE ON UPDATE CASCADE,
    education_type   TEXT,                                 -- Тип (высшее, среднее и т.п.)
    institution      TEXT NOT NULL,                        -- Учебное заведение
    specialty        TEXT,                                 -- Специальность
    graduation_date  DATE                                  -- Дата окончания
);

-- 12. Зарубежные поездки
CREATE TABLE foreign_travel (
    id            SERIAL PRIMARY KEY,                      -- ИД поездки
    employee_id   INTEGER NOT NULL REFERENCES employees(id) ON DELETE CASCADE ON UPDATE CASCADE,
    country       TEXT NOT NULL,                           -- Страна
    start_date    DATE,                                    -- Дата начала
    end_date      DATE                                     -- Дата окончания
);

-- 13. Государственные награды
CREATE TABLE state_awards (
    id            SERIAL PRIMARY KEY,                      -- ИД награды
    employee_id   INTEGER NOT NULL REFERENCES employees(id) ON DELETE CASCADE ON UPDATE CASCADE,
    award_name    TEXT NOT NULL,                           -- Название
    date_awarded  DATE,                                    -- Дата вручения
    order_id      INTEGER REFERENCES orders(id) ON DELETE SET NULL ON UPDATE CASCADE
);

-- 14. Ведомственные награды
CREATE TABLE departmental_awards (
    id            SERIAL PRIMARY KEY,                      -- ИД ведомственной награды
    employee_id   INTEGER NOT NULL REFERENCES employees(id) ON DELETE CASCADE ON UPDATE CASCADE,
    award_name    TEXT NOT NULL,                           -- Название
    date_awarded  DATE,                                    -- Дата вручения
    order_id      INTEGER REFERENCES orders(id) ON DELETE SET NULL ON UPDATE CASCADE
);

-- 15. Участие в боевых действиях
CREATE TABLE combat_service (
    id             SERIAL PRIMARY KEY,                     -- ИД записи участия
    employee_id    INTEGER NOT NULL REFERENCES employees(id) ON DELETE CASCADE ON UPDATE CASCADE,
    operation_type TEXT NOT NULL,                          -- Тип операции (напр. «САР»)
    location       TEXT,                                   -- Место проведения
    start_date     DATE NOT NULL,                          -- Дата начала
    end_date       DATE,                                   -- Дата окончания
    notes          TEXT                                    -- Примечания
);

-- 16. Семья
CREATE TABLE family (
    id             SERIAL PRIMARY KEY,                     -- ИД родственника
    employee_id    INTEGER NOT NULL REFERENCES employees(id) ON DELETE CASCADE ON UPDATE CASCADE,
    full_name      TEXT NOT NULL,                          -- ФИО родственника
    relationship   TEXT NOT NULL,                          -- Родство
    birth_date     DATE                                     -- Дата рождения
);

-- 17. Послужной список
CREATE TABLE service_history (
    id                       SERIAL PRIMARY KEY,                     -- ИД записи
    employee_id              INTEGER NOT NULL REFERENCES employees(id) ON DELETE CASCADE ON UPDATE CASCADE,
    position_id              INTEGER REFERENCES positions(id) ON DELETE CASCADE ON UPDATE CASCADE, -- Может быть NULL, если должность внешняя
    external_position_title  TEXT,                                   -- Наименование внешней должности, если нет в нашем справочнике
    start_date               DATE NOT NULL,                          -- Дата начала службы в должности
    end_date                 DATE,                                   -- Дата окончания службы в должности
    acceptance_date          DATE,                                   -- Дата приёма дел и должности
    handover_date            DATE,                                   -- Дата сдачи дел и должности
    order_id                 INTEGER REFERENCES orders(id) ON DELETE SET NULL ON UPDATE CASCADE, -- Наш приказ (о зачислении, назначении и т.п.)
    external_order_info      TEXT,                                   -- Информация о внешнем приказе (номер, дата)
    notes                    TEXT                                    -- Примечания
);
