import 'package:shelf_router/shelf_router.dart';
import 'package:backend/controllers/rank_type_controller.dart';

/// Создает маршрутизатор для работы с типами званий
Router rankTypeRoutes() {
  final controller = RankTypeController();
  final router = Router();

  // Получить все типы званий
  router.get('/', controller.getAll);

  // Получить тип звания по ID
  router.get('/<id>', controller.getById);

  // Создать новый тип звания
  router.post('/', controller.create);

  // Обновить тип звания
  router.put('/<id>', controller.update);

  // Удалить тип звания
  router.delete('/<id>', controller.delete);

  return router;
}
