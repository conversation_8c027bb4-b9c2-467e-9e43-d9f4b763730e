# Подзадача: Создание сервиса для работы с API вложений на фронтенде

## Описание
Необходимо создать сервис для работы с API вложений к приказам на фронтенде. Сервис должен обеспечивать все необходимые операции для работы с вложениями: получение, создание, обновление, удаление и скачивание.

## Текущее состояние
- API для работы с вложениями к приказам реализовано на бэкенде
- Отсутствует сервис для работы с API вложений на фронтенде

## Шаги выполнения
1. Создать файл `frontend/lib/services/order_attachment_service.dart`
2. Реализовать класс `OrderAttachmentService` со следующими методами:
   - `Future<List<OrderAttachment>> getAttachments()` - получить все вложения
   - `Future<List<OrderAttachment>> getAttachmentsByOrderId(int orderId)` - получить вложения по ID приказа
   - `Future<OrderAttachment?> getAttachment(int id)` - получить вложение по ID
   - `Future<int> createAttachment(int orderId, String fileName, List<int> fileData)` - создать новое вложение
   - `Future<bool> updateAttachment(OrderAttachment attachment)` - обновить вложение
   - `Future<bool> deleteAttachment(int id)` - удалить вложение
   - `Future<List<int>> downloadAttachment(int id)` - скачать вложение

## Особенности реализации
- Для метода `createAttachment` необходимо реализовать кодирование данных файла в base64 для передачи на сервер
- Для метода `downloadAttachment` необходимо реализовать получение бинарных данных файла

## Ожидаемый результат
Сервис для работы с API вложений к приказам на фронтенде, который обеспечивает все необходимые операции для работы с вложениями.
