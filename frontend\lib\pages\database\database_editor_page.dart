import 'package:flutter/material.dart';
import 'package:frontend/components/app_drawer.dart';
import 'package:frontend/providers/table_provider.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

class DatabaseEditorPage extends StatefulWidget {
  const DatabaseEditorPage({super.key});

  @override
  State<DatabaseEditorPage> createState() => _DatabaseEditorPageState();
}

class _DatabaseEditorPageState extends State<DatabaseEditorPage> {
  @override
  void initState() {
    super.initState();
    // Загружаем список таблиц при инициализации страницы
    Future.microtask(() {
      if (mounted) {
        Provider.of<TableProvider>(context, listen: false).fetchTables();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Редактор базы данных'),
      ),
      drawer: const AppDrawer(),
      body: Consumer<TableProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (provider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Ошибка: ${provider.error}',
                    style: const TextStyle(color: Colors.red),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => provider.fetchTables(),
                    child: const Text('Повторить'),
                  ),
                ],
              ),
            );
          }

          if (provider.tables.isEmpty) {
            return const Center(
              child: Text('Нет доступных таблиц'),
            );
          }

          return ListView.builder(
            itemCount: provider.tables.length,
            itemBuilder: (context, index) {
              final tableName = provider.tables[index];
              return Card(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: ListTile(
                  title: Text(tableName),
                  trailing: const Icon(Icons.arrow_forward),
                  onTap: () {
                    context.go('/database/$tableName');
                  },
                ),
              );
            },
          );
        },
      ),
    );
  }
}
