# Task: Решить проблему несовместимости архитектуры SQLite DLL

**Goal:** Обеспечить загрузку и использование корректной 64-битной версии SQLite DLL для Windows, чтобы избежать ошибок `%1 is not a valid Win32 application`.

**Sub-tasks:**

1.  **[X] `task_1_create_main_task_file.md`**: Создать файл для трекинга этой задачи.
    *   Status: Completed
2.  **[ ] `task_2_update_download_sqlite_dll_script.md`**: Обновить скрипт `download_sqlite_dll.bat` для загрузки 64-битного архива SQLite DLL.
    *   Status: Pending
3.  **[ ] `task_3_remove_old_dll_and_run_script.md`**: Удалить старый `sqlite3.dll` из `dist`, запустить обновленный скрипт и убедиться, что новая DLL скопирована.
    *   Status: Pending
4.  **[ ] `task_4_test_hr_system.md`**: Проверить запуск HR system и убедиться, что ошибки загрузки DLL исчезли.
    *   Status: Pending

**Desired Outcome:**
*   В папке `dist` присутствует корректная 64-битная `sqlite3.dll`.
*   HR system запускается без ошибок загрузки `sqlite3.dll` (error code 193). 