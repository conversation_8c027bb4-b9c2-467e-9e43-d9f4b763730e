import 'package:flutter/material.dart';
import 'package:frontend/components/app_drawer.dart';
import 'package:frontend/providers/employee_provider.dart';
import 'package:frontend/providers/position_provider.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

class DashboardPage extends StatefulWidget {
  const DashboardPage({super.key});

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

class _DashboardPageState extends State<DashboardPage> {
  @override
  void initState() {
    super.initState();
    // Загружаем данные при инициализации страницы
    Future.microtask(() {
      if (mounted) {
        Provider.of<EmployeeProvider>(context, listen: false).fetchEmployees();
        Provider.of<PositionProvider>(context, listen: false).fetchPositions();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final employeeProvider = Provider.of<EmployeeProvider>(context);
    final positionProvider = Provider.of<PositionProvider>(context);

    return Scaffold(
      appBar: AppBar(title: const Text('HR System')),
      drawer: const AppDrawer(),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Панель управления',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: _buildCard(
                    context,
                    title: 'Сотрудники',
                    count: employeeProvider.employees.length,
                    icon: Icons.people,
                    color: Colors.blue,
                    onTap: () => context.go('/employees'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildCard(
                    context,
                    title: 'Должности',
                    count: positionProvider.positions.length,
                    icon: Icons.work,
                    color: Colors.green,
                    onTap: () => context.go('/positions'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            const Text(
              'Последние сотрудники',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            employeeProvider.isLoading
                ? const Center(child: CircularProgressIndicator())
                : employeeProvider.employees.isEmpty
                ? const Center(child: Text('Нет данных о сотрудниках'))
                : ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount:
                      employeeProvider.employees.length > 5
                          ? 5
                          : employeeProvider.employees.length,
                  itemBuilder: (context, index) {
                    final employee = employeeProvider.employees[index];
                    return ListTile(
                      title: Text(employee.fullName),
                      subtitle: Text(employee.personalNumber ?? ''),
                      onTap: () => context.go('/employees/${employee.id}'),
                    );
                  },
                ),
            const SizedBox(height: 16),
            const Text(
              'Последние должности',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            positionProvider.isLoading
                ? const Center(child: CircularProgressIndicator())
                : positionProvider.positions.isEmpty
                ? const Center(child: Text('Нет данных о должностях'))
                : ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount:
                      positionProvider.positions.length > 5
                          ? 5
                          : positionProvider.positions.length,
                  itemBuilder: (context, index) {
                    final position = positionProvider.positions[index];
                    return ListTile(
                      title: Text(position.title),
                      subtitle: Text(position.department ?? ''),
                      onTap: () => context.go('/positions/${position.id}'),
                    );
                  },
                ),
          ],
        ),
      ),
    );
  }

  Widget _buildCard(
    BuildContext context, {
    required String title,
    required int count,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Icon(icon, color: color, size: 32),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                count.toString(),
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 8),
              Text('Всего', style: TextStyle(color: Colors.grey[600])),
            ],
          ),
        ),
      ),
    );
  }
}
