@echo off
echo ===== Building production version of HR system =====

echo.
echo 1. Installing backend dependencies...
cd backend
call dart pub get
if %ERRORLEVEL% neq 0 (
    echo Error installing backend dependencies!
    exit /b %ERRORLEVEL%
)
cd ..

echo.
echo 2. Installing frontend dependencies...
cd frontend
call flutter pub get
if %ERRORLEVEL% neq 0 (
    echo Error installing frontend dependencies!
    exit /b %ERRORLEVEL%
)

echo.
echo 3. Building web version of frontend for offline mode...
call flutter build web --release --web-renderer canvaskit --dart-define=FLUTTER_WEB_CANVASKIT_URL=/canvaskit/
if %ERRORLEVEL% neq 0 (
    echo Error building frontend!
    exit /b %ERRORLEVEL%
)
cd ..

echo.
echo 4. Copying frontend build files to backend directory...
if not exist backend\web mkdir backend\web
xcopy /E /Y frontend\build\web backend\web\
if %ERRORLEVEL% neq 0 (
    echo Error copying frontend files!
    exit /b %ERRORLEVEL%
)

echo.
echo 4.1. Copying offline configuration files...
copy frontend\web\flutter_config.js backend\web\flutter_config.js
if %ERRORLEVEL% neq 0 (
    echo Warning: Could not copy flutter_config.js
)
copy frontend\web\offline_enhancer.js backend\web\offline_enhancer.js
if %ERRORLEVEL% neq 0 (
    echo Warning: Could not copy offline_enhancer.js
)

echo.
echo 4.2. Copying local fonts...
if not exist backend\web\fonts mkdir backend\web\fonts
xcopy /E /Y frontend\web\fonts backend\web\fonts\
if %ERRORLEVEL% neq 0 (
    echo Warning: Could not copy fonts
)

echo.
echo 5. Creating directories for database and migrations...
if not exist backend\db mkdir backend\db
if not exist backend\migrations mkdir backend\migrations

echo.
echo 6. Compiling backend to executable...
cd backend
call dart compile exe bin/server.dart -o bin/server.exe
if %ERRORLEVEL% neq 0 (
    echo Error compiling backend!
    exit /b %ERRORLEVEL%
)

echo.
echo 7. Creating directory for production build...
cd ..
if not exist dist mkdir dist
if not exist dist\db mkdir dist\db
if not exist dist\migrations mkdir dist\migrations

echo.
echo 8. Copying necessary files to production directory...
copy backend\bin\server.exe dist\hr_system.exe
if %ERRORLEVEL% neq 0 (
    echo Error copying executable file!
    exit /b %ERRORLEVEL%
)

xcopy /E /Y backend\web dist\web\
if %ERRORLEVEL% neq 0 (
    echo Error copying frontend files to production build!
    exit /b %ERRORLEVEL%
)

echo.
echo 8.1. Copying offline configuration to production build...
copy frontend\web\flutter_config.js dist\web\flutter_config.js
if %ERRORLEVEL% neq 0 (
    echo Warning: Could not copy flutter_config.js to production build
)
copy frontend\web\offline_enhancer.js dist\web\offline_enhancer.js
if %ERRORLEVEL% neq 0 (
    echo Warning: Could not copy offline_enhancer.js to production build
)

echo.
echo 8.2. Copying local fonts to production build...
if not exist dist\web\fonts mkdir dist\web\fonts
xcopy /E /Y frontend\web\fonts dist\web\fonts\
if %ERRORLEVEL% neq 0 (
    echo Warning: Could not copy fonts to production build
)

xcopy /E /Y backend\migrations dist\migrations\
if %ERRORLEVEL% neq 0 (
    echo Error copying migration files!
    exit /b %ERRORLEVEL%
)

echo.
echo 8.3. Checking for SQLite DLL...
set SQLITE_DLL_FOUND=0

echo Checking for SQLite DLL...
echo Checking in system path...
where sqlite3.dll >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo SQLite DLL found in system path.
    for /f "tokens=*" %%i in ('where sqlite3.dll') do set SQLITE_DLL=%%i
    set SQLITE_DLL_FOUND=1
) else (
    echo SQLite DLL not found in system path.
    echo Checking in Dart cache...

    set DART_CACHE_DIR=%LOCALAPPDATA%\Pub\Cache\hosted\pub.dev

    if exist "%DART_CACHE_DIR%\sqlite3-*" (
        for /d %%d in ("%DART_CACHE_DIR%\sqlite3-*") do (
            if exist "%%d\lib\src\ffi\*.dll" (
                for %%f in ("%%d\lib\src\ffi\*.dll") do (
                    if "%%~nxf"=="sqlite3.dll" (
                        set SQLITE_DLL=%%f
                        set SQLITE_DLL_FOUND=1
                    )
                )
            )
        )
    )
)

if %SQLITE_DLL_FOUND% equ 1 (
    echo Copying SQLite DLL from: %SQLITE_DLL%
    copy "%SQLITE_DLL%" dist\sqlite3.dll
    if %ERRORLEVEL% neq 0 (
        echo Warning: Failed to copy SQLite DLL.
        echo The application may not work without sqlite3.dll.
        echo Please run fix_sqlite_dll.bat after the build completes.
    ) else (
        echo SQLite DLL copied successfully.
    )
) else (
    echo Warning: SQLite DLL not found.
    echo Attempting to download 64-bit SQLite DLL...

    echo Creating temporary directory...
    if not exist temp mkdir temp
    cd temp

    echo Downloading 64-bit SQLite DLL from sqlite.org...
    powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://www.sqlite.org/2023/sqlite-dll-win64-x64-3430200.zip' -OutFile 'sqlite.zip'}"

    if exist sqlite.zip (
        echo Extracting SQLite DLL...
        powershell -Command "& {Expand-Archive -Path 'sqlite.zip' -DestinationPath '.' -Force}"

        if exist sqlite3.dll (
            echo Copying 64-bit SQLite DLL to dist directory...
            copy sqlite3.dll ..\dist\sqlite3.dll
            echo SQLite DLL downloaded and copied successfully.
        ) else (
            echo Error: Failed to extract SQLite DLL.
            echo The application may not work without sqlite3.dll.
            echo Please download it manually from https://www.sqlite.org/download.html
        )
    ) else (
        echo Error: Failed to download SQLite DLL.
        echo The application may not work without sqlite3.dll.
        echo Please download it manually from https://www.sqlite.org/download.html
    )

    cd ..
    rmdir /s /q temp
)

echo.
echo 9. Creating README.txt with instructions...
echo HR System - Production Build > dist\README.txt
echo. >> dist\README.txt
echo To start the application, follow these steps: >> dist\README.txt
echo 1. Run hr_system.exe >> dist\README.txt
echo 2. Open http://localhost:8080 in your browser >> dist\README.txt
echo. >> dist\README.txt
echo The database will be automatically created in the db directory on first launch. >> dist\README.txt
echo All data will be stored in this directory. >> dist\README.txt
echo. >> dist\README.txt
echo IMPORTANT: >> dist\README.txt
echo - Make sure sqlite3.dll is present in the same directory as hr_system.exe >> dist\README.txt
echo - If you encounter any issues with SQLite, download the 64-bit version from: >> dist\README.txt
echo   https://www.sqlite.org/download.html (sqlite-dll-win64-x64-*.zip) >> dist\README.txt

echo.
echo 10. Creating startup script...
echo @echo off > dist\start_hr_system.bat
echo echo Starting HR system... >> dist\start_hr_system.bat
echo echo. >> dist\start_hr_system.bat
echo echo Application will be available at: http://localhost:8080 >> dist\start_hr_system.bat
echo echo. >> dist\start_hr_system.bat
echo echo To stop the server, press Ctrl+C >> dist\start_hr_system.bat
echo hr_system.exe >> dist\start_hr_system.bat

echo.
echo ===== Production build completed successfully! =====
echo.
echo Production version is located in the dist directory
echo To run the application, execute dist\start_hr_system.bat or run dist\hr_system.exe
echo.
echo Press any key to exit...
pause
