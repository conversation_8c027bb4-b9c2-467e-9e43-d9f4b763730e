# Task: Обновление модели Position во frontend

**Goal:** Обновить модель Position во frontend для соответствия с backend моделью, добавив недостающие поля и методы.

## Анализ проблемы

При сравнении моделей Position во frontend и backend были выявлены следующие различия:

1. В backend модели есть поле `requiredEducationLevel`, которое отсутствует во frontend модели.
2. В backend модели есть метод `requiredEducationLevelText`, который отсутствует во frontend модели.
3. В backend модели используется аннотация `@JsonSerializable()` и автогенерация методов `fromJson` и `to<PERSON><PERSON>`, в то время как во frontend модели эти методы написаны вручную.

Эти различия могут приводить к проблемам при обмене данными между frontend и backend, особенно если backend возвращает поле `requiredEducationLevel`, которое frontend не ожидает.

## Шаги выполнения

1. Добавить поле `requiredEducationLevel` в модель Position во frontend:
   ```dart
   final int? requiredEducationLevel;
   ```

2. Обновить конструктор для включения нового поля:
   ```dart
   Position({
     // существующие поля
     this.requiredEducationLevel,
     // остальные поля
   });
   ```

3. Обновить метод `fromJson` для обработки нового поля:
   ```dart
   requiredEducationLevel: json['requiredEducationLevel'],
   ```

4. Обновить метод `toJson` для включения нового поля:
   ```dart
   'requiredEducationLevel': requiredEducationLevel,
   ```

5. Добавить метод `requiredEducationLevelText` для соответствия с backend моделью:
   ```dart
   String get requiredEducationLevelText {
     switch (requiredEducationLevel) {
       case 0:
         return 'Не требуется';
       case 1:
         return 'Основное общее образование';
       case 2:
         return 'Среднее общее образование';
       case 3:
         return 'Среднее профессиональное образование';
       case 4:
         return 'Высшее образование';
       default:
         return 'Не указано';
     }
   }
   ```

6. Обновить метод `copyWith` для включения нового поля:
   ```dart
   Position copyWith({
     // существующие поля
     int? requiredEducationLevel,
     // остальные поля
   }) {
     return Position(
       // существующие поля
       requiredEducationLevel: requiredEducationLevel ?? this.requiredEducationLevel,
       // остальные поля
     );
   }
   ```

## Ожидаемый результат

* Обновленная модель Position во frontend, которая соответствует backend модели
* Корректная обработка поля `requiredEducationLevel` при получении данных с backend
* Отсутствие ошибок при работе с моделью Position
