# Manual Fix for SQLite DLL Issue

If the automatic scripts (`fix_sqlite_dll.bat` or `download_sqlite_dll.bat`) don't work, you can manually fix the SQLite DLL issue by following these steps:

## Option 1: Download SQLite DLL from the official website

1. Visit the SQLite download page: https://www.sqlite.org/download.html
2. Download the "Precompiled Binaries for Windows" zip file (e.g., `sqlite-dll-win32-x86-3430200.zip`)
3. Extract the zip file
4. Copy the `sqlite3.dll` file to the `dist` directory of your HR system

## Option 2: Find SQLite DLL in Dart cache

If you have Dart SDK installed, the SQLite DLL might be in the Dart cache:

1. Navigate to `%LOCALAPPDATA%\Pub\Cache\hosted\pub.dev\sqlite3-*`
2. Look for the `sqlite3.dll` file in the subdirectories
3. Copy the `sqlite3.dll` file to the `dist` directory of your HR system

## Option 3: Install SQLite and use its DLL

1. Download SQLite tools from https://www.sqlite.org/download.html
2. Install SQLite
3. Find the `sqlite3.dll` file in the installation directory
4. Copy the `sqlite3.dll` file to the `dist` directory of your HR system

## After copying the DLL

Once you've copied the `sqlite3.dll` file to the `dist` directory, you should be able to run the HR system using:

```
dist\start_hr_system.bat
```

or directly:

```
cd dist
hr_system.exe
```

## Verifying the fix

To verify that the fix worked:

1. Run the HR system
2. Check if the database directory (`db`) is created
3. Check if you can access the application at http://localhost:8080
