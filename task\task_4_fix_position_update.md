# Подзадача: Исправление обновления должности при редактировании сотрудника

**Статус:** Выполнено

## Описание проблемы
При редактировании сотрудника и изменении его должности не создается новая запись в послужном списке. Это происходит потому, что в методе `_saveEmployee()` создание записи в послужном списке происходит только при создании нового сотрудника, но не при обновлении существующего.

## Решение
1. Модифицировать метод `_saveEmployee()` для проверки, изменилась ли должность сотрудника
2. Если должность изменилась, создавать новую запись в послужном списке
3. Закрывать предыдущую активную запись в послужном списке (устанавливать дату окончания)

## План реализации
1. Сохранять предыдущую должность сотрудника в переменной `_currentPositionId`
2. В методе `_saveEmployee()` сравнивать `_currentPositionId` с выбранной должностью из формы
3. Если должность изменилась, закрывать предыдущую запись и создавать новую
