import 'dart:convert';
import 'package:shelf/shelf.dart';
import 'package:backend/database/repositories/contract_repository.dart';
import 'package:backend/models/contract.dart';
import 'package:logging/logging.dart';

class ContractController {
  static final Logger _logger = Logger('ContractController');
  final ContractRepository _repository = ContractRepository();

  /// Получить все контракты
  Response getAll(Request request) {
    _logger.info('Getting all contracts');
    
    try {
      final contracts = _repository.getAll();
      
      return Response.ok(
        jsonEncode({
          'success': true,
          'data': contracts.map((e) => e.toJson()).toList(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error getting all contracts: $e\n$stackTrace');
      
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Получить контракты сотрудника
  Response getByEmployeeId(Request request, String employeeId) {
    _logger.info('Getting contracts for employee ID: $employeeId');
    
    try {
      final empId = int.parse(employeeId);
      final contracts = _repository.getByEmployeeId(empId);
      
      return Response.ok(
        jsonEncode({
          'success': true,
          'data': contracts.map((e) => e.toJson()).toList(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error getting contracts for employee: $e\n$stackTrace');
      
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Получить контракт по ID
  Response getById(Request request, String id) {
    _logger.info('Getting contract by ID: $id');
    
    try {
      final contractId = int.parse(id);
      final contract = _repository.getById(contractId);
      
      if (contract == null) {
        return Response.notFound(
          jsonEncode({
            'success': false,
            'error': 'Not Found',
            'message': 'Contract with ID $id not found',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }
      
      return Response.ok(
        jsonEncode({
          'success': true,
          'data': contract.toJson(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error getting contract by ID: $e\n$stackTrace');
      
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Создать новый контракт
  Future<Response> create(Request request) async {
    _logger.info('Creating new contract');
    
    try {
      final jsonBody = await request.readAsString();
      final Map<String, dynamic> data = jsonDecode(jsonBody);
      
      final contract = Contract(
        employeeId: data['employeeId'],
        startDate: data['startDate'],
        endDate: data['endDate'],
        orderId: data['orderId'],
      );
      
      final id = _repository.create(contract);
      
      return Response.ok(
        jsonEncode({
          'success': true,
          'data': {'id': id},
          'message': 'Contract created successfully',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error creating contract: $e\n$stackTrace');
      
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Обновить контракт
  Future<Response> update(Request request, String id) async {
    _logger.info('Updating contract with ID: $id');
    
    try {
      final contractId = int.parse(id);
      final existingContract = _repository.getById(contractId);
      
      if (existingContract == null) {
        return Response.notFound(
          jsonEncode({
            'success': false,
            'error': 'Not Found',
            'message': 'Contract with ID $id not found',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }
      
      final jsonBody = await request.readAsString();
      final Map<String, dynamic> data = jsonDecode(jsonBody);
      
      final contract = Contract(
        id: contractId,
        employeeId: data['employeeId'] ?? existingContract.employeeId,
        startDate: data['startDate'] ?? existingContract.startDate,
        endDate: data['endDate'] ?? existingContract.endDate,
        orderId: data['orderId'] ?? existingContract.orderId,
      );
      
      final success = _repository.update(contract);
      
      if (!success) {
        return Response.internalServerError(
          body: jsonEncode({
            'success': false,
            'error': 'Internal Server Error',
            'message': 'Failed to update contract',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }
      
      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'Contract updated successfully',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error updating contract: $e\n$stackTrace');
      
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Удалить контракт
  Response delete(Request request, String id) {
    _logger.info('Deleting contract with ID: $id');
    
    try {
      final contractId = int.parse(id);
      final existingContract = _repository.getById(contractId);
      
      if (existingContract == null) {
        return Response.notFound(
          jsonEncode({
            'success': false,
            'error': 'Not Found',
            'message': 'Contract with ID $id not found',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }
      
      final success = _repository.delete(contractId);
      
      if (!success) {
        return Response.internalServerError(
          body: jsonEncode({
            'success': false,
            'error': 'Internal Server Error',
            'message': 'Failed to delete contract',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }
      
      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'Contract deleted successfully',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error deleting contract: $e\n$stackTrace');
      
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }
}
