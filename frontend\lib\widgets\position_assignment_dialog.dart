import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:frontend/models/employee.dart';
import 'package:frontend/models/position.dart';
import 'package:frontend/models/service_history.dart';
import 'package:frontend/providers/position_provider.dart';
import 'package:frontend/services/service_history_service.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

class PositionAssignmentDialog extends StatefulWidget {
  final Employee employee;
  final int? currentPositionId;
  final int? preselectedPositionId;

  const PositionAssignmentDialog({
    Key? key,
    required this.employee,
    this.currentPositionId,
    this.preselectedPositionId,
  }) : super(key: key);

  @override
  State<PositionAssignmentDialog> createState() =>
      _PositionAssignmentDialogState();
}

class _PositionAssignmentDialogState extends State<PositionAssignmentDialog> {
  final _formKey = GlobalKey<FormBuilderState>();
  final _serviceHistoryService = ServiceHistoryService();

  bool _isLoading = false;
  String? _error;
  List<Position> _positions = [];
  List<Position> _filteredPositions = [];

  @override
  void initState() {
    super.initState();
    // Используем addPostFrameCallback для отложенного вызова загрузки данных
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadPositions();
    });
  }

  Future<void> _loadPositions() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await Provider.of<PositionProvider>(context, listen: false)
          .fetchPositions();

      if (!mounted) return;

      final positions =
          Provider.of<PositionProvider>(context, listen: false).positions;

      setState(() {
        _positions = positions;
        _filteredPositions = _positions;
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _filterPositions(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredPositions = _positions;
      } else {
        _filteredPositions = _positions.where((position) {
          return position.title.toLowerCase().contains(query.toLowerCase());
        }).toList();
      }
    });
  }

  Future<void> _assignPosition() async {
    if (_formKey.currentState?.saveAndValidate() ?? false) {
      final formData = _formKey.currentState!.value;

      setState(() {
        _isLoading = true;
        _error = null;
      });

      try {
        // Создаем запись в послужном списке
        final serviceHistory = ServiceHistory(
          employeeId: widget.employee.id!,
          positionId: formData['positionId'],
          startDate: DateFormat('yyyy-MM-dd').format(formData['startDate']),
          acceptanceDate: formData['acceptanceDate'] != null
              ? DateFormat('yyyy-MM-dd').format(formData['acceptanceDate'])
              : null,
          handoverDate: formData['handoverDate'] != null
              ? DateFormat('yyyy-MM-dd').format(formData['handoverDate'])
              : null,
          orderId: formData['orderId'],
          externalOrderInfo: formData['externalOrderInfo'],
          notes: formData['notes'],
        );

        // Отправляем запрос на сервер
        await _serviceHistoryService.moveEmployeeToPosition(
          widget.employee.id!,
          serviceHistory,
        );

        setState(() {
          _isLoading = false;
        });

        if (mounted) {
          Navigator.of(context).pop(true);
        }
      } catch (e) {
        setState(() {
          _isLoading = false;
          _error = e.toString();
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Назначение на должность'),
      content: SizedBox(
        width: 500,
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                child: FormBuilder(
                  key: _formKey,
                  initialValue: widget.preselectedPositionId != null
                      ? {'positionId': widget.preselectedPositionId}
                      : {},
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (_error != null)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 16.0),
                          child: Text(
                            _error!,
                            style: const TextStyle(color: Colors.red),
                          ),
                        ),

                      // Поле поиска должности
                      TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'Поиск должности',
                          prefixIcon: Icon(Icons.search),
                          border: OutlineInputBorder(),
                        ),
                        onChanged: _filterPositions,
                      ),

                      const SizedBox(height: 16),

                      // Выбор должности
                      FormBuilderDropdown<int>(
                        name: 'positionId',
                        decoration: const InputDecoration(
                          labelText: 'Должность',
                          border: OutlineInputBorder(),
                        ),
                        validator: FormBuilderValidators.required(
                          errorText: 'Выберите должность',
                        ),
                        items: _filteredPositions.map((position) {
                          return DropdownMenuItem(
                            value: position.id,
                            child: Text(position.title),
                          );
                        }).toList(),
                      ),

                      const SizedBox(height: 16),

                      // Дата начала работы в новой должности
                      FormBuilderDateTimePicker(
                        name: 'startDate',
                        inputType: InputType.date,
                        format: DateFormat('dd.MM.yyyy'),
                        decoration: const InputDecoration(
                          labelText: 'Дата начала работы в должности',
                          border: OutlineInputBorder(),
                        ),
                        validator: FormBuilderValidators.required(
                          errorText: 'Укажите дату начала работы',
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Дата принятия дел и должности
                      FormBuilderDateTimePicker(
                        name: 'acceptanceDate',
                        inputType: InputType.date,
                        format: DateFormat('dd.MM.yyyy'),
                        decoration: const InputDecoration(
                          labelText: 'Дата принятия дел и должности',
                          border: OutlineInputBorder(),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Дата сдачи дел по предыдущей должности
                      FormBuilderDateTimePicker(
                        name: 'handoverDate',
                        inputType: InputType.date,
                        format: DateFormat('dd.MM.yyyy'),
                        decoration: const InputDecoration(
                          labelText: 'Дата сдачи дел по предыдущей должности',
                          border: OutlineInputBorder(),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Номер приказа
                      FormBuilderTextField(
                        name: 'orderId',
                        decoration: const InputDecoration(
                          labelText: 'ID приказа о назначении',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                      ),

                      const SizedBox(height: 16),

                      // Информация о приказе
                      FormBuilderTextField(
                        name: 'externalOrderInfo',
                        decoration: const InputDecoration(
                          labelText: 'Номер и дата приказа',
                          border: OutlineInputBorder(),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Примечания
                      FormBuilderTextField(
                        name: 'notes',
                        decoration: const InputDecoration(
                          labelText: 'Примечания',
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 3,
                      ),
                    ],
                  ),
                ),
              ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(false),
          child: const Text('Отмена'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _assignPosition,
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Назначить'),
        ),
      ],
    );
  }
}
