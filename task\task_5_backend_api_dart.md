# Task: Разработка REST API на Dart/Shelf для работы с данными

**Goal:** Разработать REST API на Dart/Shelf для взаимодействия с данными системы отдела кадров.

**Steps:**

1. Создать базовую структуру маршрутов в директории `lib/routes/`:
   - `employee_routes.dart` - маршруты для работы с сотрудниками
   - `position_routes.dart` - маршруты для работы с должностями
   - `rank_routes.dart` - маршруты для работы с званиями
   - `order_routes.dart` - маршруты для работы с приказами
   - Другие необходимые маршруты
2. Создать контроллеры в директории `lib/controllers/`:
   - `employee_controller.dart` - контроллер для работы с сотрудниками
   - `position_controller.dart` - контроллер для работы с должностями
   - `rank_controller.dart` - контроллер для работы с званиями
   - `order_controller.dart` - контроллер для работы с приказами
   - Другие необходимые контроллеры
3. Реализовать CRUD-операции для каждой модели:
   - Create (POST) - создание новой записи
   - Read (GET) - получение записи или списка записей
   - Update (PUT/PATCH) - обновление существующей записи
   - Delete (DELETE) - удаление записи
4. Реализовать дополнительные эндпоинты для специфических операций:
   - Назначение сотрудника на должность
   - Присвоение звания сотруднику
   - Создание приказа с вложениями
   - Другие необходимые операции
5. Реализовать валидацию входных данных в `lib/utils/validator.dart`
6. Реализовать обработку ошибок в `lib/middleware/error_middleware.dart`
7. Настроить логирование запросов в `lib/middleware/logging_middleware.dart`
8. Объединить все маршруты в `lib/routes/router.dart`

**Expected Result:**
- Полнофункциональное REST API для работы с данными системы отдела кадров
- Реализованные CRUD-операции для всех моделей
- Дополнительные эндпоинты для специфических операций
- Валидация входных данных и обработка ошибок
- Логирование запросов
- Объединенный маршрутизатор для всех эндпоинтов

**Status:** Pending
