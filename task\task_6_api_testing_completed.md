# Task: Тестирование REST API на Dart/Shelf

**Goal:** Разработать и выполнить тесты для REST API на Dart/Shelf для проверки работоспособности API перед разработкой пользовательского интерфейса.

**Steps:**

1. ✅ Создать тестовый скрипт для проверки доступности API:
   - ✅ `bin/test_api.dart` - скрипт для проверки доступности API и основных функций

2. ✅ Создать тесты для основных моделей:
   - ✅ `test/api/employee_api_test.dart` - тесты для API сотрудников
   - ✅ `test/api/position_api_test.dart` - тесты для API должностей
   - ✅ `test/api/employee_operations_test.dart` - тесты для специфических операций с сотрудниками

3. ✅ Реализовать тесты для CRUD-операций:
   - ✅ Create (POST) - тесты для создания новых записей
   - ✅ Read (GET) - тесты для получения записей
   - ✅ Update (PUT) - тесты для обновления записей
   - ✅ Delete (DELETE) - тесты для удаления записей

4. ✅ Реализовать тесты для валидации данных:
   - ✅ Тесты для проверки обязательных полей
   - ✅ Тесты для проверки типов данных
   - ✅ Тесты для проверки форматов данных

5. ✅ Реализовать тесты для специфических операций:
   - ✅ Тесты для перемещения сотрудника на должность
   - ✅ Тесты для получения послужного списка сотрудника

6. ✅ Создать скрипт для запуска всех тестов:
   - ✅ `bin/run_tests.dart` - скрипт для запуска всех тестов API

7. ✅ Создать документацию по тестированию API:
   - ✅ `README_API_TESTING.md` - инструкции по тестированию API

**Result:**
- ✅ Полный набор тестов для проверки работоспособности API
- ✅ Скрипты для автоматического запуска тестов
- ✅ Документация по тестированию API

**Status:** Completed
