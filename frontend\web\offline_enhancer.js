// Offline Mode Enhancer for Flutter Web
// This script enhances offline capabilities and prevents external resource loading

(function() {
  'use strict';

  console.log('Loading offline enhancer...');

  // Block external resource loading
  const blockedDomains = [
    'unpkg.com',
    'cdn.jsdelivr.net',
    'gstatic.com',
    'googleapis.com',
    'fonts.googleapis.com',
    'fonts.gstatic.com'
  ];

  // Helper function for compatibility with older browsers
  function stringIncludes(str, searchString) {
    if (typeof str !== 'string') {
      str = String(str);
    }
    return str.indexOf(searchString) !== -1;
  }

  // Override XMLHttpRequest to block external requests
  const originalXHROpen = XMLHttpRequest.prototype.open;
  XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
    const urlString = url.toString();

    // Check if URL is external and blocked
    for (let i = 0; i < blockedDomains.length; i++) {
      const domain = blockedDomains[i];
      if (stringIncludes(urlString, domain)) {
        console.warn('Blocked external XHR request:', urlString);
        // Create a fake failed request
        setTimeout(() => {
          if (this.onerror) {
            this.onerror(new Error('External requests blocked for offline mode'));
          }
        }, 0);
        return;
      }
    }

    return originalXHROpen.call(this, method, url, async, user, password);
  };

  // Override fetch to redirect CanvasKit requests to local files
  const originalFetch = window.fetch;
  window.fetch = function(resource, init) {
    const url = typeof resource === 'string' ? resource : resource.url;

    // Block external requests to blocked domains
    for (let i = 0; i < blockedDomains.length; i++) {
      const domain = blockedDomains[i];
      if (stringIncludes(url, domain)) {
        console.warn('Blocked external fetch request:', url);
        return Promise.reject(new Error('External requests blocked for offline mode'));
      }
    }

    // Redirect CanvasKit requests to local files
    if (stringIncludes(url, 'canvaskit') && (stringIncludes(url, 'unpkg.com') || stringIncludes(url, 'cdn'))) {
      const filename = url.split('/').pop();
      const localUrl = '/canvaskit/' + filename;
      console.log('Redirecting CanvasKit request from', url, 'to', localUrl);
      return originalFetch(localUrl, init);
    }

    // Redirect Google Fonts requests to local Roboto fonts
    if (stringIncludes(url, 'fonts.gstatic.com') && stringIncludes(url, 'roboto')) {
      console.log('Redirecting Google Fonts request to local Roboto');
      // Return a simple CSS that uses our local fonts
      const localFontCSS = `
        @font-face {
          font-family: 'Roboto';
          font-style: normal;
          font-weight: 400;
          src: url('/fonts/roboto-regular.woff2') format('woff2');
        }
      `;
      return Promise.resolve(new Response(localFontCSS, {
        status: 200,
        headers: { 'Content-Type': 'text/css' }
      }));
    }

    return originalFetch(resource, init);
  };

  // Override dynamic script loading to prevent external scripts
  const originalCreateElement = document.createElement;
  document.createElement = function(tagName) {
    const element = originalCreateElement.call(this, tagName);

    if (tagName.toLowerCase() === 'script') {
      const originalSetSrc = Object.getOwnPropertyDescriptor(HTMLScriptElement.prototype, 'src').set;
      Object.defineProperty(element, 'src', {
        set: function(value) {
          // Check if script source is external and blocked
          for (let i = 0; i < blockedDomains.length; i++) {
            const domain = blockedDomains[i];
            if (stringIncludes(value, domain)) {
              console.warn('Blocked external script loading:', value);
              return;
            }
          }
          originalSetSrc.call(this, value);
        },
        get: function() {
          return this.getAttribute('src');
        }
      });
    }

    return element;
  };

  // Enhance service worker for better offline support
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.addEventListener('message', function(event) {
      if (event.data && event.data.type === 'CACHE_UPDATED') {
        console.log('Service worker cache updated for offline mode');
      }
    });
  }

  // Monitor network status
  function updateOnlineStatus() {
    if (navigator.onLine) {
      console.log('Network available - but external requests are still blocked for offline mode');
    } else {
      console.log('Network unavailable - running in offline mode');
    }
  }

  window.addEventListener('online', updateOnlineStatus);
  window.addEventListener('offline', updateOnlineStatus);
  updateOnlineStatus();

  console.log('Offline enhancer loaded successfully');
})();
