# Task: Настройка раздачи статических файлов фронтенда через бэкенд на Dart

**Goal:** Настроить раздачу статических файлов фронтенда через бэкенд на Dart для обеспечения работы системы как единого приложения.

**Steps:**

1. Настроить сборку фронтенда для продакшена:
   ```
   cd frontend
   flutter build web
   ```
2. Создать директорию `backend/web` для статических файлов
3. Настроить копирование собранных файлов фронтенда в директорию `backend/web`:
   ```
   mkdir -p backend/web
   cp -r frontend/build/web/* backend/web/
   ```
4. Добавить зависимость `shelf_static` в `backend/pubspec.yaml`:
   ```yaml
   dependencies:
     shelf_static: ^1.1.0
   ```
5. Настроить раздачу статических файлов в `bin/server.dart`:
   ```dart
   import 'package:shelf_static/shelf_static.dart';
   
   // ...
   
   final staticHandler = createStaticHandler('web', defaultDocument: 'index.html');
   
   final handler = Pipeline()
       .addMiddleware(logRequests())
       .addHandler(Cascade()
           .add(staticHandler)
           .add(_router.call)
           .handler);
   ```
6. Настроить маршрут для обработки всех запросов, не относящихся к API:
   ```dart
   _router.get('/<ignored|.*>', (Request request) {
     return staticHandler(request);
   });
   ```
7. Настроить CORS для разработки (когда фронтенд и бэкенд запущены на разных портах)
8. Создать скрипт для сборки и запуска всего приложения

**Expected Result:**
- Статические файлы фронтенда раздаются через бэкенд
- Система работает как единое приложение
- Настроена маршрутизация для SPA (Single Page Application)
- Настроен CORS для разработки
- Создан скрипт для сборки и запуска всего приложения

**Status:** Completed
