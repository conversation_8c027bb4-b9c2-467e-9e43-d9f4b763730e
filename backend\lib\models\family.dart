import 'package:json_annotation/json_annotation.dart';

part 'family.g.dart';

@JsonSerializable()
class Family {
  final int? id;
  final int employeeId;
  final String fullName;
  final String relationship;
  final String? birthDate;
  final String? createdAt;
  final String? updatedAt;

  Family({
    this.id,
    required this.employeeId,
    required this.fullName,
    required this.relationship,
    this.birthDate,
    this.createdAt,
    this.updatedAt,
  });

  factory Family.fromJson(Map<String, dynamic> json) => _$FamilyFromJson(json);

  Map<String, dynamic> toJson() => _$FamilyToJson(this);
}
