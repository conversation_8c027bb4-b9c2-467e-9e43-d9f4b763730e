import 'package:json_annotation/json_annotation.dart';

part 'state_award.g.dart';

@JsonSerializable()
class StateAward {
  final int? id;
  final int employeeId;
  final String awardName;
  final String? dateAwarded;
  final int? orderId;
  final String? createdAt;
  final String? updatedAt;

  StateAward({
    this.id,
    required this.employeeId,
    required this.awardName,
    this.dateAwarded,
    this.orderId,
    this.createdAt,
    this.updatedAt,
  });

  factory StateAward.fromJson(Map<String, dynamic> json) => _$StateAwardFromJson(json);

  Map<String, dynamic> toJson() => _$StateAwardToJson(this);
}
