import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:frontend/models/order.dart';
import 'package:frontend/providers/order_provider.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'dart:typed_data';
import 'package:frontend/components/file_upload_dialog.dart';
import 'package:frontend/providers/order_attachment_provider.dart';

class OrderFormPage extends StatefulWidget {
  final int? orderId;

  const OrderFormPage({super.key, this.orderId});

  @override
  State<OrderFormPage> createState() => _OrderFormPageState();
}

// Класс для хранения выбранных файлов при создании приказа
class _AttachmentData {
  final String fileName;
  final Uint8List fileData;
  _AttachmentData({required this.fileName, required this.fileData});
}

class _OrderFormPageState extends State<OrderFormPage> {
  final _formKey = GlobalKey<FormBuilderState>();
  bool _isLoading = false;
  bool _isEdit = false;
  Order? _order;
  final List<_AttachmentData> _selectedFiles = [];

  @override
  void initState() {
    super.initState();
    _isEdit = widget.orderId != null;
    if (_isEdit) {
      _loadOrder();
    }
  }

  Future<void> _loadOrder() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final order = await Provider.of<OrderProvider>(context, listen: false)
          .getOrder(widget.orderId!);

      setState(() {
        _order = order;
        _isLoading = false;
      });

      // Примечание: мы больше не используем patchValue здесь,
      // так как данные будут установлены через initialValue в FormBuilder
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Ошибка при загрузке данных: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _addAttachment() async {
    await showDialog(
      context: context,
      builder: (context) => FileUploadDialog(
        onUpload: (fileName, fileData) {
          setState(() {
            _selectedFiles
                .add(_AttachmentData(fileName: fileName, fileData: fileData));
          });
        },
      ),
    );
  }

  Future<void> _saveOrder() async {
    if (_formKey.currentState?.saveAndValidate() ?? false) {
      setState(() {
        _isLoading = true;
      });

      final formData = _formKey.currentState!.value;
      final order = Order(
        id: _isEdit ? widget.orderId : null,
        number: formData['number'],
        date: formData['date'] != null
            ? DateFormat('yyyy-MM-dd').format(formData['date'])
            : DateFormat('yyyy-MM-dd').format(DateTime.now()),
        issuedBy: formData['issuedBy'],
        description: formData['description'],
      );

      try {
        bool success;
        if (_isEdit) {
          success = await Provider.of<OrderProvider>(context, listen: false)
              .updateOrder(order);
        } else {
          success = await Provider.of<OrderProvider>(context, listen: false)
              .createOrder(order);
        }

        setState(() {
          _isLoading = false;
        });

        if (mounted) {
          if (success) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(_isEdit
                    ? 'Приказ успешно обновлен'
                    : 'Приказ успешно создан'),
              ),
            );

            // Загрузка выбранных вложений после создания приказа
            if (!_isEdit && _selectedFiles.isNotEmpty) {
              final orderProvider =
                  Provider.of<OrderProvider>(context, listen: false);
              final newOrderId = orderProvider.orders.last.id!;
              final attachmentProvider =
                  Provider.of<OrderAttachmentProvider>(context, listen: false);
              for (final file in _selectedFiles) {
                try {
                  await attachmentProvider.createAttachment(
                      newOrderId, file.fileName, file.fileData);
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Вложение ${file.fileName} загружено'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                } catch (e) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Ошибка загрузки ${file.fileName}: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              }
            }

            if (mounted) {
              context.go('/orders');
            }
          } else {
            final provider = Provider.of<OrderProvider>(context, listen: false);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Ошибка: ${provider.error}'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } catch (e) {
        setState(() {
          _isLoading = false;
        });
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Ошибка: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEdit ? 'Редактирование приказа' : 'Новый приказ'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/orders'),
        ),
      ),
      body: _isLoading && _isEdit
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: FormBuilder(
                key: _formKey,
                initialValue: _isEdit && _order != null
                    ? {
                        'number': _order!.number,
                        'date': _order!.date.isNotEmpty
                            ? DateTime.parse(_order!.date)
                            : DateTime.now(),
                        'issuedBy': _order!.issuedBy,
                        'description': _order!.description ?? '',
                      }
                    : {},
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Информация о приказе',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    FormBuilderTextField(
                      name: 'number',
                      decoration: const InputDecoration(
                        labelText: 'Номер приказа',
                        border: OutlineInputBorder(),
                      ),
                      validator: FormBuilderValidators.compose([
                        FormBuilderValidators.required(
                            errorText: 'Поле обязательно для заполнения'),
                      ]),
                    ),
                    const SizedBox(height: 16),
                    FormBuilderDateTimePicker(
                      name: 'date',
                      inputType: InputType.date,
                      format: DateFormat('dd.MM.yyyy'),
                      initialValue: DateTime.now(),
                      decoration: const InputDecoration(
                        labelText: 'Дата приказа',
                        border: OutlineInputBorder(),
                        suffixIcon: Icon(Icons.calendar_today),
                      ),
                      validator: FormBuilderValidators.compose([
                        FormBuilderValidators.required(
                            errorText: 'Поле обязательно для заполнения'),
                      ]),
                    ),
                    const SizedBox(height: 16),
                    FormBuilderTextField(
                      name: 'issuedBy',
                      decoration: const InputDecoration(
                        labelText: 'Кем издан',
                        border: OutlineInputBorder(),
                      ),
                      validator: FormBuilderValidators.compose([
                        FormBuilderValidators.required(
                            errorText: 'Поле обязательно для заполнения'),
                      ]),
                    ),
                    const SizedBox(height: 16),
                    FormBuilderTextField(
                      name: 'description',
                      decoration: const InputDecoration(
                        labelText: 'Описание',
                        border: OutlineInputBorder(),
                        alignLabelWithHint: true,
                      ),
                      maxLines: 5,
                    ),

                    const SizedBox(height: 16),

                    // Вложения
                    const Text(
                      'Вложения',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton.icon(
                      onPressed: _addAttachment,
                      icon: const Icon(Icons.upload_file),
                      label: const Text('Добавить файл'),
                    ),
                    if (_selectedFiles.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: _selectedFiles.length,
                        itemBuilder: (context, index) {
                          final file = _selectedFiles[index];
                          return ListTile(
                            leading: const Icon(Icons.insert_drive_file),
                            title: Text(file.fileName),
                            trailing: IconButton(
                              icon: const Icon(Icons.delete),
                              onPressed: () {
                                setState(() {
                                  _selectedFiles.removeAt(index);
                                });
                              },
                            ),
                          );
                        },
                      ),
                    ],

                    const SizedBox(height: 24),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _saveOrder,
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Text(
                            _isLoading
                                ? 'Сохранение...'
                                : _isEdit
                                    ? 'Сохранить изменения'
                                    : 'Создать приказ',
                            style: const TextStyle(fontSize: 16),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
