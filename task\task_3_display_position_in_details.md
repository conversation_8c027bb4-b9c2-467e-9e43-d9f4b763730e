# Подзадача: Отображение должности на странице деталей сотрудника

**Статус:** Выполнено

## Описание проблемы
На странице деталей сотрудника не отображается информация о его текущей должности. Это происходит потому, что при загрузке данных сотрудника не загружается информация о его текущей должности из послужного списка.

## Решение
1. Добавить в `_EmployeeDetailsPageState` переменные для хранения информации о текущей должности
2. Создать метод для загрузки текущей должности из послужного списка
3. Вызвать этот метод после загрузки данных сотрудника
4. Добавить отображение информации о должности на странице деталей

## План реализации
1. Добавить в класс `_EmployeeDetailsPageState` переменные `ServiceHistory? _currentServiceRecord` и `Position? _currentPosition`
2. Создать метод `_loadCurrentPosition()` для загрузки текущей должности из послужного списка
3. Вызвать этот метод после загрузки данных сотрудника
4. Добавить блок с информацией о должности на странице деталей сотрудника
