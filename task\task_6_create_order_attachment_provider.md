# Подзадача: Создание провайдера для управления состоянием вложений

## Описание
Необходимо создать провайдер для управления состоянием вложений к приказам на фронтенде. Провайдер должен обеспечивать доступ к данным вложений и методам для работы с ними из любой части приложения.

## Текущее состояние
- Сервис для работы с API вложений к приказам создан
- Отсутствует провайдер для управления состоянием вложений

## Шаги выполнения
1. Создать файл `frontend/lib/providers/order_attachment_provider.dart`
2. Реализовать класс `OrderAttachmentProvider` с использованием `ChangeNotifier` со следующими свойствами и методами:
   - `List<OrderAttachment> _attachments` - список вложений
   - `bool _isLoading` - флаг загрузки
   - `String? _error` - сообщение об ошибке
   - `List<OrderAttachment> get attachments` - геттер для списка вложений
   - `bool get isLoading` - геттер для флага загрузки
   - `String? get error` - геттер для сообщения об ошибке
   - `Future<void> fetchAttachments()` - получить все вложения
   - `Future<void> fetchAttachmentsByOrderId(int orderId)` - получить вложения по ID приказа
   - `Future<OrderAttachment?> getAttachment(int id)` - получить вложение по ID
   - `Future<int> createAttachment(int orderId, String fileName, List<int> fileData)` - создать новое вложение
   - `Future<bool> updateAttachment(OrderAttachment attachment)` - обновить вложение
   - `Future<bool> deleteAttachment(int id)` - удалить вложение
   - `Future<List<int>> downloadAttachment(int id)` - скачать вложение
3. Обновить файл `frontend/lib/app.dart`, добавив `OrderAttachmentProvider` в список провайдеров

## Ожидаемый результат
Провайдер для управления состоянием вложений к приказам на фронтенде, который обеспечивает доступ к данным вложений и методам для работы с ними из любой части приложения.
