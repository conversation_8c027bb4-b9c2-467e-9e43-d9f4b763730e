import 'package:shelf_router/shelf_router.dart';
import 'package:backend/controllers/order_attachment_controller.dart';

/// Создает маршрутизатор для работы с вложениями к приказам
Router orderAttachmentRoutes() {
  final controller = OrderAttachmentController();
  final router = Router();

  // Получить все вложения
  router.get('/', controller.getAll);

  // Получить вложения по ID приказа
  router.get('/order/<orderId>', controller.getByOrderId);

  // Получить вложение по ID
  router.get('/<id>', controller.getById);

  // Создать новое вложение
  router.post('/', controller.create);

  // Обновить вложение
  router.put('/<id>', controller.update);

  // Удалить вложение
  router.delete('/<id>', controller.delete);

  // Скачать вложение
  router.get('/<id>/download', controller.download);

  return router;
}
