// ignore_for_file: avoid_print
import 'package:web/web.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

/// Утилиты для скачивания файлов в веб-приложении
class DownloadUtils {
  /// Базовый URL для API
  static const String baseUrl = 'http://localhost:8080/api';

  /// Скачать файл вложения по ID
  ///
  /// [attachmentId] - ID вложения
  /// [fileName] - имя файла для скачивания
  ///
  /// Возвращает Future, который завершается, когда скачивание начато
  static Future<void> downloadAttachment(
      int attachmentId, String fileName) async {
    if (!kIsWeb) {
      print('Скачивание файлов поддерживается только в веб-версии');
      return;
    }

    try {
      print('Начинаем скачивание файла: $fileName (ID: $attachmentId)');

      // Сначала пробуем метод с iframe, который обходит ограничения CORS
      downloadWithIframe(attachmentId);

      // Если нужно, можно попробовать альтернативные методы
      // await _downloadWithDataUrl(attachmentId, fileName);
    } catch (e) {
      print('Ошибка при скачивании файла: $e');
      rethrow;
    }
  }

  /// Метод скачивания через iframe, который обходит ограничения CORS
  static void downloadWithIframe(int attachmentId) {
    if (!kIsWeb) return;

    try {
      print('Скачивание файла через iframe (обход CORS)');

      // Формируем URL для скачивания
      final downloadUrl = '$baseUrl/order-attachments/$attachmentId/download';
      print('URL для скачивания: $downloadUrl');

      // Метод 1: Открытие в новой вкладке
      // window.open(downloadUrl, '_blank');

      // Метод 2: Использование iframe
      final iframe = HTMLIFrameElement();
      iframe.style.display = 'none';
      iframe.src = downloadUrl;

      // Добавляем iframe в DOM
      document.body?.append(iframe);
      print('iframe добавлен в DOM');

      // Удаляем iframe через некоторое время
      Future.delayed(const Duration(seconds: 5), () {
        iframe.remove();
        print('iframe удален из DOM');
      });

      // Метод 3: Создание ссылки и программный клик
      final anchor = HTMLAnchorElement();
      anchor.href = downloadUrl;
      anchor.target = '_blank';
      anchor.rel = 'noopener noreferrer';
      anchor.style.display = 'none';

      // Добавляем элемент в DOM
      document.body?.append(anchor);

      // Запускаем скачивание
      anchor.click();
      print('Инициировано скачивание через ссылку');

      // Удаляем элемент
      anchor.remove();

      print('Скачивание файла успешно инициировано');
    } catch (e) {
      print('Ошибка при скачивании через iframe: $e');
    }
  }
}
