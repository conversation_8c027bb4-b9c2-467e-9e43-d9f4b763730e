# Task: Корректная обработка Ctrl+C (SIGINT) для немедленного выхода

**Goal:** Настроить Node.js приложение для немедленного и чистого завершения работы при нажатии Ctrl+C, по возможности избегая системного запроса Windows "Завершить выполнение пакетного файла?".

**Sub-tasks:**

1.  **[X] `task_1_create_main_task_file.md`**: Create this task tracking file.
    *   Status: Completed
2.  **[X] `task_2_implement_sigint_handler.md`**: В `ok_admin/index.js` добавить обработчик для `process.on('SIGINT')`, который будет закрывать HTTP сервер, соединение Sequelize и вызывать `process.exit()`.
    *   Status: Completed

**Desired Outcome:**
*   При нажатии Ctrl+C в консоли, где запущено приложение, оно должно корректно завершить свою работу.
*   Соединение с базой данных и HTTP сервер должны быть закрыты перед выходом.
*   Системный запрос Windows "Завершить выполнение пакетного файла?" в идеале не должен появляться, или, по крайней мере, приложение должно быстро завершаться после ответа на него или без него. 