# План действий

## 1. Анализ текущей «базы»
- Открыть файл `Штатка полка.xls`
- Выделить основные сущности:
  - **Сотрудник** (личные данные)
  - **Должность** (название, отдел)
  - **Вакансия** (свободные места)
- Определить поля и разбить их по таблицам

## 2. Проектирование БД (SQLite)
- Таблица `employees`:
  - `id` (PK), фамилия, имя, отчество, другие личные поля
- Таблица `positions`:
  - `id` (PK)
  - `title` (название должности)
  - `department` (отдел)
  - `is_vacant` (булево: свободна/занята)
- Таблица `vacancies`:
  - `id`, `position_id`, статус (свободно/занято)
- Таблица `employee_positions` (история перемещений):
  - `id`, `employee_id`, `position_id`, дата начала, дата завершения
- Продумать связи (FK) и индексы

## 3. Инициализация проекта
- В корне создать папку `backend`
- В `pubspec.yaml` backend добавить зависимость `shelf`, `sqflite_common_ffi`
- Создать папку `migrations` для SQL-файлов миграций
- Настроить подключение к локальному файлу SQLite (`db/data.sqlite`)

## 4. Реализация Backend (Dart + shelf)
1. Файл `bin/server.dart`:
   - Подключить SQLite через `sqflite_common_ffi`
   - Применить миграции из папки `migrations`
   - Создать роуты:
     - `GET /employees`, `POST /employees`, `PUT /employees/{id}`, `DELETE /employees/{id}`
     - Аналогично для `positions`
     - `POST /employees/{id}/move` (перемещение между позициями)
   - Middleware для CORS и логирования
2. Покрыть тестами основные функции (папка `test`)

## 5. Реализация Frontend (Flutter Web)
- В корне создать папку `frontend` (уже есть)
- Страницы и виджеты:
  - Список сотрудников с фильтрами
  - Список должностей и вакансий
  - Формы добавления/редактирования
- Использовать `http` или `dio` для запросов к backend
- Настроить маршрутизацию (Router)
- Сборка: `flutter build web`

## 6. Интеграция Frontend и Backend
- После сборки веба скопировать папку `frontend/build/web` в `backend/web`
- Настроить `StaticFileHandler` в `server.dart`, отдающий содержимое `web`
- Убедиться в том, что при заходе на `/` отдаются статические файлы

## 7. Документация и запуск
- В корне создать `README.md`: 
  - Инструкции по установке Dart/Flutter
  - Как собрать frontend и запустить backend
  - Примеры запросов к API
- (Опционально) Dockerfile для контейнеризации и self-contained сборки

---
_Конец плана_ 