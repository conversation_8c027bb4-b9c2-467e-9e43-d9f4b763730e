// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'contract.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Contract _$ContractFromJson(Map<String, dynamic> json) => Contract(
  id: (json['id'] as num?)?.toInt(),
  employeeId: (json['employeeId'] as num).toInt(),
  startDate: json['startDate'] as String,
  endDate: json['endDate'] as String?,
  orderId: (json['orderId'] as num?)?.toInt(),
  createdAt: json['createdAt'] as String?,
  updatedAt: json['updatedAt'] as String?,
);

Map<String, dynamic> _$ContractToJson(Contract instance) => <String, dynamic>{
  'id': instance.id,
  'employeeId': instance.employeeId,
  'startDate': instance.startDate,
  'endDate': instance.endDate,
  'orderId': instance.orderId,
  'createdAt': instance.createdAt,
  'updatedAt': instance.updatedAt,
};
