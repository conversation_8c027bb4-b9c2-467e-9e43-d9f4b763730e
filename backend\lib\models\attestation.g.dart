// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'attestation.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Attestation _$AttestationFromJson(Map<String, dynamic> json) => Attestation(
  id: (json['id'] as num?)?.toInt(),
  employeeId: (json['employeeId'] as num).toInt(),
  date: json['date'] as String,
  result: json['result'] as String?,
  createdAt: json['createdAt'] as String?,
  updatedAt: json['updatedAt'] as String?,
);

Map<String, dynamic> _$AttestationToJson(Attestation instance) =>
    <String, dynamic>{
      'id': instance.id,
      'employeeId': instance.employeeId,
      'date': instance.date,
      'result': instance.result,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
    };
