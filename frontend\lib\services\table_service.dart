import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:frontend/models/column_metadata.dart';

class TableService {
  static const String baseUrl = 'http://localhost:8080/api/tables';

  /// Получить список таблиц
  Future<List<String>> getTables() async {
    final response = await http.get(Uri.parse(baseUrl));

    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      if (data['success'] == true && data['data'] != null) {
        return List<String>.from(data['data']);
      }
      return [];
    } else {
      throw Exception('Failed to load tables');
    }
  }

  /// Получить данные из таблицы
  Future<Map<String, dynamic>> getTableData(String tableName) async {
    final response = await http.get(Uri.parse('$baseUrl/$tableName'));

    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      if (data['success'] == true && data['data'] != null) {
        return data['data'];
      }
      return {};
    } else {
      throw Exception('Failed to load table data');
    }
  }

  /// Получить метаданные таблицы
  Future<List<ColumnMetadata>> getTableMetadata(String tableName) async {
    final response = await http.get(Uri.parse('$baseUrl/$tableName/metadata'));

    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      if (data['success'] == true && data['data'] != null && data['data']['columnsMetadata'] != null) {
        final List<dynamic> metadataList = data['data']['columnsMetadata'];
        return metadataList.map((item) => ColumnMetadata.fromJson(item)).toList();
      }
      return [];
    } else {
      throw Exception('Failed to load table metadata');
    }
  }

  /// Обновить строку в таблице
  Future<void> updateTableRow(String tableName, String id, Map<String, dynamic> rowData) async {
    final response = await http.put(
      Uri.parse('$baseUrl/$tableName/$id'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode(rowData),
    );

    if (response.statusCode != 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      throw Exception('Failed to update row: ${data['message']}');
    }
  }

  /// Добавить строку в таблицу
  Future<int> addTableRow(String tableName, Map<String, dynamic> rowData) async {
    final response = await http.post(
      Uri.parse('$baseUrl/$tableName'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode(rowData),
    );

    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      if (data['success'] == true && data['data'] != null) {
        return data['data']['id'];
      }
      throw Exception('Failed to add row: ${data['message']}');
    } else {
      throw Exception('Failed to add row');
    }
  }

  /// Удалить строку из таблицы
  Future<void> deleteTableRow(String tableName, String id) async {
    final response = await http.delete(Uri.parse('$baseUrl/$tableName/$id'));

    if (response.statusCode != 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      throw Exception('Failed to delete row: ${data['message']}');
    }
  }
}
