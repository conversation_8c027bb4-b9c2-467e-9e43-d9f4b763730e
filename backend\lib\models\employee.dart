import 'package:json_annotation/json_annotation.dart';

part 'employee.g.dart';

@JsonSerializable()
class Employee {
  final int? id;
  final String lastName;
  final String firstName;
  final String? middleName;
  final int gender;
  final String? birthDate;
  final String? placeOfBirth;
  final String? nationality;
  final String? personalNumber;
  final int? childrenUnder16;
  final String? academicDegree;
  final int? veteranSince;
  final int? positionId;
  final String? createdAt;
  final String? updatedAt;

  Employee({
    this.id,
    required this.lastName,
    required this.firstName,
    this.middleName,
    required this.gender,
    this.birthDate,
    this.placeOfBirth,
    this.nationality,
    this.personalNumber,
    this.childrenUnder16,
    this.academicDegree,
    this.veteranSince,
    this.positionId,
    this.createdAt,
    this.updatedAt,
  });

  factory Employee.fromJson(Map<String, dynamic> json) => _$EmployeeFromJson(json);

  Map<String, dynamic> toJson() => _$EmployeeToJson(this);

  @override
  String toString() {
    return 'Employee{id: $id, lastName: $lastName, firstName: $firstName, middleName: $middleName, positionId: $positionId}';
  }
}
