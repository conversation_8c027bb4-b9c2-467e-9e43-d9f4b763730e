import 'dart:typed_data';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:logging/logging.dart';

/// Диалог для выбора и загрузки файла
class FileUploadDialog extends StatefulWidget {
  final Function(String fileName, Uint8List fileData) onUpload;
  final String title;
  final String acceptedExtensions;

  const FileUploadDialog({
    super.key,
    required this.onUpload,
    this.title = 'Загрузка файла',
    this.acceptedExtensions = '.docx',
  });

  @override
  State<FileUploadDialog> createState() => _FileUploadDialogState();
}

class _FileUploadDialogState extends State<FileUploadDialog> {
  static final Logger _logger = Logger('FileUploadDialog');
  String? _fileName;
  Uint8List? _fileData;
  bool _isLoading = false;
  String? _error;

  Future<void> _pickFile() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: widget.acceptedExtensions
            .split(',')
            .map((e) => e.trim().replaceAll('.', ''))
            .toList(),
        withData: true, // Важно: всегда запрашиваем данные файла
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;

        if (file.bytes != null && file.bytes!.isNotEmpty) {
          _logger.info(
              'Файл выбран: ${file.name}, размер: ${file.bytes!.length} байт');
          setState(() {
            _fileName = file.name;
            _fileData = file.bytes;
          });
        } else {
          _logger
              .warning('Ошибка: файл пустой или не удалось прочитать данные');
          setState(() {
            _error =
                'Не удалось прочитать файл. Возможно, файл пустой или слишком большой.';
          });
        }
      } else {
        _logger.info('Файл не выбран или отменен выбор');
      }
    } catch (e) {
      _logger.severe('Исключение при выборе файла: $e');
      setState(() {
        _error = 'Ошибка при выборе файла: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _upload() {
    if (_fileName != null && _fileData != null) {
      if (_fileData!.isEmpty) {
        setState(() {
          _error = 'Файл пустой. Пожалуйста, выберите другой файл.';
        });
        return;
      }

      _logger.info(
          'Отправка файла: $_fileName, размер: ${_fileData!.length} байт');
      widget.onUpload(_fileName!, _fileData!);
      Navigator.of(context).pop();
    } else {
      setState(() {
        _error = 'Выберите файл для загрузки';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.title),
      content: SizedBox(
        width: 400,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Выберите файл для загрузки:'),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Text(
                    _fileName ?? 'Файл не выбран',
                    style: TextStyle(
                      color: _fileName != null ? Colors.black : Colors.grey,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _isLoading ? null : _pickFile,
                  child: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Text('Выбрать'),
                ),
              ],
            ),
            if (_error != null) ...[
              const SizedBox(height: 8),
              Text(
                _error!,
                style: const TextStyle(color: Colors.red),
              ),
            ],
            const SizedBox(height: 8),
            Text(
              'Поддерживаемые форматы: ${widget.acceptedExtensions}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Отмена'),
        ),
        ElevatedButton(
          onPressed: _fileName != null && _fileData != null ? _upload : null,
          child: const Text('Загрузить'),
        ),
      ],
    );
  }
}
