import 'package:flutter/material.dart';
import 'package:frontend/models/employee.dart';
import 'package:frontend/models/position.dart';
import 'package:frontend/services/api_service.dart';

/// Страница для тестирования API
class ApiTestPage extends StatefulWidget {
  const ApiTestPage({super.key});

  @override
  State<ApiTestPage> createState() => _ApiTestPageState();
}

class _ApiTestPageState extends State<ApiTestPage> {
  final ApiService _apiService = ApiService();
  
  // Состояние для хранения результатов запросов
  List<Employee> _employees = [];
  List<Position> _positions = [];
  String _error = '';
  bool _isLoading = false;
  String _testResult = '';
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Тестирование API'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Тестирование API',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            // Кнопки для тестирования API
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton(
                  onPressed: _fetchEmployees,
                  child: const Text('Получить сотрудников'),
                ),
                ElevatedButton(
                  onPressed: _fetchPositions,
                  child: const Text('Получить должности'),
                ),
                ElevatedButton(
                  onPressed: _createTestEmployee,
                  child: const Text('Создать тестового сотрудника'),
                ),
                ElevatedButton(
                  onPressed: _createTestPosition,
                  child: const Text('Создать тестовую должность'),
                ),
                ElevatedButton(
                  onPressed: _updateTestEmployee,
                  child: const Text('Обновить тестового сотрудника'),
                ),
                ElevatedButton(
                  onPressed: _deleteTestEmployee,
                  child: const Text('Удалить тестового сотрудника'),
                ),
                ElevatedButton(
                  onPressed: _runFullTest,
                  child: const Text('Запустить полный тест'),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Индикатор загрузки
            if (_isLoading)
              const Center(child: CircularProgressIndicator()),
              
            // Сообщение об ошибке
            if (_error.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(8),
                color: Colors.red.shade100,
                child: Text(
                  'Ошибка: $_error',
                  style: const TextStyle(color: Colors.red),
                ),
              ),
              
            // Результат теста
            if (_testResult.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(8),
                color: Colors.green.shade100,
                child: Text(
                  _testResult,
                  style: const TextStyle(color: Colors.green),
                ),
              ),
            
            const SizedBox(height: 16),
            
            // Список сотрудников
            if (_employees.isNotEmpty) ...[
              const Text(
                'Сотрудники:',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _employees.length,
                itemBuilder: (context, index) {
                  final employee = _employees[index];
                  return Card(
                    child: ListTile(
                      title: Text('${employee.lastName} ${employee.firstName} ${employee.middleName ?? ''}'),
                      subtitle: Text('ID: ${employee.id}, Пол: ${employee.genderText}'),
                    ),
                  );
                },
              ),
            ],
            
            const SizedBox(height: 16),
            
            // Список должностей
            if (_positions.isNotEmpty) ...[
              const Text(
                'Должности:',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _positions.length,
                itemBuilder: (context, index) {
                  final position = _positions[index];
                  return Card(
                    child: ListTile(
                      title: Text(position.title),
                      subtitle: Text('ID: ${position.id}, Отдел: ${position.department ?? 'Не указан'}'),
                    ),
                  );
                },
              ),
            ],
          ],
        ),
      ),
    );
  }
  
  // Получить всех сотрудников
  Future<void> _fetchEmployees() async {
    setState(() {
      _isLoading = true;
      _error = '';
    });
    
    try {
      final employees = await _apiService.getEmployees();
      setState(() {
        _employees = employees;
        _isLoading = false;
        _testResult = 'Получено ${employees.length} сотрудников';
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }
  
  // Получить все должности
  Future<void> _fetchPositions() async {
    setState(() {
      _isLoading = true;
      _error = '';
    });
    
    try {
      final positions = await _apiService.getPositions();
      setState(() {
        _positions = positions;
        _isLoading = false;
        _testResult = 'Получено ${positions.length} должностей';
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }
  
  // Создать тестового сотрудника
  Future<void> _createTestEmployee() async {
    setState(() {
      _isLoading = true;
      _error = '';
    });
    
    try {
      final employee = Employee(
        lastName: 'Тестов',
        firstName: 'Тест',
        middleName: 'Тестович',
        gender: 1,
        birthDate: '1990-01-01',
        placeOfBirth: 'г. Тестовск',
        nationality: 'Тестовец',
        personalNumber: 'TEST-${DateTime.now().millisecondsSinceEpoch}',
        childrenUnder16: 1,
        academicDegree: 'Тестовая степень',
        veteranSince: 2020,
      );
      
      final id = await _apiService.createEmployee(employee);
      setState(() {
        _isLoading = false;
        _testResult = 'Создан тестовый сотрудник с ID: $id';
      });
      
      // Обновляем список сотрудников
      await _fetchEmployees();
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }
  
  // Создать тестовую должность
  Future<void> _createTestPosition() async {
    setState(() {
      _isLoading = true;
      _error = '';
    });
    
    try {
      final position = Position(
        title: 'Тестовая должность ${DateTime.now().millisecondsSinceEpoch}',
        department: 'Тестовый отдел',
        unitName: 'Тестовое подразделение',
        womenAllowed: 1,
        militaryRankStaffId: null,
        vus: 'TEST-123',
        vusPss: 'TEST-456',
        positionCodePss: 'TEST-789',
        tariffCategory: '10',
        isFlightCrew: 0,
        antiCorruption: 1,
      );
      
      final id = await _apiService.createPosition(position);
      setState(() {
        _isLoading = false;
        _testResult = 'Создана тестовая должность с ID: $id';
      });
      
      // Обновляем список должностей
      await _fetchPositions();
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }
  
  // Обновить тестового сотрудника
  Future<void> _updateTestEmployee() async {
    if (_employees.isEmpty) {
      setState(() {
        _error = 'Нет сотрудников для обновления';
      });
      return;
    }
    
    setState(() {
      _isLoading = true;
      _error = '';
    });
    
    try {
      final employee = _employees.first;
      final updatedEmployee = Employee(
        id: employee.id,
        lastName: '${employee.lastName} (обновлено)',
        firstName: employee.firstName,
        middleName: employee.middleName,
        gender: employee.gender,
        birthDate: employee.birthDate,
        placeOfBirth: employee.placeOfBirth,
        nationality: employee.nationality,
        personalNumber: employee.personalNumber,
        childrenUnder16: employee.childrenUnder16,
        academicDegree: employee.academicDegree,
        veteranSince: employee.veteranSince,
      );
      
      await _apiService.updateEmployee(updatedEmployee);
      setState(() {
        _isLoading = false;
        _testResult = 'Обновлен сотрудник с ID: ${employee.id}';
      });
      
      // Обновляем список сотрудников
      await _fetchEmployees();
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }
  
  // Удалить тестового сотрудника
  Future<void> _deleteTestEmployee() async {
    if (_employees.isEmpty) {
      setState(() {
        _error = 'Нет сотрудников для удаления';
      });
      return;
    }
    
    setState(() {
      _isLoading = true;
      _error = '';
    });
    
    try {
      final employee = _employees.first;
      await _apiService.deleteEmployee(employee.id!);
      setState(() {
        _isLoading = false;
        _testResult = 'Удален сотрудник с ID: ${employee.id}';
      });
      
      // Обновляем список сотрудников
      await _fetchEmployees();
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }
  
  // Запустить полный тест
  Future<void> _runFullTest() async {
    setState(() {
      _isLoading = true;
      _error = '';
      _testResult = '';
    });
    
    try {
      // 1. Создаем тестового сотрудника
      final employee = Employee(
        lastName: 'Полный',
        firstName: 'Тест',
        middleName: 'Системы',
        gender: 1,
        birthDate: '1995-05-05',
        placeOfBirth: 'г. Тестовград',
        nationality: 'Тестовец',
        personalNumber: 'FULL-TEST-${DateTime.now().millisecondsSinceEpoch}',
        childrenUnder16: 2,
        academicDegree: 'Магистр тестирования',
        veteranSince: 2018,
      );
      
      final employeeId = await _apiService.createEmployee(employee);
      setState(() {
        _testResult += 'Создан тестовый сотрудник с ID: $employeeId\n';
      });
      
      // 2. Создаем тестовую должность
      final position = Position(
        title: 'Тестировщик систем',
        department: 'Отдел тестирования',
        unitName: 'Тестовое подразделение',
        womenAllowed: 1,
        militaryRankStaffId: null,
        vus: 'FULL-123',
        vusPss: 'FULL-456',
        positionCodePss: 'FULL-789',
        tariffCategory: '15',
        isFlightCrew: 0,
        antiCorruption: 1,
      );
      
      final positionId = await _apiService.createPosition(position);
      setState(() {
        _testResult += 'Создана тестовая должность с ID: $positionId\n';
      });
      
      // 3. Получаем созданного сотрудника
      final createdEmployee = await _apiService.getEmployee(employeeId);
      setState(() {
        _testResult += 'Получен созданный сотрудник: ${createdEmployee?.fullName}\n';
      });
      
      // 4. Обновляем сотрудника
      if (createdEmployee != null) {
        final updatedEmployee = Employee(
          id: createdEmployee.id,
          lastName: createdEmployee.lastName,
          firstName: 'Обновленный',
          middleName: createdEmployee.middleName,
          gender: createdEmployee.gender,
          birthDate: createdEmployee.birthDate,
          placeOfBirth: createdEmployee.placeOfBirth,
          nationality: createdEmployee.nationality,
          personalNumber: createdEmployee.personalNumber,
          childrenUnder16: createdEmployee.childrenUnder16,
          academicDegree: createdEmployee.academicDegree,
          veteranSince: createdEmployee.veteranSince,
        );
        
        await _apiService.updateEmployee(updatedEmployee);
        setState(() {
          _testResult += 'Обновлен сотрудник с ID: ${createdEmployee.id}\n';
        });
      }
      
      // 5. Удаляем сотрудника и должность
      await _apiService.deleteEmployee(employeeId);
      setState(() {
        _testResult += 'Удален сотрудник с ID: $employeeId\n';
      });
      
      await _apiService.deletePosition(positionId);
      setState(() {
        _testResult += 'Удалена должность с ID: $positionId\n';
      });
      
      // 6. Обновляем списки
      await _fetchEmployees();
      await _fetchPositions();
      
      setState(() {
        _isLoading = false;
        _testResult += 'Полный тест успешно завершен!';
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
        _testResult += 'Тест прерван из-за ошибки!';
      });
    }
  }
}
