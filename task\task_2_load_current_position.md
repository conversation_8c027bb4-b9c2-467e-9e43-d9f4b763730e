# Подзадача: Загрузка текущей должности сотрудника при редактировании

**Статус:** Выполнено

## Описание проблемы
При редактировании сотрудника не отображается его текущая должность в выпадающем списке должностей. Это происходит потому, что при загрузке формы редактирования не загружается информация о текущей должности сотрудника из послужного списка.

## Решение
1. Добавить в `_EmployeeFormPageState` метод для загрузки текущей должности сотрудника из послужного списка
2. Модифицировать метод `_loadEmployee()` для загрузки текущей должности
3. Добавить переменную для хранения текущей должности сотрудника
4. Обновить initialValue формы, чтобы включить текущую должность

## План реализации
1. Добавить в класс `_EmployeeFormPageState` переменную `int? _currentPositionId`
2. Создать метод `_loadCurrentPosition()` для загрузки текущей должности из послужного списка
3. Вызвать этот метод после загрузки данных сотрудника
4. Обновить initialValue формы, чтобы включить значение `_currentPositionId`
