# Подзадача: Создание контроллера для обработки запросов к API вложений

## Описание
Необходимо создать контроллер для обработки HTTP-запросов к API вложений к приказам. Контроллер должен обеспечивать все необходимые операции для работы с вложениями: получение, создание, обновление и удаление.

## Текущее состояние
- Репозиторий для работы с вложениями к приказам создан
- Отсутствует контроллер для обработки запросов к API вложений

## Шаги выполнения
1. Создать файл `backend/lib/controllers/order_attachment_controller.dart`
2. Реализовать класс `OrderAttachmentController` со следующими методами:
   - `Response getAll(Request request)` - получить все вложения
   - `Response getByOrderId(Request request, String orderId)` - получить вложения по ID приказа
   - `Response getById(Request request, String id)` - получить вложение по ID
   - `Future<Response> create(Request request)` - создать новое вложение
   - `Future<Response> update(Request request, String id)` - обновить вложение
   - `Response delete(Request request, String id)` - удалить вложение
   - `Response download(Request request, String id)` - скачать вложение

## Особенности реализации
- Для метода `create` необходимо реализовать обработку multipart/form-data запросов для загрузки файлов
- Для метода `download` необходимо реализовать возврат файла с правильными заголовками для скачивания

## Ожидаемый результат
Контроллер для обработки запросов к API вложений к приказам, который обеспечивает все необходимые операции для работы с вложениями.
