import 'dart:convert';
import 'package:shelf/shelf.dart';
import 'package:backend/database/repositories/service_history_repository.dart';
import 'package:backend/models/service_history.dart';
import 'package:logging/logging.dart';

class ServiceHistoryController {
  static final Logger _logger = Logger('ServiceHistoryController');
  final ServiceHistoryRepository _repository = ServiceHistoryRepository();

  /// Получить все записи послужного списка
  Response getAll(Request request) {
    _logger.info('Getting all service history records');

    try {
      final records = _repository.getAll();

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': records.map((e) => e.toJson()).toList(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger
          .severe('Error getting all service history records: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Получить записи послужного списка сотрудника
  Response getByEmployeeId(Request request, String employeeId) {
    _logger.info('Getting service history for employee ID: $employeeId');

    try {
      final empId = int.parse(employeeId);
      final records = _repository.getByEmployeeId(empId);

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': records.map((e) => e.toJson()).toList(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe(
          'Error getting service history for employee: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Получить запись послужного списка по ID
  Response getById(Request request, String id) {
    _logger.info('Getting service history record by ID: $id');

    try {
      final recordId = int.parse(id);
      final record = _repository.getById(recordId);

      if (record == null) {
        return Response.notFound(
          jsonEncode({
            'success': false,
            'error': 'Not Found',
            'message': 'Service history record with ID $id not found',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': record.toJson(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe(
          'Error getting service history record by ID: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Создать новую запись послужного списка
  Future<Response> create(Request request) async {
    _logger.info('Creating new service history record');

    try {
      final jsonBody = await request.readAsString();
      final Map<String, dynamic> data = jsonDecode(jsonBody);

      final record = ServiceHistory(
        employeeId: data['employeeId'],
        positionId: data['positionId'],
        externalPositionTitle: data['externalPositionTitle'],
        startDate: data['startDate'],
        endDate: data['endDate'],
        acceptanceDate: data['acceptanceDate'],
        handoverDate: data['handoverDate'],
        orderId: data['orderId'],
        externalOrderInfo: data['externalOrderInfo'],
        notes: data['notes'],
      );

      if (record.positionId != null) {
        final active = _repository.getActiveByPositionId(record.positionId!);
        if (active.isNotEmpty) {
          return Response.badRequest(
            body: jsonEncode({
              'success': false,
              'error': 'PositionOccupied',
              'message':
                  'Position ${record.positionId} already occupied by another employee',
            }),
            headers: {'Content-Type': 'application/json'},
          );
        }
      }

      final id = _repository.create(record);

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': {'id': id},
          'message': 'Service history record created successfully',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error creating service history record: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Обновить запись послужного списка
  Future<Response> update(Request request, String id) async {
    _logger.info('Updating service history record with ID: $id');

    try {
      final recordId = int.parse(id);
      final existingRecord = _repository.getById(recordId);

      if (existingRecord == null) {
        return Response.notFound(
          jsonEncode({
            'success': false,
            'error': 'Not Found',
            'message': 'Service history record with ID $id not found',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final jsonBody = await request.readAsString();
      final Map<String, dynamic> data = jsonDecode(jsonBody);

      final record = ServiceHistory(
        id: recordId,
        employeeId: data['employeeId'] ?? existingRecord.employeeId,
        positionId: data['positionId'] ?? existingRecord.positionId,
        externalPositionTitle: data['externalPositionTitle'] ??
            existingRecord.externalPositionTitle,
        startDate: data['startDate'] ?? existingRecord.startDate,
        endDate: data['endDate'] ?? existingRecord.endDate,
        acceptanceDate: data['acceptanceDate'] ?? existingRecord.acceptanceDate,
        handoverDate: data['handoverDate'] ?? existingRecord.handoverDate,
        orderId: data['orderId'] ?? existingRecord.orderId,
        externalOrderInfo:
            data['externalOrderInfo'] ?? existingRecord.externalOrderInfo,
        notes: data['notes'] ?? existingRecord.notes,
      );

      final success = _repository.update(record);

      if (!success) {
        return Response.internalServerError(
          body: jsonEncode({
            'success': false,
            'error': 'Internal Server Error',
            'message': 'Failed to update service history record',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'Service history record updated successfully',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error updating service history record: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Удалить запись послужного списка
  Response delete(Request request, String id) {
    _logger.info('Deleting service history record with ID: $id');

    try {
      final recordId = int.parse(id);
      final existingRecord = _repository.getById(recordId);

      if (existingRecord == null) {
        return Response.notFound(
          jsonEncode({
            'success': false,
            'error': 'Not Found',
            'message': 'Service history record with ID $id not found',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final success = _repository.delete(recordId);

      if (!success) {
        return Response.internalServerError(
          body: jsonEncode({
            'success': false,
            'error': 'Internal Server Error',
            'message': 'Failed to delete service history record',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'Service history record deleted successfully',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error deleting service history record: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Проверить, есть ли активные записи по ID должности
  Response getActiveByPosition(Request request, String positionId) {
    _logger
        .info('Checking active service history for position ID: $positionId');
    try {
      final id = int.parse(positionId);
      final records = _repository.getActiveByPositionId(id);
      return Response.ok(
        jsonEncode({
          'success': true,
          'data': records.map((e) => e.toJson()).toList(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error checking active service history: $e\n$stackTrace');
      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }
}
