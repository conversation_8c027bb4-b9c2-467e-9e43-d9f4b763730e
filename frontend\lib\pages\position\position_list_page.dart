import 'package:flutter/material.dart';
import 'package:frontend/components/app_drawer.dart';
import 'package:frontend/models/position.dart';
import 'package:frontend/providers/position_provider.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

class PositionListPage extends StatefulWidget {
  const PositionListPage({super.key});

  @override
  State<PositionListPage> createState() => _PositionListPageState();
}

class _PositionListPageState extends State<PositionListPage> {
  @override
  void initState() {
    super.initState();
    // Загружаем данные при инициализации страницы
    Future.microtask(() {
      if (mounted) {
        Provider.of<PositionProvider>(context, listen: false).fetchPositions();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final positionProvider = Provider.of<PositionProvider>(context);

    return Scaffold(
      appBar: AppBar(title: const Text('Должности')),
      drawer: const AppDrawer(),
      body:
          positionProvider.isLoading
              ? const Center(child: CircularProgressIndicator())
              : positionProvider.error != null
              ? Center(
                child: Text(
                  'Ошибка: ${positionProvider.error}',
                  style: const TextStyle(color: Colors.red),
                ),
              )
              : positionProvider.positions.isEmpty
              ? const Center(child: Text('Нет данных о должностях'))
              : ListView.builder(
                itemCount: positionProvider.positions.length,
                itemBuilder: (context, index) {
                  final position = positionProvider.positions[index];
                  return _buildPositionListItem(context, position);
                },
              ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          context.go('/positions/new');
        },
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        tooltip: 'Добавить новую должность',
        child: const Icon(Icons.add, size: 28),
      ),
    );
  }

  Widget _buildPositionListItem(BuildContext context, Position position) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        title: Text(
          position.title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (position.department != null)
              Text('Подразделение: ${position.department}'),
            if (position.unitName != null)
              Text('Условное наименование части: ${position.unitName}'),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () {
                context.go('/positions/${position.id}/edit');
              },
            ),
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: () {
                _showDeleteConfirmationDialog(context, position);
              },
            ),
          ],
        ),
        onTap: () {
          context.go('/positions/${position.id}');
        },
      ),
    );
  }

  Future<void> _showDeleteConfirmationDialog(
    BuildContext context,
    Position position,
  ) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('Подтверждение удаления'),
          content: SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                Text(
                  'Вы уверены, что хотите удалить должность "${position.title}"?',
                ),
                const Text('Это действие нельзя будет отменить.'),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Отмена'),
              onPressed: () {
                Navigator.of(dialogContext).pop();
              },
            ),
            TextButton(
              child: const Text('Удалить'),
              onPressed: () {
                Navigator.of(dialogContext).pop();
                _deletePosition(position);
              },
            ),
          ],
        );
      },
    );
  }

  // Метод для удаления должности
  Future<void> _deletePosition(Position position) async {
    if (position.id != null) {
      // Сохраняем ссылку на провайдер перед асинхронной операцией
      final provider = Provider.of<PositionProvider>(context, listen: false);
      final success = await provider.deletePosition(position.id!);

      // Проверяем, что виджет все еще в дереве виджетов
      if (!mounted) return;

      // Используем BuildContext только после проверки mounted
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Должность успешно удалена')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Ошибка при удалении должности: ${provider.error}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
