# Task: Автоматическое применение миграций при старте

**Goal:** Настроить приложение так, чтобы все ожидающие миграции Sequelize применялись автоматически при каждом запуске сервера.

**Sub-tasks:**

1.  **[X] `task_1_create_main_task_file.md`**: Create this task tracking file.
    *   Status: Completed
2.  **[X] `task_2_add_migration_script_to_indexjs.md`**: В `ok_admin/index.js` добавить код для выполнения команды `npx sequelize-cli db:migrate` перед аутентификацией Sequelize.
    *   Status: Completed

**Desired Outcome:**
*   При запуске `npm start` (или `node ok_admin/index.js`) все непримененные миграции выполняются автоматически.
*   Сообщения от процесса миграции выводятся в консоль. 