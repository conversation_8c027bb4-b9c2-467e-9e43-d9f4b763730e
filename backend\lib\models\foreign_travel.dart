import 'package:json_annotation/json_annotation.dart';

part 'foreign_travel.g.dart';

@JsonSerializable()
class ForeignTravel {
  final int? id;
  final int employeeId;
  final String country;
  final String? startDate;
  final String? endDate;
  final String? createdAt;
  final String? updatedAt;

  ForeignTravel({
    this.id,
    required this.employeeId,
    required this.country,
    this.startDate,
    this.endDate,
    this.createdAt,
    this.updatedAt,
  });

  factory ForeignTravel.fromJson(Map<String, dynamic> json) => _$ForeignTravelFromJson(json);

  Map<String, dynamic> toJson() => _$ForeignTravelToJson(this);
}
