# Устранение неполадок веб-интерфейса на Windows 7

## Проблемы
На Windows 7 могут возникать следующие проблемы с HR системой:

### 1. Белая страница (Service Workers)
Веб-интерфейс не загружается, отображая только белую страницу.

### 2. Ошибка SSL/TLS соединения (НОВАЯ ПРОБЛЕМА)
Браузер пытается использовать HTTPS вместо HTTP, показывая ошибки:
- "PR_END_OF_FILE_ERROR"
- "Ошибка при установлении защищённого соединения"
- "This site can't provide a secure connection"

## Причины
1. **Service Workers не поддерживаются** в старых версиях браузеров на Windows 7
2. **Современный JavaScript** может быть несовместим с устаревшими браузерами
3. **Таймауты загрузки** могут быть слишком короткими для медленных систем
4. **Принудительное HTTPS** - браузер автоматически перенаправляет на HTTPS
5. **Кэш браузера** содержит старые настройки HTTPS

## Решения

### 1. БЫСТРОЕ РЕШЕНИЕ для проблем с HTTPS
**Если браузер не может подключиться к серверу:**

1. **Запустите автоматическое исправление:**
   ```
   fix_windows7_https.bat
   ```

2. **Используйте принудительный HTTP:**
   - Откройте `http://localhost:8080` (НЕ https)
   - Или используйте `http://127.0.0.1:8080`

3. **Очистите кэш браузера:**
   - Chrome: Ctrl+Shift+Del → "Все время" → отметьте все
   - Firefox: Ctrl+Shift+Del → "Все" → отметьте все
   - IE: Сервис → Свойства обозревателя → Удалить

4. **Используйте режим инкогнито/приватный просмотр**

### 2. Основное решение для белой страницы (уже применено)
Файл `web/index.html` был обновлен для лучшей совместимости с Windows 7:
- Добавлена проверка поддержки современных функций браузера
- Сокращены таймауты для более быстрого fallback
- Улучшена обработка ошибок Service Worker
- Добавлены множественные fallback механизмы
- Добавлено принудительное перенаправление с HTTPS на HTTP

### 3. Альтернативное решение для критических случаев
Если основная версия не работает, используйте legacy версию:

1. Переименуйте `index.html` в `index_modern.html`
2. Переименуйте `index_legacy.html` в `index.html`
3. Перезапустите сервер

### 3. Рекомендуемые браузеры для Windows 7
- **Microsoft Edge Legacy** (если доступен)
- **Google Chrome** версии 49 или новее
- **Mozilla Firefox** версии 52 ESR или новее
- **Internet Explorer 11** (ограниченная поддержка)

### 4. Диагностика проблем

#### Проверка в консоли браузера
Откройте консоль разработчика (F12) и проверьте сообщения:

**Нормальная загрузка:**
```
Initializing Flutter app...
Modern browser detected, trying Service Worker...
Service Worker registered successfully
main.dart.js loaded successfully
```

**Fallback загрузка:**
```
Initializing Flutter app...
Legacy browser detected, loading directly...
main.dart.js loaded successfully
```

**Проблемы:**
```
Failed to load main.dart.js
Service Worker registration failed
```

#### Проверка сетевых запросов
В консоли разработчика на вкладке Network проверьте:
- `index.html` - должен загружаться (200 или 304)
- `main.dart.js` - должен загружаться (200)
- `flutter_service_worker.js` - может не загружаться в legacy режиме

### 5. Ручная диагностика

#### Проверка доступности файлов
Откройте в браузере:
- `http://localhost:8080/` - главная страница
- `http://localhost:8080/test.html` - **диагностическая страница** (НОВОЕ!)
- `http://localhost:8080/main.dart.js` - основной JavaScript файл
- `http://localhost:8080/api/employees` - API должно возвращать JSON

#### Диагностическая страница (НОВОЕ!)
Откройте `http://localhost:8080/test.html` для:
- Автоматической диагностики подключения
- Тестирования HTTP/HTTPS
- Проверки API
- Получения информации о системе
- Принудительного перенаправления на HTTP

#### Проверка логов сервера
В консоли сервера должны быть сообщения:
```
GET  200 (Xms)  # для успешных запросов
GET  304 (Xms)  # для кэшированных файлов
```

### 6. Дополнительные настройки для Windows 7

#### Настройки Internet Explorer (влияют на Edge Legacy)
1. Откройте Internet Explorer
2. Перейдите в Сервис → Свойства обозревателя
3. Вкладка "Безопасность" → Интернет → Другой
4. Включите "Активные сценарии"
5. Включите "Выполнять сценарии приложений Java"

#### Обновление браузера
Убедитесь, что используется последняя доступная версия браузера для Windows 7.

### 7. Контакты для поддержки
Если проблемы продолжаются:
1. Сохраните логи из консоли браузера
2. Сохраните логи сервера
3. Укажите версию Windows и браузера
4. Обратитесь к системному администратору

## Статус исправления
✅ **Исправлено**: Обновлен index.html для лучшей совместимости с Windows 7
✅ **Добавлено**: Legacy версия для критических случаев
✅ **Протестировано**: Работает с API запросами на Windows 7
✅ **НОВОЕ**: Добавлено принудительное перенаправление с HTTPS на HTTP
✅ **НОВОЕ**: Создана диагностическая страница test.html
✅ **НОВОЕ**: Добавлены заголовки безопасности для предотвращения HTTPS
✅ **НОВОЕ**: Создан скрипт автоматического исправления fix_windows7_https.bat
✅ **НОВОЕ**: Добавлены ярлыки для запуска браузеров с принудительным HTTP
