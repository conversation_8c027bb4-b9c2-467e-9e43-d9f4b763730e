import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:test/test.dart';

void main() {
  const baseUrl = 'http://localhost:8080/api';
  int? employeeId;

  group('Employee API Tests', () {
    test('Create Employee', () async {
      final employeeData = {
        'lastName': 'Иванов',
        'firstName': 'Иван',
        'middleName': 'Иванович',
        'gender': 1,
        'birthDate': '1990-01-01',
        'placeOfBirth': 'г. Москва',
        'nationality': 'Русский',
        'personalNumber': 'EMP-${DateTime.now().millisecondsSinceEpoch}',
        'childrenUnder16': 2,
        'academicDegree': 'Кандидат наук',
        'veteranSince': 2015
      };
      
      final response = await http.post(
        Uri.parse('$baseUrl/employees'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(employeeData),
      );
      
      expect(response.statusCode, equals(200));
      
      final result = jsonDecode(response.body);
      expect(result['success'], isTrue);
      expect(result['data'], isNotNull);
      expect(result['data']['id'], isNotNull);
      
      employeeId = result['data']['id'];
    });
    
    test('Get All Employees', () async {
      final response = await http.get(Uri.parse('$baseUrl/employees'));
      
      expect(response.statusCode, equals(200));
      
      final result = jsonDecode(response.body);
      expect(result['success'], isTrue);
      expect(result['data'], isA<List>());
    });
    
    test('Get Employee By ID', () async {
      if (employeeId == null) {
        fail('Employee ID is null. Create Employee test failed.');
      }
      
      final response = await http.get(Uri.parse('$baseUrl/employees/$employeeId'));
      
      expect(response.statusCode, equals(200));
      
      final result = jsonDecode(response.body);
      expect(result['success'], isTrue);
      expect(result['data'], isNotNull);
      expect(result['data']['id'], equals(employeeId));
      expect(result['data']['lastName'], equals('Иванов'));
      expect(result['data']['firstName'], equals('Иван'));
    });
    
    test('Update Employee', () async {
      if (employeeId == null) {
        fail('Employee ID is null. Create Employee test failed.');
      }
      
      final updateData = {
        'lastName': 'Иванов',
        'firstName': 'Иван',
        'middleName': 'Петрович', // Изменено отчество
        'nationality': 'Россиянин', // Изменена национальность
      };
      
      final response = await http.put(
        Uri.parse('$baseUrl/employees/$employeeId'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(updateData),
      );
      
      expect(response.statusCode, equals(200));
      
      final result = jsonDecode(response.body);
      expect(result['success'], isTrue);
      
      // Проверяем, что данные действительно обновились
      final getResponse = await http.get(Uri.parse('$baseUrl/employees/$employeeId'));
      final getResult = jsonDecode(getResponse.body);
      
      expect(getResult['data']['middleName'], equals('Петрович'));
      expect(getResult['data']['nationality'], equals('Россиянин'));
    });
    
    test('Validation Error - Missing Required Fields', () async {
      final invalidData = {
        // Отсутствует обязательное поле lastName
        'firstName': 'Тест',
        'gender': 1
      };
      
      final response = await http.post(
        Uri.parse('$baseUrl/employees'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(invalidData),
      );
      
      expect(response.statusCode, equals(400));
      
      final result = jsonDecode(response.body);
      expect(result['success'], isFalse);
      expect(result['error'], equals('Bad Request'));
    });
    
    test('Validation Error - Invalid Data Types', () async {
      final invalidData = {
        'lastName': 'Тестов',
        'firstName': 'Тест',
        'gender': 'мужской', // Должно быть числом (1 или 0)
        'childrenUnder16': 'два' // Должно быть числом
      };
      
      final response = await http.post(
        Uri.parse('$baseUrl/employees'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(invalidData),
      );
      
      expect(response.statusCode, equals(400));
      
      final result = jsonDecode(response.body);
      expect(result['success'], isFalse);
    });
    
    test('Delete Employee', () async {
      if (employeeId == null) {
        fail('Employee ID is null. Create Employee test failed.');
      }
      
      final response = await http.delete(Uri.parse('$baseUrl/employees/$employeeId'));
      
      expect(response.statusCode, equals(200));
      
      final result = jsonDecode(response.body);
      expect(result['success'], isTrue);
      
      // Проверяем, что сотрудник действительно удален
      final getResponse = await http.get(Uri.parse('$baseUrl/employees/$employeeId'));
      expect(getResponse.statusCode, equals(404));
    });
  });
}
