# Task: Изменить логику назначения должностей

**Goal:** Убрать прямое редактирование должности, заменить на поиск и назначение через диалоговое окно на страницах деталей и редактирования сотрудника.

**Sub-tasks:**

1.  **[X] `task_1_create_main_task_file.md`**: Create this task tracking file.
    *   Status: Completed
2.  **[X] `task_2_modify_employee_detail_page.md`**: Изменить страницу деталей сотрудника (`EmployeeDetailPage`): удалить кнопку "Назначить", переименовать "Найти" в "Найти и назначить" и подключить к диалогу назначения.
    *   Status: Completed
3.  **[X] `task_3_modify_employee_form_page.md`**: Изменить страницу формы сотрудника (`EmployeeFormPage`): удалить выпадающий список должностей, добавить кнопку "Найти и назначить", подключить к диалогу назначения и логике сохранения формы.
    *   Status: Completed

**Desired Outcome:**
*   На странице деталей сотрудника (`EmployeeDetailPage`) удалена кнопка "Назначить".
*   На странице деталей сотрудника кнопка "Найти" переименована в "Найти и назначить".
*   Нажатие на "Найти и назначить" в `EmployeeDetailPage` открывает диалог поиска должностей; выбор должности в этом диалоге приводит к открытию диалога назначения (`PositionAssignmentDialog`) с предвыбранной должностью.
*   На странице редактирования сотрудника (`EmployeeFormPage`) удалено выпадающее меню для выбора должности.
*   На странице редактирования сотрудника добавлена кнопка "Найти и назначить".
*   Нажатие на "Найти и назначить" в `EmployeeFormPage` открывает диалог поиска должностей; выбор должности в этом диалоге обновляет выбранную должность для сохранения формы, и рядом с кнопкой отображается название выбранной должности.
*   Данные о назначении (должность, дата начала) корректно сохраняются для сотрудника и создается соответствующая запись в истории службы при изменении должности. 