-- Создание таблицы ведомственных наград
CREATE TABLE IF NOT EXISTS departmental_awards (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    employee_id INTEGER NOT NULL,
    award_name TEXT NOT NULL,
    date_awarded TEXT,
    order_id INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOR<PERSON><PERSON><PERSON> KEY (order_id) REFERENCES orders(id) ON DELETE SET NULL ON UPDATE CASCADE
);
