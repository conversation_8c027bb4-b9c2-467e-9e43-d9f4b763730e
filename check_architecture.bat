@echo off
echo ===== Checking HR System Executable Architecture =====
echo.

cd dist
echo Checking hr_system.exe architecture...

powershell -Command "& { $arch = (Get-Command .\hr_system.exe).FileVersionInfo.FileDescription; if ($arch -match '64-bit') { Write-Host 'hr_system.exe is 64-bit' } else { $bytes = [System.IO.File]::ReadAllBytes('hr_system.exe'); $is64 = $bytes[0x3C] -lt $bytes.Length -and ($bytes[$bytes[0x3C] + 0x4] -eq 0x64 -or $bytes[$bytes[0x3C] + 0x4] -eq 0x62); if ($is64) { Write-Host 'hr_system.exe is 64-bit' } else { Write-Host 'hr_system.exe is 32-bit' } } }"

echo.
echo ===== Architecture check completed =====
echo.
cd ..
pause
