# Подзадача: Тестирование обновления должности сотрудника

**Статус:** Выполнено

## Описание тестирования
Было проведено тестирование функциональности обновления должности сотрудника при редактировании.

## Сценарии тестирования
1. Создание нового сотрудника с назначением на должность
2. Редактирование сотрудника без изменения должности
3. Редактирование сотрудника с изменением должности

## Результаты тестирования
1. При создании нового сотрудника с выбранной должностью успешно создается запись в послужном списке
2. При редактировании сотрудника без изменения должности новая запись в послужном списке не создается
3. При редактировании сотрудника с изменением должности:
   - Предыдущая запись в послужном списке закрывается (устанавливается дата окончания)
   - Создается новая запись с новой должностью
   - На странице деталей сотрудника отображается актуальная должность

## Выводы
Функциональность обновления должности сотрудника работает корректно. Теперь при изменении должности сотрудника создается новая запись в послужном списке, а предыдущая запись закрывается.
