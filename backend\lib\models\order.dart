import 'package:json_annotation/json_annotation.dart';

part 'order.g.dart';

@JsonSerializable()
class Order {
  final int? id;
  final String number;
  final String date;
  final String issuedBy;
  final String? description;
  final String? createdAt;
  final String? updatedAt;

  Order({
    this.id,
    required this.number,
    required this.date,
    required this.issuedBy,
    this.description,
    this.createdAt,
    this.updatedAt,
  });

  factory Order.fromJson(Map<String, dynamic> json) => _$OrderFromJson(json);

  Map<String, dynamic> toJson() => _$OrderToJson(this);
}
