import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:sqlite3/sqlite3.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart' as sqflite_ffi;
import 'package:logging/logging.dart';

/// Класс для работы с базой данных SQLite
class AppDatabase {
  static final Logger _logger = Logger('AppDatabase');
  static Database? _database;
  static const String _dbName = 'hr_system.db';
  static const String _dbDir = 'db';

  /// Получить экземпляр базы данных
  static Database get database {
    if (_database == null) {
      throw StateError('Database not initialized. Call initialize() first.');
    }
    return _database!;
  }

  /// Инициализировать базу данных
  static Future<void> initialize({String? customDbDir}) async {
    if (_database != null) return;

    // Инициализируем SQLite FFI
    try {
      // Инициализация для всех платформ
      sqflite_ffi.sqfliteFfiInit();
      _logger.info('SQLite FFI initialized successfully');
    } catch (e) {
      _logger.warning('Failed to initialize SQLite FFI: $e');
      // Продолжаем работу, так как библиотека может быть уже доступна в системе
    }

    // Используем указанную директорию или директорию по умолчанию
    final dbDirPath = customDbDir ?? _dbDir;
    _logger.info('Using database directory: $dbDirPath');

    // Создаем директорию для базы данных, если она не существует
    final dbDir = Directory(dbDirPath);
    if (!await dbDir.exists()) {
      _logger.info('Creating database directory: $dbDirPath');
      await dbDir.create(recursive: true);
      _logger.info('Database directory created successfully');
    } else {
      _logger.info('Database directory already exists: $dbDirPath');
    }

    final dbPath = path.join(dbDirPath, _dbName);
    _logger.info('Initializing database at $dbPath');

    try {
      _database = sqlite3.open(dbPath);
      _logger.info('Database initialized successfully');
    } catch (e) {
      _logger.severe('Failed to open database: $e');
      rethrow;
    }
  }

  /// Закрыть соединение с базой данных
  static void close() {
    _database?.dispose();
    _database = null;
    _logger.info('Database connection closed');
  }

  /// Выполнить SQL-запрос, который возвращает результат (SELECT)
  static ResultSet select(String sql, [List<Object?> parameters = const []]) {
    _logger.fine('Executing SELECT SQL: $sql with parameters: $parameters');
    return database.select(sql, parameters);
  }

  /// Выполнить SQL-запрос, который не возвращает результат (INSERT, UPDATE, DELETE)
  static void execute(String sql, [List<Object?> parameters = const []]) {
    _logger.fine('Executing SQL: $sql with parameters: $parameters');
    database.execute(sql, parameters);
  }

  /// Применить миграции из директории migrations
  static Future<void> applyMigrations({String? customMigrationsDir}) async {
    _logger.info('Applying migrations...');

    // Создаем таблицу для отслеживания миграций, если она не существует
    execute('''
      CREATE TABLE IF NOT EXISTS migrations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        applied_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
      )
    ''');

    // Получаем список уже примененных миграций
    final appliedMigrations = select(
      'SELECT name FROM migrations',
    ).map((row) => row['name'] as String).toList();

    _logger.info('Already applied migrations: ${appliedMigrations.join(', ')}');

    // Получаем список файлов миграций
    final migrationsDirPath = customMigrationsDir ?? 'migrations';
    final migrationsDir = Directory(migrationsDirPath);

    _logger.info('Looking for migrations in: ${migrationsDir.path}');

    if (!await migrationsDir.exists()) {
      _logger.warning(
          'Migrations directory does not exist: ${migrationsDir.path}');
      return;
    }

    try {
      final migrationFiles = await migrationsDir
          .list()
          .where((entity) => entity is File && entity.path.endsWith('.sql'))
          .map((entity) => entity as File)
          .toList();

      _logger.info('Found ${migrationFiles.length} migration files');

      if (migrationFiles.isEmpty) {
        _logger.warning('No migration files found in ${migrationsDir.path}');
        return;
      }

      // Сортируем файлы миграций по имени
      migrationFiles.sort(
        (a, b) => path.basename(a.path).compareTo(path.basename(b.path)),
      );

      // Выводим список найденных файлов миграций
      _logger.info('Migration files found:');
      for (final file in migrationFiles) {
        _logger.info('  ${file.path}');
      }

      // Применяем миграции, которые еще не были применены
      for (final file in migrationFiles) {
        final migrationName = path.basename(file.path);
        if (!appliedMigrations.contains(migrationName)) {
          _logger.info('Applying migration: $migrationName');

          try {
            final sql = await File(file.path).readAsString();
            _logger.fine('Migration SQL: $sql');

            database.execute(sql);

            // Записываем информацию о примененной миграции
            execute(
                'INSERT INTO migrations (name) VALUES (?)', [migrationName]);

            _logger.info('Migration applied successfully: $migrationName');
          } catch (e, stackTrace) {
            _logger.severe('Error applying migration $migrationName: $e');
            _logger.severe('Stack trace: $stackTrace');
            rethrow;
          }
        } else {
          _logger.info('Migration already applied: $migrationName');
        }
      }

      _logger.info('All migrations applied successfully');
    } catch (e, stackTrace) {
      _logger.severe('Error listing migration files: $e');
      _logger.severe('Stack trace: $stackTrace');
      rethrow;
    }
  }
}
