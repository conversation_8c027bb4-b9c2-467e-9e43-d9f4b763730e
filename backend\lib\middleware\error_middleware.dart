import 'dart:convert';
import 'package:shelf/shelf.dart';
import 'package:logging/logging.dart';

/// Middleware для обработки ошибок
Middleware errorMiddleware() {
  final logger = Logger('ErrorMiddleware');
  
  return (Handler innerHandler) {
    return (Request request) async {
      try {
        final response = await innerHandler(request);
        return response;
      } catch (e, stackTrace) {
        logger.severe('Error handling request: $e\n$stackTrace');
        
        return Response.internalServerError(
          body: jsonEncode({
            'error': 'Internal Server Error',
            'message': e.toString(),
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }
    };
  };
}
