# Task: Обновить db_schema.md для таблицы Employees

**Goal:** Синхронизировать определение таблицы `employees` в файле `db_schema.md` с изменениями, внесенными в миграцию: изменить тип поля `veteran_since` на INTEGER и удалить поле `combat_participation`.

**Sub-tasks:**

1.  **[X] `task_1_create_main_task_file.md`**: Create this task tracking file.
    *   Status: Completed
2.  **[X] `task_2_modify_db_schema_employees.md`**: В файле `db_schema.md` в определении таблицы `employees` изменить тип поля `veteran_since` с `DATE` на `INTEGER` и удалить строку с полем `combat_participation BOOLEAN`.
    *   Status: Completed

**Desired Outcome:**
*   Файл `db_schema.md` корректно отражает актуальную структуру таблицы `employees`:
    *   Поле `veteran_since` имеет тип `INTEGER`.
    *   Поле `combat_participation` отсутствует.
*   Файл задачи `task/task_update_db_schema_for_employees.md` отражает текущий статус выполнения. 