import 'package:shelf_router/shelf_router.dart';
import 'package:backend/controllers/contract_controller.dart';

/// Создает маршрутизатор для работы с контрактами
Router contractRoutes() {
  final controller = ContractController();
  final router = Router();

  // Получить все контракты
  router.get('/', controller.getAll);

  // Получить контракт по ID
  router.get('/<id>', controller.getById);

  // Получить контракты сотрудника
  router.get('/employee/<employeeId>', controller.getByEmployeeId);

  // Создать новый контракт
  router.post('/', controller.create);

  // Обновить контракт
  router.put('/<id>', controller.update);

  // Удалить контракт
  router.delete('/<id>', controller.delete);

  return router;
}
