# Подзадача: Реализация фильтрации данных на страницах списков

## Описание
Необходимо реализовать фильтрацию данных на страницах списков (сотрудники, должности, приказы, послужной список) для улучшения пользовательского опыта при работе с большими объемами данных. Фильтрация должна позволять пользователям быстро находить нужную информацию по различным критериям.

## Текущее состояние
- Страницы списков отображают все данные без возможности фильтрации
- API бэкенда поддерживает базовые параметры фильтрации, но они не используются во фронтенде
- Отсутствует единый подход к фильтрации данных во фронтенде

## Необходимые изменения
1. Создать переиспользуемый компонент для фильтрации:
   - `frontend/lib/components/filter_panel.dart` - компонент для отображения и управления фильтрами

2. Обновить сервисы для поддержки параметров фильтрации:
   - `frontend/lib/services/employee_service.dart`
   - `frontend/lib/services/position_service.dart`
   - `frontend/lib/services/order_service.dart`
   - `frontend/lib/services/service_history_service.dart`

3. Обновить провайдеры для поддержки фильтрации:
   - `frontend/lib/providers/employee_provider.dart`
   - `frontend/lib/providers/position_provider.dart`
   - `frontend/lib/providers/order_provider.dart`
   - `frontend/lib/providers/service_history_provider.dart`

4. Обновить страницы списков для использования компонента фильтрации:
   - `frontend/lib/pages/employee/employee_list_page.dart`
   - `frontend/lib/pages/position/position_list_page.dart`
   - `frontend/lib/pages/order/order_list_page.dart`
   - `frontend/lib/pages/service_history/service_history_list_page.dart`

## Детали реализации
1. Компонент фильтрации (`filter_panel.dart`):
   - Поддержка различных типов фильтров (текстовый поиск, выбор из списка, диапазон дат, числовой диапазон)
   - Возможность добавления и удаления фильтров
   - Сохранение состояния фильтров между сеансами
   - Кнопки для применения фильтров и сброса

2. Обновление сервисов:
   - Добавление параметров фильтрации в запросы к API
   - Преобразование параметров фильтрации в формат, понятный API

3. Обновление провайдеров:
   - Хранение текущих фильтров в состоянии
   - Методы для установки и сброса фильтров
   - Обновление методов загрузки данных для использования фильтров

4. Обновление страниц списков:
   - Добавление компонента фильтрации
   - Обработка изменений фильтров
   - Отображение примененных фильтров
   - Индикация загрузки при изменении фильтров

## Фильтры для каждой сущности
1. Сотрудники:
   - ФИО (текстовый поиск)
   - Пол (выбор из списка)
   - Дата рождения (диапазон дат)
   - Личный номер (текстовый поиск)

2. Должности:
   - Название (текстовый поиск)
   - Отдел (текстовый поиск)
   - Подразделение (текстовый поиск)
   - Доступность для женщин (выбор из списка)

3. Приказы:
   - Номер (текстовый поиск)
   - Тип (выбор из списка)
   - Дата (диапазон дат)
   - Статус (выбор из списка)

4. Послужной список:
   - Сотрудник (выбор из списка)
   - Должность (выбор из списка)
   - Период (диапазон дат)
   - Приказ (текстовый поиск)

## Ожидаемый результат
- Единый подход к фильтрации данных во всем приложении
- Удобный пользовательский интерфейс для управления фильтрами
- Быстрый доступ к нужной информации при работе с большими объемами данных
- Сохранение состояния фильтров между сеансами
