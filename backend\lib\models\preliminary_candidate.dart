import 'package:json_annotation/json_annotation.dart';

part 'preliminary_candidate.g.dart';

@JsonSerializable()
class PreliminaryCandidate {
  final int? id;
  final String lastName;
  final String firstName;
  final String? middleName;
  final String? rank;
  final String? notes;
  final String? createdAt;
  final String? updatedAt;

  PreliminaryCandidate({
    this.id,
    required this.lastName,
    required this.firstName,
    this.middleName,
    this.rank,
    this.notes,
    this.createdAt,
    this.updatedAt,
  });

  factory PreliminaryCandidate.fromJson(Map<String, dynamic> json) => _$PreliminaryCandidateFromJson(json);

  Map<String, dynamic> toJson() => _$PreliminaryCandidateToJson(this);

  String get fullName {
    if (middleName != null && middleName!.isNotEmpty) {
      return '$lastName $firstName $middleName';
    }
    return '$lastName $firstName';
  }
}
