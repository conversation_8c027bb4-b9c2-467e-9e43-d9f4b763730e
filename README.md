# HR System - Система управления кадрами

Полнофункциональная система управления кадрами с веб-интерфейсом на Flutter и бэкендом на Dart. Система работает полностью автономно без необходимости интернет-соединения.

## 🚀 Быстрый старт

### Сборка и запуск
```bash
# Сборка production версии
.\build_production_en.bat

# Запуск приложения
cd dist
.\hr_system.exe

# ИЛИ используйте startup скрипт
.\dist\start_hr_system.bat
```

### Доступ к приложению
Откройте http://localhost:8080 в браузере после запуска сервера.

## ✨ Особенности

- **Полностью автономная работа** - не требует интернет-соединения
- **Локальные шрифты Roboto** - встроены в сборку приложения
- **Локальный CanvasKit** - все ресурсы Flutter включены
- **SQLite база данных** - автономное хранение данных
- **Единый исполняемый файл** - простое развертывание

## 📁 Структура проекта

```
├── backend/          # Dart бэкенд (Shelf)
├── frontend/         # Flutter веб-фронтенд
├── dist/            # Production сборка
├── docs/            # Документация
└── migrations/      # SQL миграции
```

## 🛠️ Технологии

- **Backend**: Dart + Shelf
- **Frontend**: Flutter Web
- **Database**: SQLite
- **Fonts**: Roboto (локальные)
- **Rendering**: CanvasKit (локальный)

## 📚 Документация

### Основная документация
- [**Сборка и запуск**](docs/BUILD_AND_RUN.md) - Подробные инструкции по сборке и запуску
- [**Автономный режим**](docs/README_OFFLINE_MODE.md) - Настройка работы без интернета
- [**Решение проблем Flutter**](docs/FLUTTER_OFFLINE_SOLUTION.md) - Техническое решение автономного режима

### Техническая документация
- [**Настройка автономного режима**](docs/OFFLINE_MODE_SETUP.md) - Детальная настройка
- [**Схема базы данных**](docs/db_schema.md) - Структура БД
- [**План разработки**](docs/PLAN.md) - Общий план проекта

### Устранение неполадок
- [**Исправление SQLite**](docs/SQLITE_ARCHITECTURE_FIX.md) - Проблемы с SQLite
- [**Ручные исправления**](docs/MANUAL_FIX_INSTRUCTIONS.md) - Ручные исправления
- [**Windows 7**](docs/TROUBLESHOOTING_WINDOWS7.md) - Проблемы в Windows 7

## 🔧 Разработка

### Требования
- Dart SDK 3.3.0+
- Flutter SDK
- Windows (для production сборки)

### Запуск в режиме разработки
```bash
# Бэкенд
cd backend
dart run bin/server.dart

# Фронтенд (в отдельном терминале)
cd frontend
flutter run -d edge
```

## 📦 Развертывание

1. Запустите `build_production_en.bat`
2. Скопируйте папку `dist/` на целевой сервер
3. Запустите `hr_system.exe`

Приложение будет доступно на http://localhost:8080

## 🆘 Поддержка

При возникновении проблем:
1. Проверьте [документацию по устранению неполадок](docs/)
2. Убедитесь, что все файлы присутствуют в `dist/`
3. Проверьте логи сервера в консоли

## 📄 Лицензия

Проект разработан для внутреннего использования.
