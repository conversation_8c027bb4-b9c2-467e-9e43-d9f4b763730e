# Система для отдела кадров

## Описание

Система для отдела кадров представляет собой веб-приложение, разработанное для автоматизации работы отдела кадров. Система позволяет вести учет сотрудников, должностей, званий, приказов и других данных, необходимых для работы отдела кадров.

## Архитектура

Система разделена на две части:
- **Бэкенд**: Dart + Shelf + SQLite
- **Фронтенд**: Flutter Web

## Требования к системе

- Windows 7 или выше
- Dart SDK 3.7.2 или выше
- Flutter SDK 3.7.0 или выше
- Современный браузер (Chrome, Firefox, Edge) или Internet Explorer 11

## Установка и запуск

### Установка Dart и Flutter

1. Скачайте и установите Dart SDK с [официального сайта](https://dart.dev/get-dart)
2. Скачайте и установите Flutter SDK с [официального сайта](https://flutter.dev/docs/get-started/install)
3. Убедитесь, что Dart и Flutter добавлены в PATH

### Установка зависимостей

```bash
# Установка зависимостей бэкенда
cd backend
dart pub get

# Установка зависимостей фронтенда
cd ../frontend
flutter pub get
```

### Запуск в режиме разработки

```bash
# Запуск бэкенда
cd backend
dart run bin/server.dart

# Запуск фронтенда (в отдельном терминале)
cd frontend
flutter run -d chrome
```

### Сборка для продакшена

```bash
# Сборка фронтенда
cd frontend
flutter build web

# Копирование собранных файлов в директорию бэкенда
mkdir -p ../backend/web
cp -r build/web/* ../backend/web/

# Запуск сервера
cd ../backend
dart run bin/server.dart
```

## Основные функции

- Управление данными сотрудников
- Управление должностями и званиями
- Управление приказами и контрактами
- Формирование отчетов
- Поиск и фильтрация данных

## Структура проекта

```
project/
├── backend/                # Бэкенд на Dart + Shelf
│   ├── bin/                # Исполняемые файлы
│   │   └── server.dart     # Точка входа для запуска сервера
│   ├── lib/                # Библиотеки
│   │   ├── controllers/    # Контроллеры для обработки запросов
│   │   ├── models/         # Модели данных
│   │   ├── routes/         # Маршруты API
│   │   ├── database/       # Работа с базой данных
│   │   ├── middleware/     # Промежуточное ПО
│   │   └── utils/          # Вспомогательные функции
│   ├── migrations/         # Миграции базы данных
│   ├── test/               # Тесты
│   ├── web/                # Статические файлы фронтенда
│   └── pubspec.yaml        # Зависимости бэкенда
├── frontend/               # Фронтенд на Flutter
│   ├── lib/                # Исходный код
│   │   ├── components/     # Переиспользуемые компоненты
│   │   ├── pages/          # Страницы приложения
│   │   ├── services/       # Сервисы для работы с API
│   │   ├── utils/          # Вспомогательные функции
│   │   ├── models/         # Модели данных
│   │   ├── main.dart       # Точка входа
│   │   └── app.dart        # Корневой компонент
│   ├── test/               # Тесты
│   └── pubspec.yaml        # Зависимости фронтенда
├── task/                   # Задачи по разработке
└── README.md               # Документация проекта
```

## Документация

Подробная документация по API и использованию системы находится в директории `docs/`.

## Лицензия

Данный проект является внутренним и не подлежит распространению без согласия владельца.
