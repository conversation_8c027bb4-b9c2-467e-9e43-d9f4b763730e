# Task: Добавление функциональности для прикрепления .docx файлов к приказам

**Goal:** Реализовать возможность загрузки, хранения и просмотра файлов документов Microsoft Word (.docx), связанных с приказами в системе.

**Sub-tasks:**

1.  **[X] `task_1_create_main_task_file.md`**: Create this task tracking file.
    *   Status: Completed
2.  **[X] `task_2_create_order_attachment_repository.md`**: Создать репозиторий для работы с вложениями к приказам в файле `backend/lib/database/repositories/order_attachment_repository.dart`.
    *   Status: Completed
3.  **[X] `task_3_create_order_attachment_controller.md`**: Создать контроллер для обработки запросов к API вложений в файле `backend/lib/controllers/order_attachment_controller.dart`.
    *   Status: Completed
4.  **[X] `task_4_add_order_attachment_routes.md`**: Добавить маршруты для API вложений в файле `backend/lib/routes/order_attachment_routes.dart` и подключить их в `router.dart`.
    *   Status: Completed
5.  **[X] `task_5_create_order_attachment_service.md`**: Создать сервис для работы с API вложений на фронтенде в файле `frontend/lib/services/order_attachment_service.dart`.
    *   Status: Completed
6.  **[X] `task_6_create_order_attachment_provider.md`**: Создать провайдер для управления состоянием вложений на фронтенде в файле `frontend/lib/providers/order_attachment_provider.dart`.
    *   Status: Completed
7.  **[X] `task_7_update_order_details_page.md`**: Обновить страницу деталей приказа для отображения списка вложений и добавления новых вложений.
    *   Status: Completed
8.  **[X] `task_8_test_order_attachment_api.md`**: Создать и выполнить тесты для API вложений к приказам.
    *   Status: Completed

**Desired Outcome:**
*   Пользователь может загружать файлы .docx в качестве вложений к приказам
*   Файлы сохраняются в базе данных и связываются с конкретным приказом
*   Пользователь может просматривать список вложений к приказу
*   Пользователь может скачивать вложения к приказу
*   API предоставляет все необходимые эндпоинты для работы с вложениями
*   Интерфейс пользователя интуитивно понятен и удобен в использовании
