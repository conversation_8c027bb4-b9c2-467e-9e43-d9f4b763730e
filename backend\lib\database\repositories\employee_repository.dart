import 'package:backend/database/database.dart';
import 'package:backend/models/employee.dart';
import 'package:logging/logging.dart';

class EmployeeRepository {
  static final Logger _logger = Logger('EmployeeRepository');

  /// Получить всех сотрудников
  List<Employee> getAll() {
    _logger.info('Getting all employees');

    final result = AppDatabase.select('SELECT * FROM employees');

    return result.map((row) {
      return Employee(
        id: row['id'] as int,
        lastName: row['last_name'] as String,
        firstName: row['first_name'] as String,
        middleName: row['middle_name'] as String?,
        gender: row['gender'] as int,
        birthDate: row['birth_date'] as String?,
        placeOfBirth: row['place_of_birth'] as String?,
        nationality: row['nationality'] as String?,
        personalNumber: row['personal_number'] as String?,
        childrenUnder16: row['children_under_16'] as int?,
        academicDegree: row['academic_degree'] as String?,
        veteranSince: row['veteran_since'] as int?,
        positionId: row['position_id'] as int?,
        createdAt: row['created_at'] as String?,
        updatedAt: row['updated_at'] as String?,
      );
    }).toList();
  }

  /// Получить сотрудника по ID
  Employee? getById(int id) {
    _logger.info('Getting employee by ID: $id');

    final result = AppDatabase.select('SELECT * FROM employees WHERE id = ?', [
      id,
    ]);

    if (result.isEmpty) {
      return null;
    }

    final row = result.first;

    return Employee(
      id: row['id'] as int,
      lastName: row['last_name'] as String,
      firstName: row['first_name'] as String,
      middleName: row['middle_name'] as String?,
      gender: row['gender'] as int,
      birthDate: row['birth_date'] as String?,
      placeOfBirth: row['place_of_birth'] as String?,
      nationality: row['nationality'] as String?,
      personalNumber: row['personal_number'] as String?,
      childrenUnder16: row['children_under_16'] as int?,
      academicDegree: row['academic_degree'] as String?,
      veteranSince: row['veteran_since'] as int?,
      positionId: row['position_id'] as int?,
      createdAt: row['created_at'] as String?,
      updatedAt: row['updated_at'] as String?,
    );
  }

  /// Создать нового сотрудника
  int create(Employee employee) {
    _logger.info('Creating new employee: $employee');

    AppDatabase.execute(
      '''
      INSERT INTO employees (
        last_name, first_name, middle_name, gender, birth_date, place_of_birth,
        nationality, personal_number, children_under_16, academic_degree, veteran_since,
        position_id
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      ''',
      [
        employee.lastName,
        employee.firstName,
        employee.middleName,
        employee.gender,
        employee.birthDate,
        employee.placeOfBirth,
        employee.nationality,
        employee.personalNumber,
        employee.childrenUnder16,
        employee.academicDegree,
        employee.veteranSince,
        employee.positionId,
      ],
    );

    return AppDatabase.database.lastInsertRowId;
  }

  /// Обновить сотрудника
  bool update(Employee employee) {
    _logger.info('Updating employee: $employee');

    if (employee.id == null) {
      throw ArgumentError('Employee ID cannot be null');
    }

    AppDatabase.execute(
      '''
      UPDATE employees SET
        last_name = ?, first_name = ?, middle_name = ?, gender = ?, birth_date = ?,
        place_of_birth = ?, nationality = ?, personal_number = ?, children_under_16 = ?,
        academic_degree = ?, veteran_since = ?, position_id = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
      ''',
      [
        employee.lastName,
        employee.firstName,
        employee.middleName,
        employee.gender,
        employee.birthDate,
        employee.placeOfBirth,
        employee.nationality,
        employee.personalNumber,
        employee.childrenUnder16,
        employee.academicDegree,
        employee.veteranSince,
        employee.positionId,
        employee.id,
      ],
    );

    return AppDatabase.database.updatedRows > 0;
  }

  /// Удалить сотрудника
  bool delete(int id) {
    _logger.info('Deleting employee with ID: $id');

    AppDatabase.execute('DELETE FROM employees WHERE id = ?', [id]);

    return AppDatabase.database.updatedRows > 0;
  }

  /// Проверить, занята ли должность другим сотрудником
  Employee? getEmployeeByPositionId(int positionId, {int? excludeEmployeeId}) {
    _logger.info('Checking if position with ID $positionId is occupied');

    String query = 'SELECT * FROM employees WHERE position_id = ?';
    List<dynamic> params = [positionId];

    if (excludeEmployeeId != null) {
      query += ' AND id != ?';
      params.add(excludeEmployeeId);
    }

    final result = AppDatabase.select(query, params);

    if (result.isEmpty) {
      return null;
    }

    final row = result.first;

    return Employee(
      id: row['id'] as int,
      lastName: row['last_name'] as String,
      firstName: row['first_name'] as String,
      middleName: row['middle_name'] as String?,
      gender: row['gender'] as int,
      birthDate: row['birth_date'] as String?,
      placeOfBirth: row['place_of_birth'] as String?,
      nationality: row['nationality'] as String?,
      personalNumber: row['personal_number'] as String?,
      childrenUnder16: row['children_under_16'] as int?,
      academicDegree: row['academic_degree'] as String?,
      veteranSince: row['veteran_since'] as int?,
      positionId: row['position_id'] as int?,
      createdAt: row['created_at'] as String?,
      updatedAt: row['updated_at'] as String?,
    );
  }

  /// Обновить должность сотрудника
  bool updatePosition(int employeeId, int? positionId) {
    _logger.info('Updating position for employee with ID: $employeeId to position ID: $positionId');

    AppDatabase.execute(
      '''
      UPDATE employees SET
        position_id = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
      ''',
      [positionId, employeeId],
    );

    return AppDatabase.database.updatedRows > 0;
  }
}
