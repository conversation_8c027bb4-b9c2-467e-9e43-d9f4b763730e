// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'employee.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Employee _$EmployeeFromJson(Map<String, dynamic> json) => Employee(
      id: (json['id'] as num?)?.toInt(),
      lastName: json['lastName'] as String,
      firstName: json['firstName'] as String,
      middleName: json['middleName'] as String?,
      gender: (json['gender'] as num).toInt(),
      birthDate: json['birthDate'] as String?,
      placeOfBirth: json['placeOfBirth'] as String?,
      nationality: json['nationality'] as String?,
      personalNumber: json['personalNumber'] as String?,
      childrenUnder16: (json['childrenUnder16'] as num?)?.toInt(),
      academicDegree: json['academicDegree'] as String?,
      veteranSince: (json['veteranSince'] as num?)?.toInt(),
      positionId: json['positionId'] as int?,
      createdAt: json['createdAt'] as String?,
      updatedAt: json['updatedAt'] as String?,
    );

Map<String, dynamic> _$EmployeeToJson(Employee instance) => <String, dynamic>{
      'id': instance.id,
      'lastName': instance.lastName,
      'firstName': instance.firstName,
      'middleName': instance.middleName,
      'gender': instance.gender,
      'birthDate': instance.birthDate,
      'placeOfBirth': instance.placeOfBirth,
      'nationality': instance.nationality,
      'personalNumber': instance.personalNumber,
      'childrenUnder16': instance.childrenUnder16,
      'academicDegree': instance.academicDegree,
      'veteranSince': instance.veteranSince,
      'positionId': instance.positionId,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
    };
