// ignore_for_file: use_build_context_synchronously, avoid_print, unused_element
import 'package:flutter/material.dart';
import 'package:frontend/models/employee.dart';
import 'package:frontend/models/position.dart';
import 'package:frontend/models/service_history.dart';
import 'package:frontend/providers/employee_provider.dart';
import 'package:frontend/providers/position_provider.dart';
import 'package:frontend/providers/service_history_provider.dart';
import 'package:frontend/components/app_drawer.dart';
import 'package:frontend/widgets/position_assignment_dialog.dart';
import 'package:frontend/widgets/position_search_dialog.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

class EmployeeDetailPage extends StatefulWidget {
  final String employeeId;

  const EmployeeDetailPage({Key? key, required this.employeeId})
      : super(key: key);

  @override
  State<EmployeeDetailPage> createState() => _EmployeeDetailPageState();
}

class _EmployeeDetailPageState extends State<EmployeeDetailPage> {
  bool _isLoading = true;
  Employee? _employee;
  Position? _currentPosition;
  List<ServiceHistory> _serviceHistory = [];
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Загружаем данные о сотруднике
      final employeeId = int.parse(widget.employeeId);
      final employee =
          await Provider.of<EmployeeProvider>(context, listen: false)
              .getEmployee(employeeId);

      if (employee == null) {
        setState(() {
          _isLoading = false;
          _error = 'Сотрудник не найден';
        });
        return;
      }

      // Загружаем данные о должностях
      await Provider.of<PositionProvider>(context, listen: false)
          .fetchPositions();

      // Загружаем послужной список
      await Provider.of<ServiceHistoryProvider>(context, listen: false)
          .fetchByEmployeeId(employeeId);
      final serviceHistory =
          Provider.of<ServiceHistoryProvider>(context, listen: false).records;

      // Определяем текущую должность
      Position? currentPosition;
      if (employee.positionId != null) {
        final positions =
            Provider.of<PositionProvider>(context, listen: false).positions;
        // Выводим отладочную информацию
        print('Employee positionId: ${employee.positionId}');
        print(
            'Available positions: ${positions.map((p) => '${p.id}: ${p.title}').join(', ')}');

        // Ищем должность по ID
        try {
          // Проверяем, есть ли должность с таким ID
          final matchingPositions =
              positions.where((p) => p.id == employee.positionId).toList();

          if (matchingPositions.isNotEmpty) {
            currentPosition = matchingPositions.first;
            print('Found position: ${currentPosition.title}');
          } else {
            // Если должность не найдена в списке, пробуем получить её напрямую
            final positionProvider =
                Provider.of<PositionProvider>(context, listen: false);
            final position =
                await positionProvider.getPosition(employee.positionId!);

            if (position != null) {
              currentPosition = position;
              print('Fetched position directly: ${currentPosition.title}');
            } else {
              currentPosition = Position(
                  title: 'Неизвестная должность (ID: ${employee.positionId})');
              print('Position not found even with direct fetch');
            }
          }
        } catch (e) {
          print('Error finding position: $e');
          currentPosition = Position(
              title: 'Неизвестная должность (ID: ${employee.positionId})');
        }
      } else {
        print('Employee has no positionId');
      }

      setState(() {
        _employee = employee;
        _currentPosition = currentPosition;
        _serviceHistory = serviceHistory;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = e.toString();
      });
    }
  }

  Future<void> _showPositionAssignmentDialog() async {
    if (_employee == null) return;

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => PositionAssignmentDialog(
        employee: _employee!,
        currentPositionId: _employee!.positionId,
      ),
    );

    if (result == true) {
      // Перезагружаем данные после назначения на должность
      _loadData();
    }
  }

  Future<void> _showPositionSearchDialog() async {
    if (_employee == null) return;

    final positions =
        Provider.of<PositionProvider>(context, listen: false).positions;
    final selectedId = await showDialog<int>(
      context: context,
      builder: (_) => PositionSearchDialog(
        positions: positions,
        currentPositionId: _employee!.positionId,
      ),
    );
    if (selectedId != null) {
      _showPositionAssignmentDialogWithPosition(selectedId);
    }
  }

  // Показать диалог назначения на должность с предварительно выбранной должностью
  Future<void> _showPositionAssignmentDialogWithPosition(int positionId) async {
    if (_employee == null) return;

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => PositionAssignmentDialog(
        employee: _employee!,
        currentPositionId: _employee!.positionId,
        preselectedPositionId: positionId,
      ),
    );

    if (result == true) {
      // Перезагружаем данные после назначения на должность
      _loadData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_employee != null
            ? 'Сотрудник: ${_employee!.fullName}'
            : 'Детали сотрудника'),
        actions: [
          if (_employee != null)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () {
                context.go('/employees/${_employee!.id}/edit');
              },
            ),
        ],
      ),
      drawer: const AppDrawer(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(
                  child: Text(
                    'Ошибка: $_error',
                    style: const TextStyle(color: Colors.red),
                  ),
                )
              : _employee == null
                  ? const Center(child: Text('Сотрудник не найден'))
                  : SingleChildScrollView(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildEmployeeInfo(),
                          const SizedBox(height: 24),
                          _buildPositionInfo(),
                          const SizedBox(height: 24),
                          _buildServiceHistory(),
                        ],
                      ),
                    ),
    );
  }

  Widget _buildEmployeeInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Основная информация',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildInfoRow('ФИО', _employee!.fullName),
            _buildInfoRow('Пол', _employee!.genderText),
            if (_employee!.birthDate != null)
              _buildInfoRow('Дата рождения', _employee!.birthDate!),
            if (_employee!.placeOfBirth != null)
              _buildInfoRow('Место рождения', _employee!.placeOfBirth!),
            if (_employee!.nationality != null)
              _buildInfoRow('Национальность', _employee!.nationality!),
            if (_employee!.personalNumber != null)
              _buildInfoRow('Личный номер', _employee!.personalNumber!),
            if (_employee!.childrenUnder16 != null)
              _buildInfoRow(
                  'Дети до 16 лет', _employee!.childrenUnder16.toString()),
            if (_employee!.academicDegree != null)
              _buildInfoRow('Ученая степень', _employee!.academicDegree!),
            if (_employee!.veteranSince != null)
              _buildInfoRow('Ветеран с', _employee!.veteranSince.toString()),
          ],
        ),
      ),
    );
  }

  Widget _buildPositionInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Текущая должность',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                Row(
                  children: [
                    if (_employee != null)
                      Padding(
                        padding: const EdgeInsets.only(right: 8.0),
                        child: OutlinedButton.icon(
                          icon: const Icon(Icons.search),
                          label: const Text('Найти и назначить'),
                          onPressed: _showPositionSearchDialog,
                        ),
                      ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            _currentPosition != null
                ? _buildInfoRow('Должность', _currentPosition!.title)
                : const Text('Не назначен на должность'),
          ],
        ),
      ),
    );
  }

  Widget _buildServiceHistory() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Послужной список',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _serviceHistory.isEmpty
                ? const Text('Нет записей в послужном списке')
                : ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: _serviceHistory.length,
                    itemBuilder: (context, index) {
                      final record = _serviceHistory[index];
                      final position = record.positionId != null
                          ? Provider.of<PositionProvider>(context,
                                  listen: false)
                              .positions
                              .firstWhere(
                                (p) => p.id == record.positionId,
                                orElse: () =>
                                    Position(title: 'Неизвестная должность'),
                              )
                          : null;

                      return ListTile(
                        title: Text(position?.title ??
                            record.externalPositionTitle ??
                            'Неизвестная должность'),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Начало: ${record.startDate}'),
                            if (record.endDate != null)
                              Text('Окончание: ${record.endDate}'),
                          ],
                        ),
                        trailing: record.endDate == null
                            ? const Chip(
                                label: Text('Активная'),
                                backgroundColor: Colors.green,
                              )
                            : null,
                      );
                    },
                  ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 150,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }
}
