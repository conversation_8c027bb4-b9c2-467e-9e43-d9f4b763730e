# Подзадача: Улучшение адаптивности интерфейса для различных размеров экрана

## Описание
Необходимо улучшить адаптивность пользовательского интерфейса для обеспечения комфортной работы с системой на устройствах с различными размерами экрана, от небольших ноутбуков до больших мониторов. Это особенно важно для системы отдела кадров, которая может использоваться на различных рабочих местах.

## Текущее состояние
- Интерфейс не полностью адаптирован для различных размеров экрана
- На маленьких экранах некоторые элементы могут выходить за границы видимой области
- На больших экранах не эффективно используется доступное пространство
- Отсутствует оптимизация для различных соотношений сторон экрана

## Необходимые изменения
1. Создать утилиты для работы с адаптивным дизайном:
   - `frontend/lib/utils/responsive_utils.dart` - утилиты для определения размера экрана и адаптации интерфейса

2. Создать адаптивные компоненты:
   - `frontend/lib/components/responsive_scaffold.dart` - адаптивный каркас для страниц
   - `frontend/lib/components/responsive_data_table.dart` - адаптивная таблица данных
   - `frontend/lib/components/responsive_form.dart` - адаптивная форма

3. Обновить существующие страницы для использования адаптивных компонентов:
   - Страницы списков (сотрудники, должности, приказы, послужной список)
   - Страницы деталей
   - Страницы форм

## Детали реализации
1. Утилиты для адаптивного дизайна (`responsive_utils.dart`):
   - Определение размера экрана (маленький, средний, большой, очень большой)
   - Определение ориентации экрана (портретная, альбомная)
   - Вспомогательные методы для адаптации размеров и отступов

2. Адаптивный каркас (`responsive_scaffold.dart`):
   - Автоматическое изменение расположения боковой панели в зависимости от размера экрана
   - На маленьких экранах - выдвижная панель
   - На больших экранах - постоянно видимая боковая панель
   - Адаптивные отступы и размеры элементов

3. Адаптивная таблица данных (`responsive_data_table.dart`):
   - На маленьких экранах - вертикальное отображение данных в виде карточек
   - На средних экранах - таблица с прокруткой
   - На больших экранах - полноценная таблица
   - Автоматическое скрытие менее важных столбцов на маленьких экранах

4. Адаптивная форма (`responsive_form.dart`):
   - На маленьких экранах - вертикальное расположение полей
   - На больших экранах - расположение полей в несколько колонок
   - Адаптивные размеры полей ввода
   - Автоматическое изменение расположения кнопок

## Адаптация для различных размеров экрана
1. Маленькие экраны (< 600px):
   - Вертикальное расположение элементов
   - Выдвижная боковая панель
   - Упрощенное отображение таблиц (в виде карточек)
   - Одна колонка для форм

2. Средние экраны (600px - 1200px):
   - Комбинированное расположение элементов
   - Выдвижная или постоянная боковая панель (в зависимости от доступного пространства)
   - Таблицы с прокруткой
   - Одна или две колонки для форм

3. Большие экраны (> 1200px):
   - Горизонтальное расположение элементов
   - Постоянная боковая панель
   - Полноценные таблицы
   - Две или три колонки для форм

## Ожидаемый результат
- Улучшенная адаптивность интерфейса для различных размеров экрана
- Комфортная работа с системой на устройствах с различными размерами экрана
- Эффективное использование доступного пространства
- Единый подход к адаптивности во всем приложении
