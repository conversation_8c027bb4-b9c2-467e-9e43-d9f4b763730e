import 'package:backend/database/database.dart';
import 'package:backend/models/service_history.dart';
import 'package:logging/logging.dart';

class ServiceHistoryRepository {
  static final Logger _logger = Logger('ServiceHistoryRepository');

  /// Получить все записи послужного списка
  List<ServiceHistory> getAll() {
    _logger.info('Getting all service history records');

    final result = AppDatabase.select('SELECT * FROM service_history');

    return result.map((row) {
      return ServiceHistory(
        id: row['id'] as int,
        employeeId: row['employee_id'] as int,
        positionId: row['position_id'] as int?,
        externalPositionTitle: row['external_position_title'] as String?,
        startDate: row['start_date'] as String,
        endDate: row['end_date'] as String?,
        acceptanceDate: row['acceptance_date'] as String?,
        handoverDate: row['handover_date'] as String?,
        orderId: row['order_id'] as int?,
        externalOrderInfo: row['external_order_info'] as String?,
        notes: row['notes'] as String?,
        createdAt: row['created_at'] as String?,
        updatedAt: row['updated_at'] as String?,
      );
    }).toList();
  }

  /// Получить запись послужного списка по ID
  ServiceHistory? getById(int id) {
    _logger.info('Getting service history record by ID: $id');

    final result =
        AppDatabase.select('SELECT * FROM service_history WHERE id = ?', [
      id,
    ]);

    if (result.isEmpty) {
      return null;
    }

    final row = result.first;

    return ServiceHistory(
      id: row['id'] as int,
      employeeId: row['employee_id'] as int,
      positionId: row['position_id'] as int?,
      externalPositionTitle: row['external_position_title'] as String?,
      startDate: row['start_date'] as String,
      endDate: row['end_date'] as String?,
      acceptanceDate: row['acceptance_date'] as String?,
      handoverDate: row['handover_date'] as String?,
      orderId: row['order_id'] as int?,
      externalOrderInfo: row['external_order_info'] as String?,
      notes: row['notes'] as String?,
      createdAt: row['created_at'] as String?,
      updatedAt: row['updated_at'] as String?,
    );
  }

  /// Получить записи послужного списка сотрудника
  List<ServiceHistory> getByEmployeeId(int employeeId) {
    _logger
        .info('Getting service history records for employee ID: $employeeId');

    final result = AppDatabase.select(
        'SELECT * FROM service_history WHERE employee_id = ?', [
      employeeId,
    ]);

    return result.map((row) {
      return ServiceHistory(
        id: row['id'] as int,
        employeeId: row['employee_id'] as int,
        positionId: row['position_id'] as int?,
        externalPositionTitle: row['external_position_title'] as String?,
        startDate: row['start_date'] as String,
        endDate: row['end_date'] as String?,
        acceptanceDate: row['acceptance_date'] as String?,
        handoverDate: row['handover_date'] as String?,
        orderId: row['order_id'] as int?,
        externalOrderInfo: row['external_order_info'] as String?,
        notes: row['notes'] as String?,
        createdAt: row['created_at'] as String?,
        updatedAt: row['updated_at'] as String?,
      );
    }).toList();
  }

  /// Создать новую запись послужного списка
  int create(ServiceHistory serviceHistory) {
    _logger.info('Creating new service history record: $serviceHistory');

    AppDatabase.execute(
      '''
      INSERT INTO service_history (
        employee_id, position_id, external_position_title, start_date, end_date,
        acceptance_date, handover_date, order_id, external_order_info, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      ''',
      [
        serviceHistory.employeeId,
        serviceHistory.positionId,
        serviceHistory.externalPositionTitle,
        serviceHistory.startDate,
        serviceHistory.endDate,
        serviceHistory.acceptanceDate,
        serviceHistory.handoverDate,
        serviceHistory.orderId,
        serviceHistory.externalOrderInfo,
        serviceHistory.notes,
      ],
    );

    return AppDatabase.database.lastInsertRowId;
  }

  /// Обновить запись послужного списка
  bool update(ServiceHistory serviceHistory) {
    _logger.info('Updating service history record: $serviceHistory');

    if (serviceHistory.id == null) {
      throw ArgumentError('Service history ID cannot be null');
    }

    AppDatabase.execute(
      '''
      UPDATE service_history SET
        employee_id = ?, position_id = ?, external_position_title = ?, start_date = ?, end_date = ?,
        acceptance_date = ?, handover_date = ?, order_id = ?, external_order_info = ?, notes = ?,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
      ''',
      [
        serviceHistory.employeeId,
        serviceHistory.positionId,
        serviceHistory.externalPositionTitle,
        serviceHistory.startDate,
        serviceHistory.endDate,
        serviceHistory.acceptanceDate,
        serviceHistory.handoverDate,
        serviceHistory.orderId,
        serviceHistory.externalOrderInfo,
        serviceHistory.notes,
        serviceHistory.id,
      ],
    );

    return AppDatabase.database.updatedRows > 0;
  }

  /// Удалить запись послужного списка
  bool delete(int id) {
    _logger.info('Deleting service history record with ID: $id');

    AppDatabase.execute('DELETE FROM service_history WHERE id = ?', [id]);

    return AppDatabase.database.updatedRows > 0;
  }

  /// Получить активные записи послужного списка по ID должности
  List<ServiceHistory> getActiveByPositionId(int positionId) {
    _logger.info('Getting active service history for position ID: $positionId');

    final result = AppDatabase.select(
      'SELECT * FROM service_history WHERE position_id = ? AND end_date IS NULL',
      [positionId],
    );

    return result.map((row) {
      return ServiceHistory(
        id: row['id'] as int,
        employeeId: row['employee_id'] as int,
        positionId: row['position_id'] as int?,
        externalPositionTitle: row['external_position_title'] as String?,
        startDate: row['start_date'] as String,
        endDate: row['end_date'] as String?,
        acceptanceDate: row['acceptance_date'] as String?,
        handoverDate: row['handover_date'] as String?,
        orderId: row['order_id'] as int?,
        externalOrderInfo: row['external_order_info'] as String?,
        notes: row['notes'] as String?,
        createdAt: row['created_at'] as String?,
        updatedAt: row['updated_at'] as String?,
      );
    }).toList();
  }
}
