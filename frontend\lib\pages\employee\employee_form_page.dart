// ignore_for_file: use_build_context_synchronously
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:frontend/models/employee.dart';
import 'package:frontend/models/position.dart';
import 'package:frontend/models/service_history.dart';
import 'package:frontend/providers/employee_provider.dart';
import 'package:frontend/providers/position_provider.dart';
import 'package:frontend/providers/service_history_provider.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:frontend/widgets/position_assignment_dialog.dart';
import 'package:frontend/widgets/position_search_dialog.dart';
import 'package:frontend/widgets/service_history_form_dialog.dart';
import 'package:frontend/services/service_history_service.dart';

class EmployeeFormPage extends StatefulWidget {
  final int? employeeId;

  const EmployeeFormPage({super.key, this.employeeId});

  @override
  State<EmployeeFormPage> createState() => _EmployeeFormPageState();
}

class _EmployeeFormPageState extends State<EmployeeFormPage> {
  final _formKey = GlobalKey<FormBuilderState>();
  bool _isLoading = false;
  bool _isEdit = false;
  Employee? _employee;
  List<Position> _positions = [];
  int? _currentPositionId;
  ServiceHistory? _pendingServiceHistory;

  @override
  void initState() {
    super.initState();
    _isEdit = widget.employeeId != null;

    // Используем addPostFrameCallback для отложенного вызова методов загрузки данных
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadPositions();
      if (_isEdit) {
        _loadEmployee();
      }
    });
  }

  Future<void> _loadPositions() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await Provider.of<PositionProvider>(context, listen: false)
          .fetchPositions();

      setState(() {
        _positions =
            Provider.of<PositionProvider>(context, listen: false).positions;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Ошибка при загрузке должностей: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadEmployee() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final employee =
          await Provider.of<EmployeeProvider>(context, listen: false)
              .getEmployee(widget.employeeId!);

      setState(() {
        _employee = employee;
        _isLoading = false;
      });

      // После загрузки сотрудника загружаем его текущую должность
      await _loadCurrentPosition();

      // Примечание: мы больше не используем patchValue здесь,
      // так как данные будут установлены через initialValue в FormBuilder
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Ошибка при загрузке данных: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadCurrentPosition() async {
    if (!_isEdit || widget.employeeId == null) return;

    try {
      // Загружаем послужной список сотрудника
      final serviceHistoryProvider =
          Provider.of<ServiceHistoryProvider>(context, listen: false);
      await serviceHistoryProvider.fetchByEmployeeId(widget.employeeId!);

      // Ищем активную запись (без даты окончания)
      final activeRecords = serviceHistoryProvider.records
          .where(
              (record) => record.endDate == null && record.positionId != null)
          .toList();

      if (activeRecords.isNotEmpty) {
        // Берем первую активную запись
        setState(() {
          _currentPositionId = activeRecords.first.positionId;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Ошибка при загрузке должности: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _saveEmployee() async {
    if (_formKey.currentState?.saveAndValidate() ?? false) {
      setState(() {
        _isLoading = true;
      });

      final formData = _formKey.currentState!.value;

      final employee = Employee(
        id: _isEdit ? widget.employeeId : null,
        lastName: formData['lastName'],
        firstName: formData['firstName'],
        middleName: formData['middleName'],
        gender: formData['gender'],
        birthDate: formData['birthDate'] != null
            ? DateFormat('yyyy-MM-dd').format(formData['birthDate'])
            : null,
        placeOfBirth: formData['placeOfBirth'],
        nationality: formData['nationality'],
        personalNumber: formData['personalNumber'],
        childrenUnder16: formData['childrenUnder16'] != null &&
                formData['childrenUnder16'].isNotEmpty
            ? int.tryParse(formData['childrenUnder16'])
            : null,
        academicDegree: formData['academicDegree'],
        veteranSince: formData['veteranSince'] != null &&
                formData['veteranSince'].isNotEmpty
            ? int.tryParse(formData['veteranSince'])
            : null,
      );

      try {
        bool success;
        int? employeeId;

        if (_isEdit) {
          success = await Provider.of<EmployeeProvider>(context, listen: false)
              .updateEmployee(employee);
          employeeId = widget.employeeId;
        } else {
          success = await Provider.of<EmployeeProvider>(context, listen: false)
              .createEmployee(employee);

          // Получаем ID созданного сотрудника
          if (success) {
            final employees =
                Provider.of<EmployeeProvider>(context, listen: false).employees;
            if (employees.isNotEmpty) {
              final createdEmployee = employees.firstWhere(
                (e) =>
                    e.lastName == employee.lastName &&
                    e.firstName == employee.firstName &&
                    e.middleName == employee.middleName,
                orElse: () => employee,
              );
              employeeId = createdEmployee.id;
            }
          }
        }

        // Если есть данные о назначении из диалога, создаём запись в послужном списке при создании
        final serviceHistoryProvider =
            Provider.of<ServiceHistoryProvider>(context, listen: false);
        if (!_isEdit &&
            success &&
            employeeId != null &&
            _pendingServiceHistory != null) {
          final ph = _pendingServiceHistory!;
          final toCreate = ServiceHistory(
            employeeId: employeeId,
            positionId: ph.positionId,
            externalPositionTitle: ph.externalPositionTitle,
            startDate: ph.startDate,
            endDate: ph.endDate,
            acceptanceDate: ph.acceptanceDate,
            handoverDate: ph.handoverDate,
            orderId: ph.orderId,
            externalOrderInfo: ph.externalOrderInfo,
            notes: ph.notes,
          );
          await serviceHistoryProvider.create(toCreate);
          _pendingServiceHistory = null;
        }
        // Для редактирования сохраняем старую логику
        if (_isEdit &&
            success &&
            employeeId != null &&
            _currentPositionId != null) {
          final positionId = _currentPositionId;
          final today = DateFormat('yyyy-MM-dd').format(DateTime.now());
          // Проверяем, изменилась ли должность
          if (_currentPositionId != positionId) {
            // Закрываем текущую запись и создаем новую
            await serviceHistoryProvider.fetchByEmployeeId(employeeId);
            final activeRecords = serviceHistoryProvider.records
                .where((record) =>
                    record.endDate == null && record.positionId != null)
                .toList();
            if (activeRecords.isNotEmpty) {
              final currentRecord = activeRecords.first;
              final updatedRecord = ServiceHistory(
                id: currentRecord.id,
                employeeId: currentRecord.employeeId,
                positionId: currentRecord.positionId,
                externalPositionTitle: currentRecord.externalPositionTitle,
                startDate: currentRecord.startDate,
                endDate: today,
                acceptanceDate: currentRecord.acceptanceDate,
                handoverDate: today,
                orderId: currentRecord.orderId,
                externalOrderInfo: currentRecord.externalOrderInfo,
                notes: currentRecord.notes,
              );
              await serviceHistoryProvider.update(updatedRecord);
            }
            final newRecord = ServiceHistory(
              employeeId: employeeId,
              positionId: positionId,
              startDate: today,
              acceptanceDate: today,
            );
            await serviceHistoryProvider.create(newRecord);
          }
        }

        setState(() {
          _isLoading = false;
        });

        if (success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Сотрудник успешно ${_isEdit ? 'обновлен' : 'создан'}${_currentPositionId != null ? ' и назначен на должность' : ''}'),
            ),
          );
          context.go('/employees');
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Ошибка при ${_isEdit ? 'обновлении' : 'создании'} сотрудника: ${Provider.of<EmployeeProvider>(context, listen: false).error}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        setState(() {
          _isLoading = false;
        });
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Ошибка: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEdit ? 'Редактирование сотрудника' : 'Новый сотрудник'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/employees'),
        ),
      ),
      body: _isLoading && _isEdit
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: FormBuilder(
                key: _formKey,
                initialValue: _isEdit && _employee != null
                    ? {
                        'lastName': _employee!.lastName,
                        'firstName': _employee!.firstName,
                        'middleName': _employee!.middleName ?? '',
                        'gender': _employee!.gender,
                        'birthDate': _employee!.birthDate != null
                            ? DateTime.parse(_employee!.birthDate!)
                            : null,
                        'placeOfBirth': _employee!.placeOfBirth ?? '',
                        'nationality': _employee!.nationality ?? '',
                        'personalNumber': _employee!.personalNumber ?? '',
                        'childrenUnder16':
                            _employee!.childrenUnder16?.toString() ?? '',
                        'academicDegree': _employee!.academicDegree ?? '',
                        'veteranSince':
                            _employee!.veteranSince?.toString() ?? '',
                        'positionId': _currentPositionId,
                      }
                    : {},
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Основная информация',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    FormBuilderTextField(
                      name: 'lastName',
                      decoration: const InputDecoration(
                        labelText: 'Фамилия',
                        border: OutlineInputBorder(),
                      ),
                      validator: FormBuilderValidators.compose([
                        FormBuilderValidators.required(
                            errorText: 'Поле обязательно для заполнения'),
                      ]),
                    ),
                    const SizedBox(height: 16),
                    FormBuilderTextField(
                      name: 'firstName',
                      decoration: const InputDecoration(
                        labelText: 'Имя',
                        border: OutlineInputBorder(),
                      ),
                      validator: FormBuilderValidators.compose([
                        FormBuilderValidators.required(
                            errorText: 'Поле обязательно для заполнения'),
                      ]),
                    ),
                    const SizedBox(height: 16),
                    FormBuilderTextField(
                      name: 'middleName',
                      decoration: const InputDecoration(
                        labelText: 'Отчество',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    FormBuilderDropdown<int>(
                      name: 'gender',
                      decoration: const InputDecoration(
                        labelText: 'Пол',
                        border: OutlineInputBorder(),
                      ),
                      initialValue: 1,
                      items: const [
                        DropdownMenuItem(
                          value: 1,
                          child: Text('Мужской'),
                        ),
                        DropdownMenuItem(
                          value: 0,
                          child: Text('Женский'),
                        ),
                      ],
                      validator: FormBuilderValidators.compose([
                        FormBuilderValidators.required(
                            errorText: 'Поле обязательно для заполнения'),
                      ]),
                    ),
                    const SizedBox(height: 16),
                    FormBuilderDateTimePicker(
                      name: 'birthDate',
                      inputType: InputType.date,
                      format: DateFormat('dd.MM.yyyy'),
                      decoration: const InputDecoration(
                        labelText: 'Дата рождения',
                        border: OutlineInputBorder(),
                        suffixIcon: Icon(Icons.calendar_today),
                      ),
                    ),
                    const SizedBox(height: 16),
                    FormBuilderTextField(
                      name: 'placeOfBirth',
                      decoration: const InputDecoration(
                        labelText: 'Место рождения',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    FormBuilderTextField(
                      name: 'nationality',
                      decoration: const InputDecoration(
                        labelText: 'Национальность',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 24),
                    const Text(
                      'Служебная информация',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    // Блок для назначения должности через поиск и диалог назначения
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  'Текущая должность',
                                  style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold),
                                ),
                                // Кнопка поиска и назначения доступна всегда
                                OutlinedButton.icon(
                                  icon: const Icon(Icons.search),
                                  label: const Text('Найти и назначить'),
                                  onPressed: _showPositionSearchDialogForForm,
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              _currentPositionId != null
                                  ? _positions
                                      .firstWhere(
                                        (p) => p.id == _currentPositionId,
                                        orElse: () => Position(
                                            title: 'Неизвестная должность'),
                                      )
                                      .fullTitle
                                  : 'Не назначена должность',
                              style: const TextStyle(fontSize: 14),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    FormBuilderTextField(
                      name: 'personalNumber',
                      decoration: const InputDecoration(
                        labelText: 'Личный номер',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    FormBuilderTextField(
                      name: 'childrenUnder16',
                      decoration: const InputDecoration(
                        labelText: 'Количество детей до 16 лет',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      validator: FormBuilderValidators.compose([
                        FormBuilderValidators.numeric(
                            errorText: 'Введите число'),
                      ]),
                    ),
                    const SizedBox(height: 16),
                    FormBuilderTextField(
                      name: 'academicDegree',
                      decoration: const InputDecoration(
                        labelText: 'Ученая степень',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    FormBuilderTextField(
                      name: 'veteranSince',
                      decoration: const InputDecoration(
                        labelText: 'Год получения статуса ветерана',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      validator: FormBuilderValidators.compose([
                        FormBuilderValidators.numeric(
                            errorText: 'Введите число'),
                      ]),
                    ),
                    const SizedBox(height: 24),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _saveEmployee,
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Text(
                            _isLoading
                                ? 'Сохранение...'
                                : _isEdit
                                    ? 'Сохранить изменения'
                                    : 'Создать сотрудника',
                            style: const TextStyle(fontSize: 16),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  Future<void> _showPositionSearchDialogForForm() async {
    // Сначала выбираем должность
    final selectedId = await showDialog<int>(
      context: context,
      builder: (_) => PositionSearchDialog(
        positions: _positions,
        currentPositionId: _currentPositionId,
      ),
    );
    if (selectedId != null) {
      // Найденная должность
      final selectedPosition = _positions.firstWhere((p) => p.id == selectedId);
      if (_isEdit && _employee != null) {
        // Редактирование: открываем диалог назначения
        final result = await showDialog<bool>(
          context: context,
          builder: (_) => PositionAssignmentDialog(
            employee: _employee!,
            currentPositionId: _currentPositionId,
            preselectedPositionId: selectedId,
          ),
        );
        if (result == true) {
          await _loadCurrentPosition();
        }
      } else {
        // Создание: сначала проверяем, занята ли уже должность
        try {
          final occupied =
              await ServiceHistoryService().isPositionOccupied(selectedId);
          if (occupied) {
            // Показываем ошибку
            await showDialog<void>(
              context: context,
              builder: (context) => AlertDialog(
                title: const Text('Должность занята'),
                content:
                    const Text('Эта должность уже занята другим сотрудником.'),
                actions: [
                  TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('ОК')),
                ],
              ),
            );
            return;
          }
        } catch (e) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Ошибка при проверке занятости: $e')),
          );
          return;
        }
        // Открываем диалог ввода данных назначения (без API вызова)
        final serviceHistory = await showDialog<ServiceHistory>(
          context: context,
          builder: (_) => ServiceHistoryFormDialog(position: selectedPosition),
        );
        if (serviceHistory != null) {
          setState(() {
            _pendingServiceHistory = serviceHistory;
            _currentPositionId = selectedId;
          });
        }
      }
    }
  }
}
