class Employee {
  final int? id;
  final String lastName;
  final String firstName;
  final String? middleName;
  final int gender;
  final String? birthDate;
  final String? placeOfBirth;
  final String? nationality;
  final String? personalNumber;
  final int? childrenUnder16;
  final String? academicDegree;
  final int? veteranSince;
  final int? positionId;
  final String? createdAt;
  final String? updatedAt;

  Employee({
    this.id,
    required this.lastName,
    required this.firstName,
    this.middleName,
    required this.gender,
    this.birthDate,
    this.placeOfBirth,
    this.nationality,
    this.personalNumber,
    this.childrenUnder16,
    this.academicDegree,
    this.veteranSince,
    this.positionId,
    this.createdAt,
    this.updatedAt,
  });

  factory Employee.fromJson(Map<String, dynamic> json) {
    return Employee(
      id: json['id'],
      lastName: json['lastName'],
      firstName: json['firstName'],
      middleName: json['middleName'],
      gender: json['gender'],
      birthDate: json['birthDate'],
      placeOfBirth: json['placeOfBirth'],
      nationality: json['nationality'],
      personalNumber: json['personalNumber'],
      childrenUnder16: json['childrenUnder16'],
      academicDegree: json['academicDegree'],
      veteranSince: json['veteranSince'],
      positionId: json['positionId'],
      createdAt: json['createdAt'],
      updatedAt: json['updatedAt'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'lastName': lastName,
      'firstName': firstName,
      'middleName': middleName,
      'gender': gender,
      'birthDate': birthDate,
      'placeOfBirth': placeOfBirth,
      'nationality': nationality,
      'personalNumber': personalNumber,
      'childrenUnder16': childrenUnder16,
      'academicDegree': academicDegree,
      'veteranSince': veteranSince,
      'positionId': positionId,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  String get fullName {
    if (middleName != null && middleName!.isNotEmpty) {
      return '$lastName $firstName $middleName';
    }
    return '$lastName $firstName';
  }

  String get genderText {
    return gender == 1 ? 'Мужской' : 'Женский';
  }

  Employee copyWith({
    int? id,
    String? lastName,
    String? firstName,
    String? middleName,
    int? gender,
    String? birthDate,
    String? placeOfBirth,
    String? nationality,
    String? personalNumber,
    int? childrenUnder16,
    String? academicDegree,
    int? veteranSince,
    int? positionId,
    String? createdAt,
    String? updatedAt,
  }) {
    return Employee(
      id: id ?? this.id,
      lastName: lastName ?? this.lastName,
      firstName: firstName ?? this.firstName,
      middleName: middleName ?? this.middleName,
      gender: gender ?? this.gender,
      birthDate: birthDate ?? this.birthDate,
      placeOfBirth: placeOfBirth ?? this.placeOfBirth,
      nationality: nationality ?? this.nationality,
      personalNumber: personalNumber ?? this.personalNumber,
      childrenUnder16: childrenUnder16 ?? this.childrenUnder16,
      academicDegree: academicDegree ?? this.academicDegree,
      veteranSince: veteranSince ?? this.veteranSince,
      positionId: positionId ?? this.positionId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
