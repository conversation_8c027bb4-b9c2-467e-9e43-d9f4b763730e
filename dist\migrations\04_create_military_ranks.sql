-- Создание таблицы воинских званий
CREATE TABLE IF NOT EXISTS military_ranks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    employee_id INTEGER NOT NULL,
    rank_type_id INTEGER NOT NULL,
    date_assigned TEXT,
    order_id INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOR<PERSON>G<PERSON> KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIG<PERSON> KEY (rank_type_id) REFERENCES rank_types(id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOR<PERSON><PERSON><PERSON> KEY (order_id) REFERENCES orders(id) ON DELETE SET NULL ON UPDATE CASCADE
);
