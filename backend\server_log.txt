Starting server initialization...
Initializing logger...
<PERSON><PERSON> initialized successfully
Checking directories...
Executable path: C:\slujeb\BD_OK\backend\bin\server.exe
Executable directory: C:\slujeb\BD_OK\backend\bin
Root directory: C:\slujeb\BD_OK\backend
DB directory path: C:\slujeb\BD_OK\backend\db
Migrations directory path: C:\slujeb\BD_OK\backend\migrations
db directory already exists
migrations directory already exists
Migrations directory contents:
  C:\slujeb\BD_OK\backend\migrations\01_create_employees.sql
  C:\slujeb\BD_OK\backend\migrations\02_create_positions.sql
  C:\slujeb\BD_OK\backend\migrations\03_create_rank_types.sql
  C:\slujeb\BD_OK\backend\migrations\04_create_military_ranks.sql
  C:\slujeb\BD_OK\backend\migrations\05_create_preliminary_candidates.sql
  C:\slujeb\BD_OK\backend\migrations\06_create_hierarchy.sql
  C:\slujeb\BD_OK\backend\migrations\07_create_orders.sql
  C:\slujeb\BD_OK\backend\migrations\08_create_order_attachments.sql
  C:\slujeb\BD_OK\backend\migrations\09_create_contracts.sql
  C:\slujeb\BD_OK\backend\migrations\10_create_attestations.sql
  C:\slujeb\BD_OK\backend\migrations\11_create_education_details.sql
  C:\slujeb\BD_OK\backend\migrations\12_create_foreign_travel.sql
  C:\slujeb\BD_OK\backend\migrations\13_create_state_awards.sql
  C:\slujeb\BD_OK\backend\migrations\14_create_departmental_awards.sql
  C:\slujeb\BD_OK\backend\migrations\15_create_combat_service.sql
  C:\slujeb\BD_OK\backend\migrations\16_create_family.sql
  C:\slujeb\BD_OK\backend\migrations\17_create_service_history.sql
  C:\slujeb\BD_OK\backend\migrations\18_add_required_education_level_to_positions.sql
  C:\slujeb\BD_OK\backend\migrations\20_update_education_type_in_education_details.sql
  C:\slujeb\BD_OK\backend\migrations\21_add_position_id_to_employees.sql
Initializing database...
2025-05-23 19:45:22.910079: INFO: AppDatabase: SQLite FFI initialized successfully
2025-05-23 19:45:22.910079: INFO: AppDatabase: Using database directory: C:\slujeb\BD_OK\backend\db
2025-05-23 19:45:22.910079: INFO: AppDatabase: Database directory already exists: C:\slujeb\BD_OK\backend\db
2025-05-23 19:45:22.910079: INFO: AppDatabase: Initializing database at C:\slujeb\BD_OK\backend\db\hr_system.db
2025-05-23 19:45:22.911081: INFO: AppDatabase: Database initialized successfully
Database initialized successfully
Applying migrations...
2025-05-23 19:45:22.911081: INFO: AppDatabase: Applying migrations...
2025-05-23 19:45:22.911081: FINE: AppDatabase: Executing SQL:       CREATE TABLE IF NOT EXISTS migrations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        applied_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
      )
     with parameters: []
2025-05-23 19:45:22.912079: FINE: AppDatabase: Executing SELECT SQL: SELECT name FROM migrations with parameters: []
2025-05-23 19:45:22.912079: INFO: AppDatabase: Already applied migrations: 01_create_employees.sql, 02_create_positions.sql, 03_create_rank_types.sql, 04_create_military_ranks.sql, 05_create_preliminary_candidates.sql, 06_create_hierarchy.sql, 07_create_orders.sql, 08_create_order_attachments.sql, 09_create_contracts.sql, 10_create_attestations.sql, 11_create_education_details.sql, 12_create_foreign_travel.sql, 13_create_state_awards.sql, 14_create_departmental_awards.sql, 15_create_combat_service.sql, 16_create_family.sql, 17_create_service_history.sql, 18_add_required_education_level_to_positions.sql, 20_update_education_type_in_education_details.sql, 21_add_position_id_to_employees.sql
2025-05-23 19:45:22.912079: INFO: AppDatabase: Looking for migrations in: C:\slujeb\BD_OK\backend\migrations
2025-05-23 19:45:22.912079: INFO: AppDatabase: Found 20 migration files
2025-05-23 19:45:22.912079: INFO: AppDatabase: Migration files found:
2025-05-23 19:45:22.912079: INFO: AppDatabase:   C:\slujeb\BD_OK\backend\migrations\01_create_employees.sql
2025-05-23 19:45:22.912079: INFO: AppDatabase:   C:\slujeb\BD_OK\backend\migrations\02_create_positions.sql
2025-05-23 19:45:22.912079: INFO: AppDatabase:   C:\slujeb\BD_OK\backend\migrations\03_create_rank_types.sql
2025-05-23 19:45:22.912079: INFO: AppDatabase:   C:\slujeb\BD_OK\backend\migrations\04_create_military_ranks.sql
2025-05-23 19:45:22.912079: INFO: AppDatabase:   C:\slujeb\BD_OK\backend\migrations\05_create_preliminary_candidates.sql
2025-05-23 19:45:22.912079: INFO: AppDatabase:   C:\slujeb\BD_OK\backend\migrations\06_create_hierarchy.sql
2025-05-23 19:45:22.912079: INFO: AppDatabase:   C:\slujeb\BD_OK\backend\migrations\07_create_orders.sql
2025-05-23 19:45:22.912079: INFO: AppDatabase:   C:\slujeb\BD_OK\backend\migrations\08_create_order_attachments.sql
2025-05-23 19:45:22.912079: INFO: AppDatabase:   C:\slujeb\BD_OK\backend\migrations\09_create_contracts.sql
2025-05-23 19:45:22.912079: INFO: AppDatabase:   C:\slujeb\BD_OK\backend\migrations\10_create_attestations.sql
2025-05-23 19:45:22.912079: INFO: AppDatabase:   C:\slujeb\BD_OK\backend\migrations\11_create_education_details.sql
2025-05-23 19:45:22.912079: INFO: AppDatabase:   C:\slujeb\BD_OK\backend\migrations\12_create_foreign_travel.sql
2025-05-23 19:45:22.912079: INFO: AppDatabase:   C:\slujeb\BD_OK\backend\migrations\13_create_state_awards.sql
2025-05-23 19:45:22.912079: INFO: AppDatabase:   C:\slujeb\BD_OK\backend\migrations\14_create_departmental_awards.sql
2025-05-23 19:45:22.912079: INFO: AppDatabase:   C:\slujeb\BD_OK\backend\migrations\15_create_combat_service.sql
2025-05-23 19:45:22.912079: INFO: AppDatabase:   C:\slujeb\BD_OK\backend\migrations\16_create_family.sql
2025-05-23 19:45:22.912079: INFO: AppDatabase:   C:\slujeb\BD_OK\backend\migrations\17_create_service_history.sql
2025-05-23 19:45:22.912079: INFO: AppDatabase:   C:\slujeb\BD_OK\backend\migrations\18_add_required_education_level_to_positions.sql
2025-05-23 19:45:22.912079: INFO: AppDatabase:   C:\slujeb\BD_OK\backend\migrations\20_update_education_type_in_education_details.sql
2025-05-23 19:45:22.912079: INFO: AppDatabase:   C:\slujeb\BD_OK\backend\migrations\21_add_position_id_to_employees.sql
2025-05-23 19:45:22.912079: INFO: AppDatabase: Migration already applied: 01_create_employees.sql
2025-05-23 19:45:22.912079: INFO: AppDatabase: Migration already applied: 02_create_positions.sql
2025-05-23 19:45:22.912079: INFO: AppDatabase: Migration already applied: 03_create_rank_types.sql
2025-05-23 19:45:22.912079: INFO: AppDatabase: Migration already applied: 04_create_military_ranks.sql
2025-05-23 19:45:22.912079: INFO: AppDatabase: Migration already applied: 05_create_preliminary_candidates.sql
2025-05-23 19:45:22.912079: INFO: AppDatabase: Migration already applied: 06_create_hierarchy.sql
2025-05-23 19:45:22.912079: INFO: AppDatabase: Migration already applied: 07_create_orders.sql
2025-05-23 19:45:22.912079: INFO: AppDatabase: Migration already applied: 08_create_order_attachments.sql
2025-05-23 19:45:22.912079: INFO: AppDatabase: Migration already applied: 09_create_contracts.sql
2025-05-23 19:45:22.913079: INFO: AppDatabase: Migration already applied: 10_create_attestations.sql
2025-05-23 19:45:22.913079: INFO: AppDatabase: Migration already applied: 11_create_education_details.sql
2025-05-23 19:45:22.913079: INFO: AppDatabase: Migration already applied: 12_create_foreign_travel.sql
2025-05-23 19:45:22.913079: INFO: AppDatabase: Migration already applied: 13_create_state_awards.sql
2025-05-23 19:45:22.913079: INFO: AppDatabase: Migration already applied: 14_create_departmental_awards.sql
2025-05-23 19:45:22.913079: INFO: AppDatabase: Migration already applied: 15_create_combat_service.sql
2025-05-23 19:45:22.913079: INFO: AppDatabase: Migration already applied: 16_create_family.sql
2025-05-23 19:45:22.913079: INFO: AppDatabase: Migration already applied: 17_create_service_history.sql
2025-05-23 19:45:22.913079: INFO: AppDatabase: Migration already applied: 18_add_required_education_level_to_positions.sql
2025-05-23 19:45:22.913079: INFO: AppDatabase: Migration already applied: 20_update_education_type_in_education_details.sql
2025-05-23 19:45:22.913079: INFO: AppDatabase: Migration already applied: 21_add_position_id_to_employees.sql
2025-05-23 19:45:22.913079: INFO: AppDatabase: All migrations applied successfully
Migrations applied successfully
Creating router...
Router created successfully
Setting up static file handler...
Web directory path: C:\slujeb\BD_OK\backend\web
Web directory exists, contents:
  C:\slujeb\BD_OK\backend\web\.last_build_id
  C:\slujeb\BD_OK\backend\web\assets
  C:\slujeb\BD_OK\backend\web\canvaskit
  C:\slujeb\BD_OK\backend\web\favicon.png
  C:\slujeb\BD_OK\backend\web\flutter.js
  C:\slujeb\BD_OK\backend\web\flutter_service_worker.js
  C:\slujeb\BD_OK\backend\web\icons
  C:\slujeb\BD_OK\backend\web\index.html
  C:\slujeb\BD_OK\backend\web\main.dart.js
  C:\slujeb\BD_OK\backend\web\manifest.json
  C:\slujeb\BD_OK\backend\web\version.json
Static file handler set up successfully
Using IP: 0.0.0.0
Setting up request pipeline...
Request pipeline set up successfully
Starting server on port 8080...
Server listening on port 8080
Server is ready to accept connections
Setting up signal handlers...
Signal handlers set up successfully
2025-05-23 19:45:40.761997: INFO: RequestLogger: GET 
Handling request for path: 
Routing to API handler
2025-05-23 19:45:40.761997: INFO: RequestLogger: GET  200 (0ms)
2025-05-23 19:47:35.731349: INFO: RequestLogger: GET 
Handling request for path: 
Routing to API handler
2025-05-23 19:47:35.731349: INFO: RequestLogger: GET  200 (0ms)
2025-05-23 19:48:58.368956: INFO: RequestLogger: GET 
Handling request for path: 
Routing to API handler
2025-05-23 19:48:58.369474: INFO: RequestLogger: GET  200 (0ms)
2025-05-23 19:49:05.739690: INFO: RequestLogger: GET employees
Handling request for path: employees
Routing to static file handler
2025-05-23 19:49:05.740588: INFO: RequestLogger: GET employees 404 (0ms)
2025-05-23 19:49:12.013996: INFO: RequestLogger: GET api/employees
Handling request for path: api/employees
Routing to API handler
2025-05-23 19:49:12.013996: INFO: EmployeeController: Getting all employees
2025-05-23 19:49:12.013996: INFO: EmployeeRepository: Getting all employees
2025-05-23 19:49:12.013996: FINE: AppDatabase: Executing SELECT SQL: SELECT * FROM employees with parameters: []
2025-05-23 19:49:12.014995: INFO: RequestLogger: GET api/employees 200 (0ms)
2025-05-23 19:49:20.335127: INFO: RequestLogger: GET index.html
Handling request for path: index.html
Routing to static file handler
2025-05-23 19:49:20.335127: INFO: RequestLogger: GET index.html 200 (0ms)
2025-05-23 19:50:42.971129: INFO: RequestLogger: GET 
Handling request for path: 
Routing to API handler
2025-05-23 19:50:42.971129: INFO: RequestLogger: GET  200 (0ms)
^C