import 'package:flutter/foundation.dart';
import 'package:frontend/models/order_attachment.dart';
import 'package:frontend/services/order_attachment_service.dart';

/// Провайдер для управления состоянием вложений к приказам
class OrderAttachmentProvider with ChangeNotifier {
  final OrderAttachmentService _service = OrderAttachmentService();
  
  List<OrderAttachment> _attachments = [];
  bool _isLoading = false;
  String? _error;
  
  List<OrderAttachment> get attachments => _attachments;
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  /// Получить все вложения
  Future<void> fetchAttachments() async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      _attachments = await _service.getAttachments();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }
  
  /// Получить вложения по ID приказа
  Future<void> fetchAttachmentsByOrderId(int orderId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      _attachments = await _service.getAttachmentsByOrderId(orderId);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }
  
  /// Получить вложение по ID
  Future<OrderAttachment?> getAttachment(int id) async {
    try {
      return await _service.getAttachment(id);
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }
  
  /// Создать новое вложение
  Future<int> createAttachment(int orderId, String fileName, List<int> fileData) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      final id = await _service.createAttachment(orderId, fileName, fileData);
      
      // Обновляем список вложений
      await fetchAttachmentsByOrderId(orderId);
      
      return id;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      rethrow;
    }
  }
  
  /// Обновить вложение
  Future<bool> updateAttachment(OrderAttachment attachment) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      final success = await _service.updateAttachment(attachment);
      
      // Обновляем список вложений
      if (success) {
        await fetchAttachmentsByOrderId(attachment.orderId);
      }
      
      return success;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      rethrow;
    }
  }
  
  /// Удалить вложение
  Future<bool> deleteAttachment(int id, int orderId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      final success = await _service.deleteAttachment(id);
      
      // Обновляем список вложений
      if (success) {
        await fetchAttachmentsByOrderId(orderId);
      }
      
      return success;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      rethrow;
    }
  }
  
  /// Скачать вложение
  Future<Uint8List> downloadAttachment(int id) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      final data = await _service.downloadAttachment(id);
      _isLoading = false;
      notifyListeners();
      return data;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      rethrow;
    }
  }
}
