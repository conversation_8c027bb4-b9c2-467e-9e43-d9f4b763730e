# Task: Обновление тестов для проверки логики назначения сотрудников на должности

**Goal:** Обновить существующие тесты и создать новые для проверки логики назначения сотрудников на должности.

## Описание
Необходимо обновить существующие тесты и создать новые для проверки логики назначения сотрудников на должности. Тесты должны проверять корректность работы новой логики, включая проверку занятости должности, автоматическое закрытие активной записи и обновление прямой ссылки на должность.

## Шаги выполнения
1. Обновить существующие тесты для работы с новой структурой данных
2. Создать новые тесты для проверки логики назначения сотрудников на должности:
   - Тест на проверку занятости должности
   - Тест на автоматическое закрытие активной записи
   - Тест на обновление прямой ссылки на должность
   - Тест на корректность работы всей логики назначения

## Технические детали
- Тесты должны быть написаны с использованием пакета `test` для Dart
- Тесты должны быть размещены в директории `backend/test/`
- Тесты должны быть интегрированы в CI/CD процесс

## Критерии завершения
- Все тесты успешно проходят
- Тесты покрывают все основные сценарии использования
- Тесты интегрированы в CI/CD процесс

## Статус: Pending
