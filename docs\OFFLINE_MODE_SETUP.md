# Настройка автономного режима Flutter Web приложения

## Обзор

Данное руководство описывает настройку Flutter веб-приложения для полностью автономной работы без интернет-соединения. Все необходимые ресурсы, включая CanvasKit, встроены в сборку приложения.

## Проблема

При запуске Flutter веб-приложения на сервере без интернет-соединения возникают ошибки:
- "Flutter web engine failed to complete HTML import request..."
- "Uncaught Error: Failed to download one or more of the following CanvasKit URLs..."

## Решение

### 1. Конфигурационные файлы

#### `frontend/web/flutter_config.js`
Основной конфигурационный файл для настройки Flutter на использование локальных ресурсов:
- Настраивает `canvasKitBaseUrl` на локальную директорию `/canvaskit/`
- Блокирует внешние запросы к CDN
- Перенаправляет запросы CanvasKit на локальные файлы

#### `frontend/web/offline_enhancer.js`
Дополнительный файл для улучшения автономного режима:
- Блокирует XMLHttpRequest к внешним доменам
- Перехватывает fetch запросы и перенаправляет их на локальные ресурсы
- Предотвращает загрузку внешних скриптов
- Мониторит статус сети

#### `frontend/web/index.html`
Обновлен для включения конфигурационных скриптов и настройки Flutter:
- Подключает `flutter_config.js` и `offline_enhancer.js`
- Настраивает `window.flutterConfiguration` для автономного режима

### 2. Сборка приложения

#### Обновленная команда сборки
```bash
flutter build web --release --web-renderer canvaskit --dart-define=FLUTTER_WEB_CANVASKIT_URL=/canvaskit/
```

Флаги:
- `--web-renderer canvaskit` - принудительно использует CanvasKit рендерер
- `--dart-define=FLUTTER_WEB_CANVASKIT_URL=/canvaskit/` - устанавливает локальный путь для CanvasKit

#### Автоматическая сборка
Используйте обновленный скрипт `build_production_en.bat`, который:
1. Собирает приложение с правильными флагами
2. Копирует все конфигурационные файлы
3. Создает standalone исполняемый файл

### 3. Структура файлов

После сборки в директории `dist/web/` должны присутствовать:
```
dist/web/
├── canvaskit/              # Локальные файлы CanvasKit
│   ├── canvaskit.js
│   ├── canvaskit.wasm
│   ├── chromium/
│   └── skwasm.*
├── flutter_config.js       # Конфигурация Flutter
├── offline_enhancer.js     # Улучшения автономного режима
├── index.html              # Обновленный HTML с конфигурацией
├── main.dart.js            # Скомпилированное приложение
└── flutter_service_worker.js # Service Worker с кешированием
```

### 4. Тестирование

#### Автоматическое тестирование
Используйте скрипт `test_offline_mode.bat` для проверки автономного режима.

#### Ручное тестирование
1. Запустите приложение: `dist\hr_system.exe`
2. Откройте http://localhost:8080 в браузере
3. Отключите интернет-соединение
4. Обновите страницу - приложение должно работать
5. Проверьте консоль браузера на отсутствие ошибок загрузки внешних ресурсов

### 5. Ожидаемое поведение

✅ **Правильное поведение:**
- Приложение загружается без интернет-соединения
- Нет запросов к внешним CDN
- CanvasKit загружается из локальных файлов
- Service Worker кеширует все ресурсы
- В консоли браузера: "Flutter offline configuration loaded"

❌ **Проблемы (должны быть устранены):**
- Запросы к unpkg.com, gstatic.com, googleapis.com
- Ошибки загрузки CanvasKit
- Белый экран при отсутствии интернета

### 6. Устранение неполадок

#### Проверка конфигурации
1. Убедитесь, что файлы `flutter_config.js` и `offline_enhancer.js` присутствуют в `dist/web/`
2. Проверьте, что директория `dist/web/canvaskit/` содержит все необходимые файлы
3. Откройте консоль браузера и найдите сообщения о загрузке конфигурации

#### Очистка кеша
При тестировании очищайте кеш браузера или используйте режим инкогнито для проверки изменений.

#### Проверка Service Worker
В DevTools браузера перейдите в Application > Service Workers и убедитесь, что service worker активен и кеширует ресурсы.

## Заключение

После применения всех настроек Flutter веб-приложение будет работать полностью автономно без необходимости интернет-соединения. Все ресурсы, включая CanvasKit, будут загружаться из локальных файлов.
