import 'package:backend/database/database.dart';
import 'package:backend/models/position.dart';
import 'package:logging/logging.dart';

class PositionRepository {
  static final Logger _logger = Logger('PositionRepository');

  /// Получить все должности
  List<Position> getAll() {
    _logger.info('Getting all positions');

    final result = AppDatabase.select('SELECT * FROM positions');

    return result.map((row) {
      return Position(
        id: row['id'] as int,
        title: row['title'] as String,
        department: row['department'] as String?,
        unitName: row['unit_name'] as String?,
        womenAllowed: row['women_allowed'] as int?,
        militaryRankStaffId: row['military_rank_staff_id'] as int?,
        vus: row['vus'] as String?,
        vusPss: row['vus_pss'] as String?,
        positionCodePss: row['position_code_pss'] as String?,
        tariffCategory: row['tariff_category'] as String?,
        isFlightCrew: row['is_flight_crew'] as int?,
        antiCorruption: row['anti_corruption'] as int?,
        requiredEducationLevel: row['required_education_level'] as int?,
        createdAt: row['created_at'] as String?,
        updatedAt: row['updated_at'] as String?,
      );
    }).toList();
  }

  /// Получить должность по ID
  Position? getById(int id) {
    _logger.info('Getting position by ID: $id');

    final result = AppDatabase.select('SELECT * FROM positions WHERE id = ?', [
      id,
    ]);

    if (result.isEmpty) {
      return null;
    }

    final row = result.first;

    return Position(
      id: row['id'] as int,
      title: row['title'] as String,
      department: row['department'] as String?,
      unitName: row['unit_name'] as String?,
      womenAllowed: row['women_allowed'] as int?,
      militaryRankStaffId: row['military_rank_staff_id'] as int?,
      vus: row['vus'] as String?,
      vusPss: row['vus_pss'] as String?,
      positionCodePss: row['position_code_pss'] as String?,
      tariffCategory: row['tariff_category'] as String?,
      isFlightCrew: row['is_flight_crew'] as int?,
      antiCorruption: row['anti_corruption'] as int?,
      requiredEducationLevel: row['required_education_level'] as int?,
      createdAt: row['created_at'] as String?,
      updatedAt: row['updated_at'] as String?,
    );
  }

  /// Создать новую должность
  int create(Position position) {
    _logger.info('Creating new position: $position');

    AppDatabase.execute(
      '''
      INSERT INTO positions (
        title, department, unit_name, women_allowed, military_rank_staff_id,
        vus, vus_pss, position_code_pss, tariff_category, is_flight_crew, anti_corruption,
        required_education_level
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      ''',
      [
        position.title,
        position.department,
        position.unitName,
        position.womenAllowed,
        position.militaryRankStaffId,
        position.vus,
        position.vusPss,
        position.positionCodePss,
        position.tariffCategory,
        position.isFlightCrew,
        position.antiCorruption,
        position.requiredEducationLevel,
      ],
    );

    return AppDatabase.database.lastInsertRowId;
  }

  /// Обновить должность
  bool update(Position position) {
    _logger.info('Updating position: $position');

    if (position.id == null) {
      throw ArgumentError('Position ID cannot be null');
    }

    AppDatabase.execute(
      '''
      UPDATE positions SET
        title = ?, department = ?, unit_name = ?, women_allowed = ?, military_rank_staff_id = ?,
        vus = ?, vus_pss = ?, position_code_pss = ?, tariff_category = ?, is_flight_crew = ?,
        anti_corruption = ?, required_education_level = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
      ''',
      [
        position.title,
        position.department,
        position.unitName,
        position.womenAllowed,
        position.militaryRankStaffId,
        position.vus,
        position.vusPss,
        position.positionCodePss,
        position.tariffCategory,
        position.isFlightCrew,
        position.antiCorruption,
        position.requiredEducationLevel,
        position.id,
      ],
    );

    return AppDatabase.database.updatedRows > 0;
  }

  /// Удалить должность
  bool delete(int id) {
    _logger.info('Deleting position with ID: $id');

    AppDatabase.execute('DELETE FROM positions WHERE id = ?', [id]);

    return AppDatabase.database.updatedRows > 0;
  }
}
