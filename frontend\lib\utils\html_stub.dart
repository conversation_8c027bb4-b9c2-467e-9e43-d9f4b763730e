// Заглушка для библиотеки dart:html на платформах, отличных от web

class Blob {
  Blob(List<dynamic> data, [String? type]);
  int get size => 0;
  String get type => '';
}

class Url {
  static String createObjectUrlFromBlob(dynamic blob) => '';
  static void revokeObjectUrl(String url) {}
}

class Element {
  void append(Element element) {}
  void remove() {}
}

class BodyElement extends Element {}

class Document {
  BodyElement? get body => null;
}

// Глобальный объект document
Document document = Document();

class Window {
  void open(String url, String target) {}
}

// Глобальный объект window
Window window = Window();

class CssStyleDeclaration {
  String display = '';
}

class AnchorElement extends Element {
  String href = '';
  String download = '';
  String target = '';
  String rel = '';
  CssStyleDeclaration style = CssStyleDeclaration();

  AnchorElement();

  void setAttribute(String name, String value) {}
  void click() {}
}

class IFrameElement extends Element {
  String src = '';
  String name = '';
  CssStyleDeclaration style = CssStyleDeclaration();

  IFrameElement();
}
