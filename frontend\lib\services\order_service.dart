import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:frontend/models/order.dart';

class OrderService {
  static const String baseUrl = 'http://localhost:8080/api/orders';

  /// Получить все приказы
  Future<List<Order>> getOrders() async {
    final response = await http.get(Uri.parse(baseUrl));
    
    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      if (data['success'] == true && data['data'] != null) {
        return (data['data'] as List)
            .map((item) => Order.fromJson(item))
            .toList();
      }
      return [];
    } else {
      throw Exception('Failed to load orders');
    }
  }

  /// Получить приказ по ID
  Future<Order?> getOrder(int id) async {
    final response = await http.get(Uri.parse('$baseUrl/$id'));
    
    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      if (data['success'] == true && data['data'] != null) {
        return Order.fromJson(data['data']);
      }
      return null;
    } else if (response.statusCode == 404) {
      return null;
    } else {
      throw Exception('Failed to load order');
    }
  }

  /// Создать новый приказ
  Future<int> createOrder(Order order) async {
    final response = await http.post(
      Uri.parse(baseUrl),
      headers: {'Content-Type': 'application/json'},
      body: json.encode(order.toJson()),
    );
    
    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      if (data['success'] == true && data['data'] != null) {
        return data['data']['id'];
      }
      throw Exception('Failed to create order: ${data['message']}');
    } else {
      throw Exception('Failed to create order');
    }
  }

  /// Обновить приказ
  Future<void> updateOrder(Order order) async {
    if (order.id == null) {
      throw Exception('Order ID cannot be null');
    }
    
    final response = await http.put(
      Uri.parse('$baseUrl/${order.id}'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode(order.toJson()),
    );
    
    if (response.statusCode != 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      throw Exception('Failed to update order: ${data['message']}');
    }
  }

  /// Удалить приказ
  Future<void> deleteOrder(int id) async {
    final response = await http.delete(Uri.parse('$baseUrl/$id'));
    
    if (response.statusCode != 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      throw Exception('Failed to delete order: ${data['message']}');
    }
  }
}
