# Task: Реализация моделей данных и подключения к SQLite в Dart

**Goal:** Создать модели данных и настроить подключение к SQLite для системы отдела кадров на Dart.

**Steps:**

1. Проанализировать существующую структуру базы данных из файла `db_schema.md`
2. Настроить подключение к SQLite в `lib/database/database.dart`:
   - Использовать пакет `sqlite3` для работы с SQLite
   - Создать класс `Database` для управления подключением
   - Реализовать методы для выполнения запросов
3. Создать модели данных в директории `lib/models/`:
   - `employee.dart` - модель сотрудника
   - `position.dart` - модель должности
   - `military_rank.dart` - модель воинского звания
   - `rank_type.dart` - модель типа звания
   - `service_history.dart` - модель послужного списка
   - `contract.dart` - модель контракта
   - `order.dart` - модель приказа
   - `hierarchy.dart` - модель иерархии
   - Другие необходимые модели
4. Реализовать сериализацию/десериализацию моделей с помощью `json_serializable`
5. Создать репозитории для работы с моделями в директории `lib/database/repositories/`:
   - `employee_repository.dart` - репозиторий для работы с сотрудниками
   - `position_repository.dart` - репозиторий для работы с должностями
   - Другие необходимые репозитории
6. Создать SQL-файлы миграций в директории `migrations/`:
   - `01_create_employees.sql` - создание таблицы сотрудников
   - `02_create_positions.sql` - создание таблицы должностей
   - Другие необходимые миграции
7. Реализовать механизм применения миграций при запуске сервера

**Expected Result:**
- Настроенное подключение к базе данных SQLite
- Полный набор моделей данных, соответствующих структуре базы данных
- Репозитории для работы с моделями
- Миграции для создания всех необходимых таблиц
- Механизм применения миграций при запуске сервера

**Status:** Completed
