import 'package:shelf/shelf.dart';
import 'package:logging/logging.dart';

/// Middleware для логирования запросов
Middleware loggingMiddleware() {
  final logger = Logger('RequestLogger');
  
  return (Handler innerHandler) {
    return (Request request) async {
      final startTime = DateTime.now();
      logger.info('${request.method} ${request.url}');
      
      final response = await innerHandler(request);
      
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      
      logger.info(
        '${request.method} ${request.url} ${response.statusCode} '
        '(${duration.inMilliseconds}ms)',
      );
      
      return response;
    };
  };
}
