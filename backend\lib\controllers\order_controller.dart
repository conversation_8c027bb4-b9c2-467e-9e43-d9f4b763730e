import 'dart:convert';
import 'package:shelf/shelf.dart';
import 'package:backend/database/repositories/order_repository.dart';
import 'package:backend/models/order.dart';
import 'package:logging/logging.dart';

class OrderController {
  static final Logger _logger = Logger('OrderController');
  final OrderRepository _repository = OrderRepository();

  /// Получить все приказы
  Response getAll(Request request) {
    _logger.info('Getting all orders');

    try {
      final orders = _repository.getAll();

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': orders.map((e) => e.toJson()).toList(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error getting all orders: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Получить приказ по ID
  Response getById(Request request, String id) {
    _logger.info('Getting order by ID: $id');

    try {
      final orderId = int.parse(id);
      final order = _repository.getById(orderId);

      if (order == null) {
        return Response.notFound(
          jsonEncode({
            'success': false,
            'error': 'Not Found',
            'message': 'Order with ID $id not found',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': order.toJson(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error getting order by ID: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Создать новый приказ
  Future<Response> create(Request request) async {
    _logger.info('Creating new order');

    try {
      final jsonBody = await request.readAsString();
      final Map<String, dynamic> data = jsonDecode(jsonBody);

      final order = Order(
        number: data['number'],
        date: data['date'],
        issuedBy: data['issuedBy'],
        description: data['description'],
      );

      final id = _repository.create(order);

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': {'id': id},
          'message': 'Order created successfully',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error creating order: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Обновить приказ
  Future<Response> update(Request request, String id) async {
    _logger.info('Updating order with ID: $id');

    try {
      final orderId = int.parse(id);
      final existingOrder = _repository.getById(orderId);

      if (existingOrder == null) {
        return Response.notFound(
          jsonEncode({
            'success': false,
            'error': 'Not Found',
            'message': 'Order with ID $id not found',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final jsonBody = await request.readAsString();
      final Map<String, dynamic> data = jsonDecode(jsonBody);

      final order = Order(
        id: orderId,
        number: data['number'] ?? existingOrder.number,
        date: data['date'] ?? existingOrder.date,
        issuedBy: data['issuedBy'] ?? existingOrder.issuedBy,
        description: data['description'] ?? existingOrder.description,
      );

      final success = _repository.update(order);

      if (!success) {
        return Response.internalServerError(
          body: jsonEncode({
            'success': false,
            'error': 'Internal Server Error',
            'message': 'Failed to update order',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'Order updated successfully',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error updating order: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Удалить приказ
  Response delete(Request request, String id) {
    _logger.info('Deleting order with ID: $id');

    try {
      final orderId = int.parse(id);
      final existingOrder = _repository.getById(orderId);

      if (existingOrder == null) {
        return Response.notFound(
          jsonEncode({
            'success': false,
            'error': 'Not Found',
            'message': 'Order with ID $id not found',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final success = _repository.delete(orderId);

      if (!success) {
        return Response.internalServerError(
          body: jsonEncode({
            'success': false,
            'error': 'Internal Server Error',
            'message': 'Failed to delete order',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'Order deleted successfully',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error deleting order: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }
}
