# Подзадача: Добавление маршрутов для API вложений

## Описание
Необходимо создать маршруты для API вложений к приказам и подключить их в основной маршрутизатор приложения.

## Текущее состояние
- Контроллер для обработки запросов к API вложений создан
- Отсутствуют маршруты для API вложений

## Шаги выполнения
1. Создать файл `backend/lib/routes/order_attachment_routes.dart`
2. Реализовать функцию `orderAttachmentRoutes()`, которая создает маршрутизатор для работы с вложениями к приказам
3. Добавить следующие маршруты:
   - `GET /` - получить все вложения
   - `GET /order/:orderId` - получить вложения по ID приказа
   - `GET /:id` - получить вложение по ID
   - `POST /` - создать новое вложение
   - `PUT /:id` - обновить вложение
   - `DELETE /:id` - удалить вложение
   - `GET /:id/download` - скачать вложение
4. Обновить файл `backend/lib/routes/router.dart`, добавив маршрут для API вложений

## Ожидаемый результат
Маршруты для API вложений к приказам, которые обеспечивают доступ ко всем необходимым операциям для работы с вложениями.
