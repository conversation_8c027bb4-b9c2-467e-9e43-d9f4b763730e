import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:backend/database/database.dart';
import 'package:backend/utils/logger.dart';

/// Скрипт для проверки примененных миграций
void main() async {
  // Инициализируем логгер
  initLogger();

  print('Checking migrations...');
  
  // Получаем путь к исполняемому файлу
  final executablePath = Platform.script.toFilePath();
  print('Executable path: $executablePath');
  
  // Получаем директорию, в которой находится исполняемый файл
  final executableDir = path.dirname(executablePath);
  print('Executable directory: $executableDir');
  
  // Получаем корневую директорию проекта (на один уровень выше bin)
  final rootDir = path.dirname(executableDir);
  print('Root directory: $rootDir');
  
  // Создаем пути к директориям относительно корневой директории
  final dbDirPath = path.join(rootDir, 'db');
  final migrationsDirPath = path.join(rootDir, 'migrations');
  
  print('DB directory path: $dbDirPath');
  print('Migrations directory path: $migrationsDirPath');
  
  // Инициализируем базу данных
  print('Initializing database...');
  await AppDatabase.initialize(customDbDir: dbDirPath);
  print('Database initialized successfully');

  // Получаем список примененных миграций
  print('Getting applied migrations...');
  final appliedMigrations = AppDatabase.select(
    'SELECT name FROM migrations ORDER BY id',
  ).map((row) => row['name'] as String).toList();
  
  print('Applied migrations:');
  for (final migration in appliedMigrations) {
    print('  $migration');
  }
  
  // Получаем список доступных миграций
  print('\nAvailable migrations:');
  final migrationsDir = Directory(migrationsDirPath);
  if (migrationsDir.existsSync()) {
    final migrationFiles = migrationsDir
        .listSync()
        .where((entity) => entity is File && entity.path.endsWith('.sql'))
        .map((entity) => entity as File)
        .toList();
    
    // Сортируем файлы миграций по имени
    migrationFiles.sort(
      (a, b) => path.basename(a.path).compareTo(path.basename(b.path)),
    );
    
    for (final file in migrationFiles) {
      final migrationName = path.basename(file.path);
      final isApplied = appliedMigrations.contains(migrationName);
      print('  $migrationName ${isApplied ? "(applied)" : "(not applied)"}');
    }
  } else {
    print('  Migrations directory does not exist!');
  }
  
  // Получаем список таблиц
  print('\nDatabase tables:');
  final tables = AppDatabase.select(
    "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'",
  ).map((row) => row['name'] as String).toList();
  
  for (final table in tables) {
    print('  $table');
    
    // Получаем список колонок для таблицы
    final columns = AppDatabase.select(
      'PRAGMA table_info($table)',
    ).map((row) => '${row['name']} (${row['type']})').toList();
    
    print('    Columns: ${columns.join(', ')}');
  }
  
  // Закрываем соединение с базой данных
  AppDatabase.close();
  print('\nDone.');
}
