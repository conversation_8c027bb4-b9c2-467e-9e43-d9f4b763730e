-- Создание таблицы должностей
CREATE TABLE IF NOT EXISTS positions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    department TEXT,
    unit_name TEXT,
    women_allowed INTEGER DEFAULT 1,
    military_rank_staff_id INTEGER,
    vus TEXT,
    vus_pss TEXT,
    position_code_pss TEXT,
    tariff_category TEXT,
    is_flight_crew INTEGER DEFAULT 0,
    anti_corruption INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
