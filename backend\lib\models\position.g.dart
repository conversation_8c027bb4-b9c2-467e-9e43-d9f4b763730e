// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'position.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Position _$PositionFromJson(Map<String, dynamic> json) => Position(
  id: (json['id'] as num?)?.toInt(),
  title: json['title'] as String,
  department: json['department'] as String?,
  unitName: json['unitName'] as String?,
  womenAllowed: (json['womenAllowed'] as num?)?.toInt(),
  militaryRankStaffId: (json['militaryRankStaffId'] as num?)?.toInt(),
  vus: json['vus'] as String?,
  vusPss: json['vusPss'] as String?,
  positionCodePss: json['positionCodePss'] as String?,
  tariffCategory: json['tariffCategory'] as String?,
  isFlightCrew: (json['isFlightCrew'] as num?)?.toInt(),
  antiCorruption: (json['antiCorruption'] as num?)?.toInt(),
  requiredEducationLevel: (json['requiredEducationLevel'] as num?)?.toInt(),
  createdAt: json['createdAt'] as String?,
  updatedAt: json['updatedAt'] as String?,
);

Map<String, dynamic> _$PositionToJson(Position instance) => <String, dynamic>{
  'id': instance.id,
  'title': instance.title,
  'department': instance.department,
  'unitName': instance.unitName,
  'womenAllowed': instance.womenAllowed,
  'militaryRankStaffId': instance.militaryRankStaffId,
  'vus': instance.vus,
  'vusPss': instance.vusPss,
  'positionCodePss': instance.positionCodePss,
  'tariffCategory': instance.tariffCategory,
  'isFlightCrew': instance.isFlightCrew,
  'antiCorruption': instance.antiCorruption,
  'requiredEducationLevel': instance.requiredEducationLevel,
  'createdAt': instance.createdAt,
  'updatedAt': instance.updatedAt,
};
