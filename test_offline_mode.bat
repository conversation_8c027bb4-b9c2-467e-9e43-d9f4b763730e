@echo off
echo ===== Testing Flutter Web Offline Mode =====
echo.

echo This script will test if the Flutter web application works offline
echo by temporarily blocking internet access and running the application.
echo.

echo 1. Building the application with offline configuration...
call build_production_en.bat
if %ERRORLEVEL% neq 0 (
    echo Error building application!
    exit /b %ERRORLEVEL%
)

echo.
echo 2. Starting the application...
echo.
echo The application should now work completely offline.
echo.
echo To test offline mode:
echo 1. Open http://localhost:8080 in your browser
echo 2. Disconnect from the internet
echo 3. Refresh the page - it should still work
echo 4. Check browser console for any external resource loading errors
echo.
echo Expected behavior:
echo - No external CanvasKit downloads
echo - No CDN requests
echo - Application loads and works normally
echo.
echo Press any key to start the server...
pause

cd dist
start hr_system.exe

echo.
echo Server started. Open http://localhost:8080 in your browser.
echo.
echo To stop the server, close the command window or press Ctrl+C in the server window.
echo.
pause
