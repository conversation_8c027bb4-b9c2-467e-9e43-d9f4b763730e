@echo off
echo ===== Testing Flutter Web Offline Mode =====
echo.

echo This script will test if the Flutter web application works offline
echo with all fixes applied including local fonts and JavaScript compatibility.
echo.

echo 1. Building the application with offline configuration...
call build_production_en.bat
if %ERRORLEVEL% neq 0 (
    echo Error building application!
    exit /b %ERRORLEVEL%
)

echo.
echo 2. Starting the application...
echo.
echo The application should now work completely offline with:
echo - Local Roboto fonts
echo - Fixed JavaScript compatibility
echo - Local CanvasKit rendering
echo - No external CDN dependencies
echo.
echo To test offline mode:
echo 1. Open http://localhost:8080 in your browser
echo 2. Check browser console for successful loading messages:
echo    - "Loading offline enhancer..."
echo    - "Flutter offline configuration loaded"
echo    - "Offline enhancer loaded successfully"
echo 3. Verify fonts load from local files (fonts/roboto-*.woff2)
echo 4. Disconnect from the internet
echo 5. Refresh the page - it should still work perfectly
echo 6. Check for NO JavaScript errors in console
echo.
echo Expected behavior:
echo - No external CanvasKit downloads
echo - No CDN requests to fonts.gstatic.com
echo - Local Roboto fonts load successfully
echo - No JavaScript "includes" method errors
echo - Application loads and works normally
echo.
echo Press any key to start the server...
pause

cd dist
start hr_system.exe

echo.
echo Server started. Open http://localhost:8080 in your browser.
echo.
echo Check the server console for successful font loading:
echo - GET fonts/roboto.css 200
echo - GET fonts/roboto-regular.woff2 200
echo - GET offline_enhancer.js 200
echo.
echo To stop the server, close the command window or press Ctrl+C in the server window.
echo.
pause
