# Подзадача: Изменить страницу деталей сотрудника

**Статус:** Выполнено

## Описание проблемы
На странице деталей сотрудника (EmployeeDetailPage) присутствовали две кнопки: "Найти" и "Назначить", а диалог поиска растягивался на весь экран, что усложняло UX и вводило лишние шаги.

## Решение
1. Удалить кнопку "Назначить" из метода `_buildPositionInfo`.
2. Переименовать кнопку "Найти" в "Найти и назначить" и изменить условие отображения на `if (_employee != null)`.
3. Обновить метод `_showPositionSearchDialog`: убрать проверку на `positionId != null`, добавить фиксированные размеры диалога (`width: 500`, `height: 400`).
4. При выборе должности сразу вызывать `_showPositionAssignmentDialogWithPosition(position.id!)`.
5. Добавить поле поиска по названию внутри диалога.

## Внесенные изменения
```dart
// frontend/lib/pages/employee/employee_detail_page.dart

// ... в методе _buildPositionInfo:
// удален ElevatedButton.icon('Назначить')
// заменен OutlinedButton.icon:
OutlinedButton.icon(
  icon: const Icon(Icons.search),
  label: const Text('Найти и назначить'),
  onPressed: _showPositionSearchDialog,
)

// ... в методе _showPositionSearchDialog:
content: SizedBox(
  width: 500,
  height: 400,
  child: Column(
    children: [
      TextField(
        decoration: InputDecoration(...),
        onChanged: ..., // фильтрация по названию
      ),
      Expanded(
        child: ListView.builder(
          itemCount: filteredPositions.length,
          itemBuilder: (_, index) {
            final position = filteredPositions[index];
            return ListTile(
              title: Text(position.title),
              trailing: isCurrentPosition
                  ? const Icon(Icons.check_circle, color: Colors.green)
                  : null,
              onTap: () {
                Navigator.of(dialogContext).pop();
                _showPositionAssignmentDialogWithPosition(position.id!);
              },
            );
          },
        ),
      ),
    ],
  ),
),
```  

## Обоснование
Объединение поиска и назначения одной кнопкой упрощает интерфейс, а фиксированный размер диалога улучшает восприятие и предотвращает растягивание на весь экран. 