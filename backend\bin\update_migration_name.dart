import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:backend/database/database.dart';
import 'package:backend/utils/logger.dart';

/// Скрипт для обновления имени миграции в таблице migrations
void main() async {
  // Инициализируем логгер
  initLogger();

  print('Updating migration name...');
  
  // Получаем путь к исполняемому файлу
  final executablePath = Platform.script.toFilePath();
  print('Executable path: $executablePath');
  
  // Получаем директорию, в которой находится исполняемый файл
  final executableDir = path.dirname(executablePath);
  print('Executable directory: $executableDir');
  
  // Получаем корневую директорию проекта (на один уровень выше bin)
  final rootDir = path.dirname(executableDir);
  print('Root directory: $rootDir');
  
  // Создаем пути к директориям относительно корневой директории
  final dbDirPath = path.join(rootDir, 'db');
  
  print('DB directory path: $dbDirPath');
  
  // Инициализируем базу данных
  print('Initializing database...');
  await AppDatabase.initialize(customDbDir: dbDirPath);
  print('Database initialized successfully');

  // Обновляем имя миграции
  print('Updating migration name...');
  final oldName = '19_update_education_type_in_education_details.sql';
  final newName = '20_update_education_type_in_education_details.sql';
  
  AppDatabase.execute(
    'UPDATE migrations SET name = ? WHERE name = ?',
    [newName, oldName],
  );
  
  print('Migration name updated successfully');
  
  // Проверяем, что имя обновлено
  final migrations = AppDatabase.select(
    'SELECT name FROM migrations WHERE name = ?',
    [newName],
  ).map((row) => row['name'] as String).toList();
  
  if (migrations.isNotEmpty) {
    print('Migration name updated to: ${migrations.first}');
  } else {
    print('Failed to update migration name');
  }
  
  // Закрываем соединение с базой данных
  AppDatabase.close();
  print('\nDone.');
}
