import 'package:shelf/shelf.dart';
import 'package:shelf_router/shelf_router.dart';
import 'package:backend/routes/employee_routes.dart';
import 'package:backend/routes/position_routes.dart';
import 'package:backend/routes/military_rank_routes.dart';
import 'package:backend/routes/rank_type_routes.dart';
import 'package:backend/routes/order_routes.dart';
import 'package:backend/routes/order_attachment_routes.dart';
import 'package:backend/routes/contract_routes.dart';
import 'package:backend/routes/service_history_routes.dart';
import 'package:backend/routes/table_routes.dart';

/// Создает основной маршрутизатор приложения
Router createRouter() {
  final router = Router();

  // Корневой маршрут для API
  router.get('/api', (Request request) {
    return Response.ok('HR System API');
  });

  // Маршрут для проверки работоспособности API
  router.get('/health', (Request request) {
    return Response.ok('OK');
  });

  // Маршруты для работы с сотрудниками
  router.mount('/api/employees', employeeRoutes().call);

  // Маршруты для работы с должностями
  router.mount('/api/positions', positionRoutes().call);

  // Маршруты для работы с воинскими званиями
  router.mount('/api/military-ranks', militaryRankRoutes().call);

  // Маршруты для работы с типами званий
  router.mount('/api/rank-types', rankTypeRoutes().call);

  // Маршруты для работы с приказами
  router.mount('/api/orders', orderRoutes().call);

  // Маршруты для работы с вложениями к приказам
  router.mount('/api/order-attachments', orderAttachmentRoutes().call);

  // Маршруты для работы с контрактами
  router.mount('/api/contracts', contractRoutes().call);

  // Маршруты для работы с послужным списком
  router.mount('/api/service-history', serviceHistoryRoutes().call);

  // Маршруты для работы с таблицами
  router.mount('/api/tables', tableRoutes().call);

  return router;
}
