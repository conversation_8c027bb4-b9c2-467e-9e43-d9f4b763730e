# Подзадача: Создание и выполнение тестов для API вложений к приказам

## Описание
Необходимо создать и выполнить тесты для API вложений к приказам, чтобы убедиться в корректной работе всех эндпоинтов.

## Текущее состояние
- API для работы с вложениями к приказам реализовано
- Отсутствуют тесты для API вложений

## Шаги выполнения
1. Создать файл `backend/test/api/order_attachment_api_test.dart`
2. Реализовать тесты для следующих эндпоинтов:
   - `GET /api/order-attachments` - получить все вложения
   - `GET /api/order-attachments/order/:orderId` - получить вложения по ID приказа
   - `GET /api/order-attachments/:id` - получить вложение по ID
   - `POST /api/order-attachments` - создать новое вложение
   - `PUT /api/order-attachments/:id` - обновить вложение
   - `DELETE /api/order-attachments/:id` - удалить вложение
   - `GET /api/order-attachments/:id/download` - скачать вложение
3. Обновить файл `backend/bin/run_tests.dart`, добавив запуск тестов для API вложений

## Особенности реализации
- Для тестирования загрузки файлов использовать тестовый файл .docx
- Для тестирования скачивания файлов проверять корректность заголовков ответа
- Для тестирования удаления файлов проверять отсутствие файла после удаления

## Ожидаемый результат
Набор тестов для API вложений к приказам, который проверяет корректную работу всех эндпоинтов.
