import 'package:json_annotation/json_annotation.dart';

part 'military_rank.g.dart';

@JsonSerializable()
class MilitaryRank {
  final int? id;
  final int employeeId;
  final int rankTypeId;
  final String? dateAssigned;
  final int? orderId;
  final String? createdAt;
  final String? updatedAt;

  MilitaryRank({
    this.id,
    required this.employeeId,
    required this.rankTypeId,
    this.dateAssigned,
    this.orderId,
    this.createdAt,
    this.updatedAt,
  });

  factory MilitaryRank.fromJson(Map<String, dynamic> json) => _$MilitaryRankFromJson(json);

  Map<String, dynamic> toJson() => _$MilitaryRankToJson(this);
}
