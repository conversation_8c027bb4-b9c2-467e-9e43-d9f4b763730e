# Инструкция по исправлению production-сборки HR системы

Эта инструкция поможет вам исправить проблемы с production-сборкой HR системы, связанные с SQLite DLL и путями к базе данных.

## Проблема 1: Несоответствие архитектуры SQLite DLL

Ошибка:
```
Failed to load dynamic library 'sqlite3.dll': %1 is not a valid Win32 application. (error code: 193)
```

Эта ошибка возникает, когда 32-битное приложение пытается загрузить 64-битную DLL (или наоборот). HR система скомпилирована как 32-битное приложение, поэтому ей нужна 32-битная версия SQLite DLL.

### Решение:

1. Скачайте 32-битную версию SQLite DLL:
   - Перейдите на сайт: https://www.sqlite.org/download.html
   - Найдите раздел "Precompiled Binaries for Windows"
   - Скачайте файл "sqlite-dll-win32-x86-3430200.zip" (или более новую версию)
   - Убедитесь, что это именно **win32-x86** версия (32-бит)

2. Замените существующий DLL:
   - Распакуйте скачанный zip-файл
   - Скопируйте файл `sqlite3.dll` в папку `dist`, заменив существующий файл

## Проблема 2: Неправильные пути к базе данных и миграциям

Ошибка:
```
DB directory path: C:\slujeb\BD_OK\db
Migrations directory path: C:\slujeb\BD_OK\migrations
```

Приложение пытается использовать пути к базе данных и миграциям относительно корневой директории проекта, а не относительно исполняемого файла.

### Решение:

1. Создайте необходимые директории в папке `dist`:
   ```
   mkdir dist\db
   mkdir dist\migrations
   ```

2. Скопируйте файлы миграций в папку `dist\migrations`:
   ```
   copy backend\migrations\*.sql dist\migrations\
   ```

## Полная инструкция по исправлению

1. **Скачайте 32-битную версию SQLite DLL**:
   - Перейдите на сайт: https://www.sqlite.org/download.html
   - Скачайте файл "sqlite-dll-win32-x86-3430200.zip" (или более новую версию)
   - Распакуйте zip-файл и найдите файл `sqlite3.dll`

2. **Замените существующий DLL**:
   - Если в папке `dist` уже есть файл `sqlite3.dll`, переименуйте его в `sqlite3.dll.bak`
   - Скопируйте скачанный файл `sqlite3.dll` в папку `dist`

3. **Создайте необходимые директории**:
   ```
   mkdir dist\db
   mkdir dist\migrations
   ```

4. **Скопируйте файлы миграций**:
   ```
   copy backend\migrations\*.sql dist\migrations\
   ```

5. **Запустите приложение**:
   ```
   cd dist
   hr_system.exe
   ```

6. **Проверьте работу приложения**:
   - Откройте браузер и перейдите по адресу http://localhost:8080
   - Убедитесь, что приложение запускается без ошибок
   - Проверьте, что база данных создается в папке `dist\db`

## Долгосрочное решение

Для долгосрочного решения проблемы мы изменили исходный код сервера, чтобы он использовал пути относительно исполняемого файла, а не относительно корневой директории проекта. Эти изменения будут включены в следующую сборку приложения.

Если вы хотите пересобрать приложение с этими изменениями, выполните следующие шаги:

1. Убедитесь, что вы внесли изменения в файл `backend\bin\server.dart`
2. Запустите скрипт сборки:
   ```
   build_production_en.bat
   ```

3. Новая сборка будет создана в папке `dist` и будет использовать правильные пути к базе данных и миграциям.
