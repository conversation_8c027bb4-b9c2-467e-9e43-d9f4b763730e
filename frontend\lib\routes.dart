import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:frontend/pages/dashboard_page.dart';
import 'package:frontend/pages/employee/employee_list_page.dart';
import 'package:frontend/pages/employee/employee_detail_page.dart';
import 'package:frontend/pages/employee/employee_form_page.dart';
import 'package:frontend/pages/position/position_list_page.dart';
import 'package:frontend/pages/position/position_details_page.dart';
import 'package:frontend/pages/position/position_form_page.dart';
import 'package:frontend/pages/order/order_list_page.dart';
import 'package:frontend/pages/order/order_details_page.dart';
import 'package:frontend/pages/order/order_form_page.dart';
import 'package:frontend/pages/api_test_page.dart';
import 'package:frontend/pages/database/database_editor_page.dart';
import 'package:frontend/pages/database/table_editor_page.dart';

final router = GoRouter(
  initialLocation: '/',
  routes: [
    // Главная страница
    GoRoute(path: '/', builder: (context, state) => const DashboardPage()),

    // Маршруты для сотрудников
    GoRoute(
      path: '/employees',
      builder: (context, state) => const EmployeeListPage(),
    ),
    GoRoute(
      path: '/employees/new',
      builder: (context, state) => const EmployeeFormPage(),
    ),
    GoRoute(
      path: '/employees/:id',
      builder: (context, state) {
        final id = int.parse(state.params['id']!);
        return EmployeeDetailPage(employeeId: id.toString());
      },
    ),
    GoRoute(
      path: '/employees/:id/edit',
      builder: (context, state) {
        final id = int.parse(state.params['id']!);
        return EmployeeFormPage(employeeId: id);
      },
    ),

    // Маршруты для должностей
    GoRoute(
      path: '/positions',
      builder: (context, state) => const PositionListPage(),
    ),
    GoRoute(
      path: '/positions/new',
      builder: (context, state) => const PositionFormPage(),
    ),
    GoRoute(
      path: '/positions/:id',
      builder: (context, state) {
        final id = int.parse(state.params['id']!);
        return PositionDetailsPage(positionId: id);
      },
    ),
    GoRoute(
      path: '/positions/:id/edit',
      builder: (context, state) {
        final id = int.parse(state.params['id']!);
        return PositionFormPage(positionId: id);
      },
    ),
    // Маршруты для приказов
    GoRoute(
      path: '/orders',
      builder: (context, state) => const OrderListPage(),
    ),
    GoRoute(
      path: '/orders/new',
      builder: (context, state) => const OrderFormPage(),
    ),
    GoRoute(
      path: '/orders/:id',
      builder: (context, state) {
        final id = int.parse(state.params['id']!);
        return OrderDetailsPage(orderId: id);
      },
    ),
    GoRoute(
      path: '/orders/:id/edit',
      builder: (context, state) {
        final id = int.parse(state.params['id']!);
        return OrderFormPage(orderId: id);
      },
    ),

    // Маршрут для тестирования API
    GoRoute(
      path: '/api-test',
      builder: (context, state) => const ApiTestPage(),
    ),

    // Маршруты для редактора базы данных
    GoRoute(
      path: '/database',
      builder: (context, state) => const DatabaseEditorPage(),
    ),
    GoRoute(
      path: '/database/:tableName',
      builder: (context, state) {
        final tableName = state.params['tableName']!;
        return TableEditorPage(tableName: tableName);
      },
    ),
  ],
  errorBuilder:
      (context, state) => Scaffold(
        appBar: AppBar(title: const Text('Ошибка')),
        body: Center(child: Text('Страница не найдена: ${state.location}')),
      ),
);
