// ignore_for_file: use_build_context_synchronously

# Подзадача: Подавить предупреждения `use_build_context_synchronously` в `employee_details_page.dart`

**Статус:** Pending

## Описание
В файле `frontend/lib/pages/employee/employee_details_page.dart` добавить в начало файла директиву:
```dart
// ignore_for_file: use_build_context_synchronously
```

## Критерии приёмки
* В файле `employee_details_page.dart` не выводятся предупреждения `use_build_context_synchronously` при запуске `flutter analyze`.
* Функциональность страницы работоспособна. 