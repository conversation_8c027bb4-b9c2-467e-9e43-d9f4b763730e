
import 'package:backend/database/database.dart';
import 'package:backend/database/repositories/position_repository.dart';
import 'package:backend/database/repositories/education_detail_repository.dart';
import 'package:backend/database/repositories/employee_repository.dart';
import 'package:backend/models/position.dart';
import 'package:backend/models/education_detail.dart';
import 'package:backend/models/employee.dart';
import 'package:backend/utils/logger.dart';

/// Тестовый скрипт для проверки работы с уровнями образования
void main() async {
  // Инициализируем логгер
  initLogger();

  print('Инициализация базы данных...');
  await AppDatabase.initialize();

  print('Применение миграций...');
  await AppDatabase.applyMigrations();

  // Создаем репозитории
  final positionRepository = PositionRepository();
  final educationDetailRepository = EducationDetailRepository();
  final employeeRepository = EmployeeRepository();

  // Создаем тестовые данные
  await createTestData(
    positionRepository,
    educationDetailRepository,
    employeeRepository,
  );

  // Получаем данные из базы данных
  await retrieveData(
    positionRepository,
    educationDetailRepository,
    employeeRepository,
  );

  // Обновляем данные
  await updateData(
    positionRepository,
    educationDetailRepository,
  );

  // Удаляем данные
  await deleteData(
    positionRepository,
    educationDetailRepository,
    employeeRepository,
  );

  // Закрываем соединение с базой данных
  AppDatabase.close();
  print('Тестирование завершено.');
}

/// Создание тестовых данных
Future<void> createTestData(
  PositionRepository positionRepository,
  EducationDetailRepository educationDetailRepository,
  EmployeeRepository employeeRepository,
) async {
  print('\n=== Создание тестовых данных ===');

  // Создаем сотрудника
  final employee = Employee(
    lastName: 'Петров',
    firstName: 'Петр',
    middleName: 'Петрович',
    gender: 1,
    birthDate: '1990-01-01',
    placeOfBirth: 'г. Москва',
    nationality: 'Русский',
    personalNumber: 'CD-654321',
    childrenUnder16: 1,
    academicDegree: 'Кандидат наук',
    veteranSince: 2018,
  );
  final employeeId = employeeRepository.create(employee);
  print('Создан сотрудник с ID: $employeeId');

  // Создаем должности с разными уровнями образования
  final positions = [
    Position(
      title: 'Младший специалист',
      department: 'Отдел кадров',
      requiredEducationLevel: 2, // Среднее общее образование
    ),
    Position(
      title: 'Специалист',
      department: 'Отдел кадров',
      requiredEducationLevel: 3, // Среднее профессиональное образование
    ),
    Position(
      title: 'Старший специалист',
      department: 'Отдел кадров',
      requiredEducationLevel: 4, // Высшее образование
    ),
  ];

  for (final position in positions) {
    final positionId = positionRepository.create(position);
    print('Создана должность "${position.title}" с ID: $positionId, требуемый уровень образования: ${position.requiredEducationLevelText}');
  }

  // Создаем записи об образовании
  final educationDetails = [
    EducationDetail(
      employeeId: employeeId,
      educationType: 2, // Среднее общее образование
      institution: 'Школа №123',
      graduationDate: '2008-06-15',
    ),
    EducationDetail(
      employeeId: employeeId,
      educationType: 3, // Среднее профессиональное образование
      institution: 'Колледж №456',
      specialty: 'Информационные системы',
      graduationDate: '2012-06-20',
    ),
    EducationDetail(
      employeeId: employeeId,
      educationType: 4, // Высшее образование
      institution: 'Университет №789',
      specialty: 'Информатика и вычислительная техника',
      graduationDate: '2016-06-25',
    ),
  ];

  for (final educationDetail in educationDetails) {
    final educationDetailId = educationDetailRepository.create(educationDetail);
    print('Создана запись об образовании с ID: $educationDetailId, тип образования: ${educationDetail.educationTypeText}');
  }
}

/// Получение данных из базы данных
Future<void> retrieveData(
  PositionRepository positionRepository,
  EducationDetailRepository educationDetailRepository,
  EmployeeRepository employeeRepository,
) async {
  print('\n=== Получение данных из базы данных ===');

  // Получаем все должности
  final positions = positionRepository.getAll();
  print('Получено должностей: ${positions.length}');
  for (final position in positions) {
    print('Должность: ${position.title}, требуемый уровень образования: ${position.requiredEducationLevelText}');
  }

  // Получаем все записи об образовании
  final educationDetails = educationDetailRepository.getAll();
  print('Получено записей об образовании: ${educationDetails.length}');
  for (final educationDetail in educationDetails) {
    print('Образование: ${educationDetail.institution}, тип: ${educationDetail.educationTypeText}');
  }

  // Получаем всех сотрудников
  final employees = employeeRepository.getAll();
  print('Получено сотрудников: ${employees.length}');
  for (final employee in employees) {
    print('Сотрудник: ${employee.lastName} ${employee.firstName} ${employee.middleName}');
    
    // Получаем образование сотрудника
    final employeeEducation = educationDetailRepository.getByEmployeeId(employee.id!);
    print('  Образование:');
    for (final education in employeeEducation) {
      print('    - ${education.institution} (${education.educationTypeText})');
    }
  }
}

/// Обновление данных
Future<void> updateData(
  PositionRepository positionRepository,
  EducationDetailRepository educationDetailRepository,
) async {
  print('\n=== Обновление данных ===');

  // Получаем первую должность
  final positions = positionRepository.getAll();
  if (positions.isNotEmpty) {
    final position = positions.first;
    final updatedPosition = Position(
      id: position.id,
      title: position.title,
      department: position.department,
      unitName: position.unitName,
      womenAllowed: position.womenAllowed,
      militaryRankStaffId: position.militaryRankStaffId,
      vus: position.vus,
      vusPss: position.vusPss,
      positionCodePss: position.positionCodePss,
      tariffCategory: position.tariffCategory,
      isFlightCrew: position.isFlightCrew,
      antiCorruption: position.antiCorruption,
      requiredEducationLevel: 4, // Изменяем на высшее образование
      createdAt: position.createdAt,
      updatedAt: position.updatedAt,
    );
    final success = positionRepository.update(updatedPosition);
    print('Обновление должности: ${success ? 'успешно' : 'не удалось'}');

    // Проверяем, что данные обновились
    final updatedPositionFromDb = positionRepository.getById(position.id!);
    print('Обновленная должность: ${updatedPositionFromDb?.title}, требуемый уровень образования: ${updatedPositionFromDb?.requiredEducationLevelText}');
  }

  // Получаем первую запись об образовании
  final educationDetails = educationDetailRepository.getAll();
  if (educationDetails.isNotEmpty) {
    final educationDetail = educationDetails.first;
    final updatedEducationDetail = EducationDetail(
      id: educationDetail.id,
      employeeId: educationDetail.employeeId,
      educationType: 4, // Изменяем на высшее образование
      institution: educationDetail.institution,
      specialty: educationDetail.specialty,
      graduationDate: educationDetail.graduationDate,
      createdAt: educationDetail.createdAt,
      updatedAt: educationDetail.updatedAt,
    );
    final success = educationDetailRepository.update(updatedEducationDetail);
    print('Обновление записи об образовании: ${success ? 'успешно' : 'не удалось'}');

    // Проверяем, что данные обновились
    final updatedEducationDetailFromDb = educationDetailRepository.getById(educationDetail.id!);
    print('Обновленная запись об образовании: ${updatedEducationDetailFromDb?.institution}, тип образования: ${updatedEducationDetailFromDb?.educationTypeText}');
  }
}

/// Удаление данных
Future<void> deleteData(
  PositionRepository positionRepository,
  EducationDetailRepository educationDetailRepository,
  EmployeeRepository employeeRepository,
) async {
  print('\n=== Удаление данных ===');

  // Получаем все записи об образовании
  final educationDetails = educationDetailRepository.getAll();
  if (educationDetails.isNotEmpty) {
    final educationDetail = educationDetails.first;
    final success = educationDetailRepository.delete(educationDetail.id!);
    print('Удаление записи об образовании: ${success ? 'успешно' : 'не удалось'}');

    // Проверяем, что данные удалились
    final educationDetailsAfterDelete = educationDetailRepository.getAll();
    print('Количество записей об образовании после удаления: ${educationDetailsAfterDelete.length}');
  }

  // Получаем все должности
  final positions = positionRepository.getAll();
  if (positions.isNotEmpty) {
    final position = positions.first;
    final success = positionRepository.delete(position.id!);
    print('Удаление должности: ${success ? 'успешно' : 'не удалось'}');

    // Проверяем, что данные удалились
    final positionsAfterDelete = positionRepository.getAll();
    print('Количество должностей после удаления: ${positionsAfterDelete.length}');
  }

  // Получаем всех сотрудников
  final employees = employeeRepository.getAll();
  if (employees.isNotEmpty) {
    final employee = employees.first;
    final success = employeeRepository.delete(employee.id!);
    print('Удаление сотрудника: ${success ? 'успешно' : 'не удалось'}');

    // Проверяем, что данные удалились
    final employeesAfterDelete = employeeRepository.getAll();
    print('Количество сотрудников после удаления: ${employeesAfterDelete.length}');
  }
}
