_fe_analyzer_shared
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/_fe_analyzer_shared-67.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/_fe_analyzer_shared-67.0.0/lib/
analyzer
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/analyzer-6.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/analyzer-6.4.1/lib/
args
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/args-2.7.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/args-2.7.0/lib/
async
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/async-2.11.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/async-2.11.0/lib/
boolean_selector
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/boolean_selector-2.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/boolean_selector-2.1.1/lib/
build
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build-2.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build-2.4.1/lib/
build_config
2.14
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build_config-1.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build_config-1.1.1/lib/
build_daemon
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build_daemon-4.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build_daemon-4.0.1/lib/
build_resolvers
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build_resolvers-2.4.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build_resolvers-2.4.2/lib/
build_runner
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build_runner-2.4.9/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build_runner-2.4.9/lib/
build_runner_core
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build_runner_core-7.3.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build_runner_core-7.3.0/lib/
built_collection
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/built_collection-5.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/built_collection-5.1.1/lib/
built_value
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/built_value-8.9.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/built_value-8.9.5/lib/
characters
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/characters-1.3.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/characters-1.3.0/lib/
checked_yaml
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/checked_yaml-2.0.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/checked_yaml-2.0.3/lib/
clock
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/clock-1.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/clock-1.1.1/lib/
code_builder
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/code_builder-4.10.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/code_builder-4.10.0/lib/
collection
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/collection-1.18.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/collection-1.18.0/lib/
convert
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/convert-3.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/convert-3.1.1/lib/
crypto
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/crypto-3.0.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/crypto-3.0.3/lib/
cupertino_icons
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cupertino_icons-1.0.8/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cupertino_icons-1.0.8/lib/
dart_style
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dart_style-2.3.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dart_style-2.3.6/lib/
fake_async
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fake_async-1.3.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fake_async-1.3.1/lib/
ffi
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/ffi-2.1.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/ffi-2.1.3/lib/
file
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file-7.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file-7.0.1/lib/
file_picker
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_picker-6.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_picker-6.2.1/lib/
fixnum
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fixnum-1.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fixnum-1.1.1/lib/
flutter_form_builder
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_form_builder-9.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_form_builder-9.2.1/lib/
flutter_lints
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_lints-2.0.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_lints-2.0.3/lib/
flutter_plugin_android_lifecycle
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.19/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.19/lib/
form_builder_validators
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/form_builder_validators-9.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/form_builder_validators-9.1.0/lib/
frontend_server_client
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/frontend_server_client-4.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/frontend_server_client-4.0.0/lib/
glob
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/glob-2.1.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/glob-2.1.3/lib/
go_router
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/go_router-6.5.9/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/go_router-6.5.9/lib/
graphs
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/graphs-2.3.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/graphs-2.3.1/lib/
http
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http-1.2.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http-1.2.2/lib/
http_multi_server
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http_multi_server-3.2.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http_multi_server-3.2.2/lib/
http_parser
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http_parser-4.0.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http_parser-4.0.2/lib/
intl
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/intl-0.18.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/intl-0.18.1/lib/
io
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/io-1.0.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/io-1.0.4/lib/
js
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/js-0.7.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/js-0.7.1/lib/
json_annotation
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/json_annotation-4.9.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/json_annotation-4.9.0/lib/
leak_tracker
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker-10.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker-10.0.0/lib/
leak_tracker_flutter_testing
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_flutter_testing-2.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_flutter_testing-2.0.1/lib/
leak_tracker_testing
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_testing-2.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_testing-2.0.1/lib/
lints
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/lints-2.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/lints-2.1.1/lib/
logging
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/logging-1.2.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/logging-1.2.0/lib/
matcher
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/matcher-0.12.16+1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/matcher-0.12.16+1/lib/
material_color_utilities
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/material_color_utilities-0.8.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/
meta
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/meta-1.11.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/meta-1.11.0/lib/
mime
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/mime-1.0.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/mime-1.0.6/lib/
mockito
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/mockito-5.4.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/mockito-5.4.4/lib/
nested
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/nested-1.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/nested-1.0.0/lib/
package_config
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/package_config-2.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/package_config-2.1.0/lib/
path
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path-1.9.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path-1.9.0/lib/
path_provider_linux
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_linux-2.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/
path_provider_platform_interface
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/
path_provider_windows
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_windows-2.3.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/
platform
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/platform-3.1.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/platform-3.1.6/lib/
plugin_platform_interface
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/plugin_platform_interface-2.1.8/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/
pool
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pool-1.5.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pool-1.5.1/lib/
provider
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/provider-6.1.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/provider-6.1.5/lib/
pub_semver
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pub_semver-2.1.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pub_semver-2.1.4/lib/
pubspec_parse
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pubspec_parse-1.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pubspec_parse-1.4.0/lib/
shared_preferences
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences-2.2.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences-2.2.3/lib/
shared_preferences_android
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_android-2.2.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_android-2.2.2/lib/
shared_preferences_foundation
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_foundation-2.5.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_foundation-2.5.3/lib/
shared_preferences_linux
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_linux-2.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib/
shared_preferences_platform_interface
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/
shared_preferences_web
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_web-2.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_web-2.4.1/lib/
shared_preferences_windows
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_windows-2.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib/
shelf
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shelf-1.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shelf-1.4.1/lib/
shelf_web_socket
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shelf_web_socket-1.0.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shelf_web_socket-1.0.4/lib/
source_gen
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_gen-1.5.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_gen-1.5.0/lib/
source_span
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_span-1.10.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_span-1.10.0/lib/
stack_trace
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stack_trace-1.11.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stack_trace-1.11.1/lib/
stream_channel
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_channel-2.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_channel-2.1.2/lib/
stream_transform
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_transform-2.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_transform-2.1.1/lib/
string_scanner
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/string_scanner-1.2.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/string_scanner-1.2.0/lib/
term_glyph
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/term_glyph-1.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/term_glyph-1.2.1/lib/
test_api
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/test_api-0.6.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/test_api-0.6.1/lib/
timing
2.14
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/timing-1.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/timing-1.0.1/lib/
typed_data
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/typed_data-1.3.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/typed_data-1.3.2/lib/
vector_math
2.14
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_math-2.1.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_math-2.1.4/lib/
vm_service
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vm_service-13.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vm_service-13.0.0/lib/
watcher
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/watcher-1.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/watcher-1.1.1/lib/
web
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web-0.5.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web-0.5.1/lib/
web_socket_channel
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web_socket_channel-2.4.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web_socket_channel-2.4.5/lib/
win32
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/win32-5.5.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/win32-5.5.0/lib/
xdg_directories
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/xdg_directories-1.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/xdg_directories-1.1.0/lib/
yaml
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/yaml-3.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/yaml-3.1.2/lib/
sky_engine
3.2
file:///C:/Users/<USER>/fvm/versions/3.19.0/bin/cache/pkg/sky_engine/
file:///C:/Users/<USER>/fvm/versions/3.19.0/bin/cache/pkg/sky_engine/lib/
flutter
3.2
file:///C:/Users/<USER>/fvm/versions/3.19.0/packages/flutter/
file:///C:/Users/<USER>/fvm/versions/3.19.0/packages/flutter/lib/
flutter_localizations
3.2
file:///C:/Users/<USER>/fvm/versions/3.19.0/packages/flutter_localizations/
file:///C:/Users/<USER>/fvm/versions/3.19.0/packages/flutter_localizations/lib/
flutter_test
3.2
file:///C:/Users/<USER>/fvm/versions/3.19.0/packages/flutter_test/
file:///C:/Users/<USER>/fvm/versions/3.19.0/packages/flutter_test/lib/
flutter_web_plugins
3.2
file:///C:/Users/<USER>/fvm/versions/3.19.0/packages/flutter_web_plugins/
file:///C:/Users/<USER>/fvm/versions/3.19.0/packages/flutter_web_plugins/lib/
frontend
3.3
file:///C:/slujeb/BD_OK/frontend/
file:///C:/slujeb/BD_OK/frontend/lib/
2
