import 'package:flutter/foundation.dart';
import 'package:frontend/models/position.dart';
import 'package:frontend/services/api_service.dart';

/// Провайдер для управления состоянием должностей
class PositionProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();

  List<Position> _positions = [];
  bool _isLoading = false;
  String? _error;
  bool _disposed = false;

  List<Position> get positions => _positions;
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// Безопасный вызов notifyListeners, который проверяет, что объект не был уничтожен
  void _safeNotifyListeners() {
    if (!_disposed) {
      notifyListeners();
    }
  }

  @override
  void dispose() {
    _disposed = true;
    super.dispose();
  }

  /// Получить все должности
  Future<void> fetchPositions() async {
    if (_disposed) return;

    _isLoading = true;
    _error = null;
    _safeNotifyListeners();

    try {
      _positions = await _apiService.getPositions();
      if (_disposed) return;

      _isLoading = false;
      _safeNotifyListeners();
    } catch (e) {
      if (_disposed) return;

      _isLoading = false;
      _error = e.toString();
      _safeNotifyListeners();
    }
  }

  /// Получить должность по ID
  Future<Position?> getPosition(int id) async {
    try {
      return await _apiService.getPosition(id);
    } catch (e) {
      _error = e.toString();
      return null;
    }
  }

  /// Создать новую должность
  Future<bool> createPosition(Position position) async {
    if (_disposed) return false;

    _isLoading = true;
    _error = null;
    _safeNotifyListeners();

    try {
      final id = await _apiService.createPosition(position);
      if (_disposed) return false;

      final newPosition = position.copyWith(id: id);
      _positions.add(newPosition);
      _isLoading = false;
      _safeNotifyListeners();
      return true;
    } catch (e) {
      if (_disposed) return false;

      _isLoading = false;
      _error = e.toString();
      _safeNotifyListeners();
      return false;
    }
  }

  /// Обновить существующую должность
  Future<bool> updatePosition(Position position) async {
    if (_disposed) return false;

    _isLoading = true;
    _error = null;
    _safeNotifyListeners();

    try {
      await _apiService.updatePosition(position);
      if (_disposed) return false;

      final index = _positions.indexWhere((p) => p.id == position.id);
      if (index != -1) {
        _positions[index] = position;
      }

      _isLoading = false;
      _safeNotifyListeners();
      return true;
    } catch (e) {
      if (_disposed) return false;

      _isLoading = false;
      _error = e.toString();
      _safeNotifyListeners();
      return false;
    }
  }

  /// Удалить должность по ID
  Future<bool> deletePosition(int id) async {
    if (_disposed) return false;

    _isLoading = true;
    _error = null;
    _safeNotifyListeners();

    try {
      await _apiService.deletePosition(id);
      if (_disposed) return false;

      _positions.removeWhere((p) => p.id == id);

      _isLoading = false;
      _safeNotifyListeners();
      return true;
    } catch (e) {
      if (_disposed) return false;

      _isLoading = false;
      _error = e.toString();
      _safeNotifyListeners();
      return false;
    }
  }
}
