import 'package:backend/database/database.dart';
import 'package:backend/models/order.dart';
import 'package:logging/logging.dart';

class OrderRepository {
  static final Logger _logger = Logger('OrderRepository');

  /// Получить все приказы
  List<Order> getAll() {
    _logger.info('Getting all orders');

    final result = AppDatabase.select('SELECT * FROM orders');

    return result.map((row) {
      return Order(
        id: row['id'] as int,
        number: row['number'] as String,
        date: row['date'] as String,
        issuedBy: row['issued_by'] as String,
        description: row['description'] as String?,
        createdAt: row['created_at'] as String?,
        updatedAt: row['updated_at'] as String?,
      );
    }).toList();
  }

  /// Получить приказ по ID
  Order? getById(int id) {
    _logger.info('Getting order by ID: $id');

    final result = AppDatabase.select('SELECT * FROM orders WHERE id = ?', [
      id,
    ]);

    if (result.isEmpty) {
      return null;
    }

    final row = result.first;

    return Order(
      id: row['id'] as int,
      number: row['number'] as String,
      date: row['date'] as String,
      issuedBy: row['issued_by'] as String,
      description: row['description'] as String?,
      createdAt: row['created_at'] as String?,
      updatedAt: row['updated_at'] as String?,
    );
  }

  /// Создать новый приказ
  int create(Order order) {
    _logger.info('Creating new order: $order');

    AppDatabase.execute(
      '''
      INSERT INTO orders (
        number, date, issued_by, description
      ) VALUES (?, ?, ?, ?)
      ''',
      [
        order.number,
        order.date,
        order.issuedBy,
        order.description,
      ],
    );

    return AppDatabase.database.lastInsertRowId;
  }

  /// Обновить приказ
  bool update(Order order) {
    _logger.info('Updating order: $order');

    if (order.id == null) {
      throw ArgumentError('Order ID cannot be null');
    }

    AppDatabase.execute(
      '''
      UPDATE orders SET
        number = ?, date = ?, issued_by = ?, description = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
      ''',
      [
        order.number,
        order.date,
        order.issuedBy,
        order.description,
        order.id,
      ],
    );

    return AppDatabase.database.updatedRows > 0;
  }

  /// Удалить приказ
  bool delete(int id) {
    _logger.info('Deleting order with ID: $id');

    AppDatabase.execute('DELETE FROM orders WHERE id = ?', [id]);

    return AppDatabase.database.updatedRows > 0;
  }
}
