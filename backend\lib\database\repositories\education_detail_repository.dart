import 'package:backend/database/database.dart';
import 'package:backend/models/education_detail.dart';
import 'package:logging/logging.dart';

class EducationDetailRepository {
  static final Logger _logger = Logger('EducationDetailRepository');

  /// Получить все записи об образовании
  List<EducationDetail> getAll() {
    _logger.info('Getting all education details');

    final result = AppDatabase.select('SELECT * FROM education_details');

    return result.map((row) {
      // Преобразование строкового значения educationType в int
      var educationTypeValue = row['education_type'];
      int? educationType;
      
      if (educationTypeValue != null) {
        if (educationTypeValue is String) {
          educationType = int.tryParse(educationTypeValue);
        } else if (educationTypeValue is int) {
          educationType = educationTypeValue;
        }
      }

      return EducationDetail(
        id: row['id'] as int,
        employeeId: row['employee_id'] as int,
        educationType: educationType,
        institution: row['institution'] as String,
        specialty: row['specialty'] as String?,
        graduationDate: row['graduation_date'] as String?,
        createdAt: row['created_at'] as String?,
        updatedAt: row['updated_at'] as String?,
      );
    }).toList();
  }

  /// Получить записи об образовании по ID сотрудника
  List<EducationDetail> getByEmployeeId(int employeeId) {
    _logger.info('Getting education details for employee ID: $employeeId');

    final result = AppDatabase.select(
      'SELECT * FROM education_details WHERE employee_id = ?',
      [employeeId],
    );

    return result.map((row) {
      // Преобразование строкового значения educationType в int
      var educationTypeValue = row['education_type'];
      int? educationType;
      
      if (educationTypeValue != null) {
        if (educationTypeValue is String) {
          educationType = int.tryParse(educationTypeValue);
        } else if (educationTypeValue is int) {
          educationType = educationTypeValue;
        }
      }

      return EducationDetail(
        id: row['id'] as int,
        employeeId: row['employee_id'] as int,
        educationType: educationType,
        institution: row['institution'] as String,
        specialty: row['specialty'] as String?,
        graduationDate: row['graduation_date'] as String?,
        createdAt: row['created_at'] as String?,
        updatedAt: row['updated_at'] as String?,
      );
    }).toList();
  }

  /// Получить запись об образовании по ID
  EducationDetail? getById(int id) {
    _logger.info('Getting education detail by ID: $id');

    final result = AppDatabase.select(
      'SELECT * FROM education_details WHERE id = ?',
      [id],
    );

    if (result.isEmpty) {
      return null;
    }

    final row = result.first;

    // Преобразование строкового значения educationType в int
    var educationTypeValue = row['education_type'];
    int? educationType;
    
    if (educationTypeValue != null) {
      if (educationTypeValue is String) {
        educationType = int.tryParse(educationTypeValue);
      } else if (educationTypeValue is int) {
        educationType = educationTypeValue;
      }
    }

    return EducationDetail(
      id: row['id'] as int,
      employeeId: row['employee_id'] as int,
      educationType: educationType,
      institution: row['institution'] as String,
      specialty: row['specialty'] as String?,
      graduationDate: row['graduation_date'] as String?,
      createdAt: row['created_at'] as String?,
      updatedAt: row['updated_at'] as String?,
    );
  }

  /// Создать новую запись об образовании
  int create(EducationDetail educationDetail) {
    _logger.info('Creating new education detail: $educationDetail');

    AppDatabase.execute(
      '''
      INSERT INTO education_details (
        employee_id, education_type, institution, specialty, graduation_date
      ) VALUES (?, ?, ?, ?, ?)
      ''',
      [
        educationDetail.employeeId,
        educationDetail.educationType,
        educationDetail.institution,
        educationDetail.specialty,
        educationDetail.graduationDate,
      ],
    );

    return AppDatabase.database.lastInsertRowId;
  }

  /// Обновить запись об образовании
  bool update(EducationDetail educationDetail) {
    _logger.info('Updating education detail: $educationDetail');

    if (educationDetail.id == null) {
      throw ArgumentError('Education detail ID cannot be null');
    }

    AppDatabase.execute(
      '''
      UPDATE education_details SET
        employee_id = ?, education_type = ?, institution = ?, specialty = ?,
        graduation_date = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
      ''',
      [
        educationDetail.employeeId,
        educationDetail.educationType,
        educationDetail.institution,
        educationDetail.specialty,
        educationDetail.graduationDate,
        educationDetail.id,
      ],
    );

    return AppDatabase.database.updatedRows > 0;
  }

  /// Удалить запись об образовании
  bool delete(int id) {
    _logger.info('Deleting education detail with ID: $id');

    AppDatabase.execute('DELETE FROM education_details WHERE id = ?', [id]);

    return AppDatabase.database.updatedRows > 0;
  }
}
