import 'package:flutter/material.dart';
import 'package:frontend/models/order.dart';
import 'package:frontend/services/order_service.dart';

class OrderProvider with ChangeNotifier {
  final OrderService _orderService = OrderService();
  
  List<Order> _orders = [];
  bool _isLoading = false;
  String? _error;
  
  List<Order> get orders => _orders;
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  /// Получить все приказы
  Future<void> fetchOrders() async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      _orders = await _orderService.getOrders();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }
  
  /// Получить приказ по ID
  Future<Order?> getOrder(int id) async {
    try {
      return await _orderService.getOrder(id);
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }
  
  /// Создать новый приказ
  Future<bool> createOrder(Order order) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      final id = await _orderService.createOrder(order);
      final newOrder = order.copyWith(id: id);
      _orders.add(newOrder);
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }
  
  /// Обновить приказ
  Future<bool> updateOrder(Order order) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      await _orderService.updateOrder(order);
      
      final index = _orders.indexWhere((o) => o.id == order.id);
      if (index != -1) {
        _orders[index] = order;
      }
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }
  
  /// Удалить приказ
  Future<bool> deleteOrder(int id) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      await _orderService.deleteOrder(id);
      
      _orders.removeWhere((o) => o.id == id);
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }
}
