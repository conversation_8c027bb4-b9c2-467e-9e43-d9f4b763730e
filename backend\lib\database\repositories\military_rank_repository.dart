import 'package:backend/database/database.dart';
import 'package:backend/models/military_rank.dart';
import 'package:logging/logging.dart';

class MilitaryRankRepository {
  static final Logger _logger = Logger('MilitaryRankRepository');

  /// Получить все воинские звания
  List<MilitaryRank> getAll() {
    _logger.info('Getting all military ranks');

    final result = AppDatabase.select('SELECT * FROM military_ranks');

    return result.map((row) {
      return MilitaryRank(
        id: row['id'] as int,
        employeeId: row['employee_id'] as int,
        rankTypeId: row['rank_type_id'] as int,
        dateAssigned: row['date_assigned'] as String?,
        orderId: row['order_id'] as int?,
        createdAt: row['created_at'] as String?,
        updatedAt: row['updated_at'] as String?,
      );
    }).toList();
  }

  /// Получить воинское звание по ID
  MilitaryRank? getById(int id) {
    _logger.info('Getting military rank by ID: $id');

    final result = AppDatabase.select('SELECT * FROM military_ranks WHERE id = ?', [
      id,
    ]);

    if (result.isEmpty) {
      return null;
    }

    final row = result.first;

    return MilitaryRank(
      id: row['id'] as int,
      employeeId: row['employee_id'] as int,
      rankTypeId: row['rank_type_id'] as int,
      dateAssigned: row['date_assigned'] as String?,
      orderId: row['order_id'] as int?,
      createdAt: row['created_at'] as String?,
      updatedAt: row['updated_at'] as String?,
    );
  }

  /// Получить воинские звания сотрудника
  List<MilitaryRank> getByEmployeeId(int employeeId) {
    _logger.info('Getting military ranks for employee ID: $employeeId');

    final result = AppDatabase.select('SELECT * FROM military_ranks WHERE employee_id = ?', [
      employeeId,
    ]);

    return result.map((row) {
      return MilitaryRank(
        id: row['id'] as int,
        employeeId: row['employee_id'] as int,
        rankTypeId: row['rank_type_id'] as int,
        dateAssigned: row['date_assigned'] as String?,
        orderId: row['order_id'] as int?,
        createdAt: row['created_at'] as String?,
        updatedAt: row['updated_at'] as String?,
      );
    }).toList();
  }

  /// Создать новое воинское звание
  int create(MilitaryRank militaryRank) {
    _logger.info('Creating new military rank: $militaryRank');

    AppDatabase.execute(
      '''
      INSERT INTO military_ranks (
        employee_id, rank_type_id, date_assigned, order_id
      ) VALUES (?, ?, ?, ?)
      ''',
      [
        militaryRank.employeeId,
        militaryRank.rankTypeId,
        militaryRank.dateAssigned,
        militaryRank.orderId,
      ],
    );

    return AppDatabase.database.lastInsertRowId;
  }

  /// Обновить воинское звание
  bool update(MilitaryRank militaryRank) {
    _logger.info('Updating military rank: $militaryRank');

    if (militaryRank.id == null) {
      throw ArgumentError('Military rank ID cannot be null');
    }

    AppDatabase.execute(
      '''
      UPDATE military_ranks SET
        employee_id = ?, rank_type_id = ?, date_assigned = ?, order_id = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
      ''',
      [
        militaryRank.employeeId,
        militaryRank.rankTypeId,
        militaryRank.dateAssigned,
        militaryRank.orderId,
        militaryRank.id,
      ],
    );

    return AppDatabase.database.updatedRows > 0;
  }

  /// Удалить воинское звание
  bool delete(int id) {
    _logger.info('Deleting military rank with ID: $id');

    AppDatabase.execute('DELETE FROM military_ranks WHERE id = ?', [id]);

    return AppDatabase.database.updatedRows > 0;
  }
}
