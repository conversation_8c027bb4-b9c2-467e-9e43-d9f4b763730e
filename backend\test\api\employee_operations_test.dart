import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:test/test.dart';

void main() {
  const baseUrl = 'http://localhost:8080/api';
  int? employeeId;
  int? positionId;
  int? orderId;
  int? serviceHistoryId;

  group('Employee Operations API Tests', () {
    setUp(() async {
      // Создаем тестового сотрудника
      final employeeData = {
        'lastName': 'Петров',
        'firstName': 'Петр',
        'middleName': 'Петрович',
        'gender': 1,
        'birthDate': '1985-05-15',
        'placeOfBirth': 'г. Санкт-Петербург',
        'nationality': 'Русский',
        'personalNumber': 'EMP-OP-${DateTime.now().millisecondsSinceEpoch}',
        'childrenUnder16': 1,
        'veteranSince': 2018
      };
      
      final employeeResponse = await http.post(
        Uri.parse('$baseUrl/employees'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(employeeData),
      );
      
      final employeeResult = jsonDecode(employeeResponse.body);
      employeeId = employeeResult['data']['id'];
      
      // Создаем тестовую должность
      final positionData = {
        'title': 'Тестовая должность для операций',
        'department': 'Тестовый отдел',
        'unitName': 'Тестовое подразделение',
        'womenAllowed': 1,
        'requiredEducationLevel': 3
      };
      
      final positionResponse = await http.post(
        Uri.parse('$baseUrl/positions'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(positionData),
      );
      
      final positionResult = jsonDecode(positionResponse.body);
      positionId = positionResult['data']['id'];
      
      // Создаем тестовый приказ
      final orderData = {
        'number': 'ORDER-OP-${DateTime.now().millisecondsSinceEpoch}',
        'date': '2023-05-10',
        'title': 'Тестовый приказ для операций',
        'description': 'Описание тестового приказа для операций',
        'orderType': 'Тестовый тип'
      };
      
      final orderResponse = await http.post(
        Uri.parse('$baseUrl/orders'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(orderData),
      );
      
      final orderResult = jsonDecode(orderResponse.body);
      orderId = orderResult['data']['id'];
    });
    
    tearDown(() async {
      // Удаляем тестовые данные
      if (serviceHistoryId != null) {
        await http.delete(Uri.parse('$baseUrl/service-history/$serviceHistoryId'));
      }
      
      if (employeeId != null) {
        await http.delete(Uri.parse('$baseUrl/employees/$employeeId'));
      }
      
      if (positionId != null) {
        await http.delete(Uri.parse('$baseUrl/positions/$positionId'));
      }
      
      if (orderId != null) {
        await http.delete(Uri.parse('$baseUrl/orders/$orderId'));
      }
    });
    
    test('Move Employee to Position', () async {
      if (employeeId == null || positionId == null || orderId == null) {
        fail('Setup failed. One or more required IDs are null.');
      }
      
      final moveData = {
        'positionId': positionId,
        'startDate': '2023-06-01',
        'acceptanceDate': '2023-06-02',
        'orderId': orderId,
        'notes': 'Тестовое перемещение сотрудника'
      };
      
      final response = await http.post(
        Uri.parse('$baseUrl/employees/$employeeId/move'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(moveData),
      );
      
      expect(response.statusCode, equals(200));
      
      final result = jsonDecode(response.body);
      expect(result['success'], isTrue);
      expect(result['data'], isNotNull);
      expect(result['data']['serviceHistoryId'], isNotNull);
      
      serviceHistoryId = result['data']['serviceHistoryId'];
      
      // Проверяем, что запись в послужном списке действительно создана
      final serviceHistoryResponse = await http.get(Uri.parse('$baseUrl/service-history/$serviceHistoryId'));
      expect(serviceHistoryResponse.statusCode, equals(200));
      
      final serviceHistoryResult = jsonDecode(serviceHistoryResponse.body);
      expect(serviceHistoryResult['data']['employeeId'], equals(employeeId));
      expect(serviceHistoryResult['data']['positionId'], equals(positionId));
      expect(serviceHistoryResult['data']['startDate'], equals('2023-06-01'));
    });
    
    test('Get Employee Service History', () async {
      if (employeeId == null || positionId == null || orderId == null) {
        fail('Setup failed. One or more required IDs are null.');
      }
      
      // Сначала перемещаем сотрудника на должность
      final moveData = {
        'positionId': positionId,
        'startDate': '2023-07-01',
        'acceptanceDate': '2023-07-02',
        'orderId': orderId,
        'notes': 'Тестовое перемещение для получения послужного списка'
      };
      
      final moveResponse = await http.post(
        Uri.parse('$baseUrl/employees/$employeeId/move'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(moveData),
      );
      
      final moveResult = jsonDecode(moveResponse.body);
      serviceHistoryId = moveResult['data']['serviceHistoryId'];
      
      // Теперь получаем послужной список сотрудника
      final response = await http.get(Uri.parse('$baseUrl/service-history/employee/$employeeId'));
      
      expect(response.statusCode, equals(200));
      
      final result = jsonDecode(response.body);
      expect(result['success'], isTrue);
      expect(result['data'], isA<List>());
      expect(result['data'].length, greaterThan(0));
      
      // Проверяем, что в списке есть наша запись
      final serviceHistoryRecord = result['data'].firstWhere(
        (record) => record['id'] == serviceHistoryId,
        orElse: () => null,
      );
      
      expect(serviceHistoryRecord, isNotNull);
      expect(serviceHistoryRecord['employeeId'], equals(employeeId));
      expect(serviceHistoryRecord['positionId'], equals(positionId));
    });
    
    test('Validation Error - Missing Required Fields in Move Operation', () async {
      if (employeeId == null) {
        fail('Setup failed. Employee ID is null.');
      }
      
      final invalidData = {
        // Отсутствует обязательное поле positionId
        'startDate': '2023-08-01'
      };
      
      final response = await http.post(
        Uri.parse('$baseUrl/employees/$employeeId/move'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(invalidData),
      );
      
      expect(response.statusCode, equals(400));
      
      final result = jsonDecode(response.body);
      expect(result['success'], isFalse);
      expect(result['error'], equals('Bad Request'));
    });
    
    test('Validation Error - Invalid Date Format in Move Operation', () async {
      if (employeeId == null || positionId == null) {
        fail('Setup failed. Employee ID or Position ID is null.');
      }
      
      final invalidData = {
        'positionId': positionId,
        'startDate': '01.09.2023', // Неверный формат даты (должен быть YYYY-MM-DD)
      };
      
      final response = await http.post(
        Uri.parse('$baseUrl/employees/$employeeId/move'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(invalidData),
      );
      
      expect(response.statusCode, equals(400));
      
      final result = jsonDecode(response.body);
      expect(result['success'], isFalse);
      expect(result['error'], equals('Validation Error'));
    });
  });
}
