import 'package:flutter/material.dart';
import 'package:frontend/models/position.dart';
import 'package:frontend/providers/position_provider.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

class PositionDetailsPage extends StatefulWidget {
  final int positionId;

  const PositionDetailsPage({super.key, required this.positionId});

  @override
  State<PositionDetailsPage> createState() => _PositionDetailsPageState();
}

class _PositionDetailsPageState extends State<PositionDetailsPage> {
  Position? _position;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadPosition();
  }

  Future<void> _loadPosition() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final position = await Provider.of<PositionProvider>(context, listen: false)
          .getPosition(widget.positionId);

      setState(() {
        _position = position;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            // Используем context.go() вместо context.pop() для надежной навигации
            context.go('/positions');
          },
        ),
        title: Text(_position != null ? _position!.title : 'Детали должности'),
        actions: [
          if (_position != null)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () {
                context.go('/positions/${_position!.id}/edit');
              },
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(
                  child: Text(
                    'Ошибка: $_error',
                    style: const TextStyle(color: Colors.red),
                  ),
                )
              : _position == null
                  ? const Center(
                      child: Text('Должность не найдена'),
                    )
                  : SingleChildScrollView(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Card(
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'Основная информация',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const Divider(),
                                  _buildInfoRow('Наименование', _position!.title),
                                  if (_position!.department != null)
                                    _buildInfoRow('Подразделение', _position!.department!),
                                  if (_position!.unitName != null)
                                    _buildInfoRow('Условное наименование части', _position!.unitName!),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),
                          Card(
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'Дополнительная информация',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const Divider(),
                                  _buildInfoRow('Комплектуется женщинами',
                                    _position!.womenAllowed == 1 ? 'Да' : 'Нет'),
                                  if (_position!.vus != null)
                                    _buildInfoRow('ВУС', _position!.vus!),
                                  if (_position!.vusPss != null)
                                    _buildInfoRow('ВУС ПСС', _position!.vusPss!),
                                  if (_position!.positionCodePss != null)
                                    _buildInfoRow('Код ПСС', _position!.positionCodePss!),
                                  if (_position!.tariffCategory != null)
                                    _buildInfoRow('Тарифная категория', _position!.tariffCategory!),
                                  _buildInfoRow('Летный состав',
                                    _position!.isFlightCrew == 1 ? 'Да' : 'Нет'),
                                  _buildInfoRow('Антикоррупционная',
                                    _position!.antiCorruption == 1 ? 'Да' : 'Нет'),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 150,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}
