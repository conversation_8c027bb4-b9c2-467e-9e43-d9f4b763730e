import 'package:shelf_router/shelf_router.dart';
import 'package:backend/controllers/position_controller.dart';

/// Создает маршрутизатор для работы с должностями
Router positionRoutes() {
  final controller = PositionController();
  final router = Router();

  // Получить все должности
  router.get('/', controller.getAll);

  // Получить должность по ID
  router.get('/<id>', controller.getById);

  // Создать новую должность
  router.post('/', controller.create);

  // Обновить должность
  router.put('/<id>', controller.update);

  // Удалить должность
  router.delete('/<id>', controller.delete);

  return router;
}
