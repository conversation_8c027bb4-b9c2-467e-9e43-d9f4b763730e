# Инструкции по тестированию REST API

В этом документе описаны инструкции по тестированию REST API, созданного для HR-системы.

## Подготовка к тестированию

Перед запуском тестов убедитесь, что:

1. Установлены все зависимости:

```bash
cd backend
dart pub get
```

2. Запущен сервер:

```bash
cd backend
dart run bin/server.dart
```

Сервер должен быть доступен по адресу `http://localhost:8080`.

## Тестирование через скрипт Dart

Для быстрой проверки доступности API и основных функций выполните:

```bash
cd backend
dart run bin/test_api.dart
```

Скрипт выполнит следующие операции:
- Проверит доступность API
- Создаст тестовые данные (тип звания, воинское звание, должность, сотрудник, приказ)
- Выполнит операцию перемещения сотрудника на должность
- Получит данные из API и выведет их в консоль
- Удалит созданные тестовые данные

Результаты каждой операции будут выведены в консоль.

## Запуск всех тестов API

Для запуска всех тестов API выполните:

```bash
cd backend
dart run bin/run_tests.dart
```

Этот скрипт запустит все тесты API, включая:
- Тесты для сотрудников (`employee_api_test.dart`)
- Тесты для должностей (`position_api_test.dart`)
- Тесты для операций с сотрудниками (`employee_operations_test.dart`)

## Запуск отдельных тестов

Вы также можете запустить отдельные тесты:

```bash
# Тесты для сотрудников
dart test test/api/employee_api_test.dart

# Тесты для должностей
dart test test/api/position_api_test.dart

# Тесты для операций с сотрудниками
dart test test/api/employee_operations_test.dart
```

## Ручное тестирование API

Для ручного тестирования API вы можете использовать любой HTTP-клиент, например, `curl` или Postman.

### Примеры запросов с использованием `curl`:

#### Получение списка сотрудников:
```bash
curl http://localhost:8080/api/employees
```

#### Создание нового сотрудника:
```bash
curl -X POST http://localhost:8080/api/employees \
  -H "Content-Type: application/json" \
  -d '{
    "lastName": "Иванов",
    "firstName": "Иван",
    "middleName": "Иванович",
    "gender": 1,
    "birthDate": "1990-01-01",
    "placeOfBirth": "г. Москва",
    "nationality": "Русский",
    "personalNumber": "EMP-123456",
    "childrenUnder16": 2,
    "academicDegree": "Кандидат наук",
    "veteranSince": 2015
  }'
```

#### Получение сотрудника по ID:
```bash
curl http://localhost:8080/api/employees/1
```

#### Обновление сотрудника:
```bash
curl -X PUT http://localhost:8080/api/employees/1 \
  -H "Content-Type: application/json" \
  -d '{
    "lastName": "Иванов",
    "firstName": "Иван",
    "middleName": "Петрович"
  }'
```

#### Удаление сотрудника:
```bash
curl -X DELETE http://localhost:8080/api/employees/1
```

#### Перемещение сотрудника на должность:
```bash
curl -X POST http://localhost:8080/api/employees/1/move \
  -H "Content-Type: application/json" \
  -d '{
    "positionId": 1,
    "startDate": "2023-01-01",
    "acceptanceDate": "2023-01-02",
    "orderId": 1,
    "notes": "Тестовое перемещение"
  }'
```

## Проверка результатов тестирования

При успешном выполнении тестов вы должны увидеть сообщения об успешном выполнении каждого теста. Если тесты не проходят, проверьте:

1. Запущен ли сервер
2. Доступен ли сервер по адресу `http://localhost:8080`
3. Правильно ли настроена база данных
4. Применены ли миграции

## Известные проблемы и их решение

### Проблема: Сервер не запускается

**Решение**: Проверьте, не занят ли порт 8080 другим процессом. Если порт занят, измените порт в файле `bin/server.dart`.

### Проблема: Ошибка "Database is locked"

**Решение**: Убедитесь, что у вас нет других процессов, которые используют базу данных. Перезапустите сервер.

### Проблема: Тесты не проходят из-за ошибок валидации

**Решение**: Проверьте, что данные, отправляемые в тестах, соответствуют требованиям валидации. Обратите внимание на обязательные поля и форматы данных.

## Дополнительная информация

Для получения дополнительной информации о структуре API и доступных эндпоинтах обратитесь к документации API или к исходному коду в директориях:

- `lib/controllers/` - контроллеры для обработки запросов
- `lib/routes/` - маршруты API
- `lib/models/` - модели данных
