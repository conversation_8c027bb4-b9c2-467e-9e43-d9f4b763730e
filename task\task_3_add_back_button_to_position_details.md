# Подзадача: Добавление кнопки "Назад" на страницу детального просмотра должности

## Описание
Добавить кнопку "Назад" в AppBar на странице детального просмотра должности для возврата к предыдущему экрану.

## Выполнено
- Добавлен элемент `leading` в AppBar компонента `PositionDetailsPage`
- Добавлена кнопка с иконкой стрелки назад
- Настроен обработчик нажатия с использованием `context.go('/positions')` для надежного возврата к списку должностей

## Изменения в коде
Файл: `frontend/lib/pages/position/position_details_page.dart`
```dart
appBar: AppBar(
  leading: IconButton(
    icon: const Icon(Icons.arrow_back),
    onPressed: () {
      // Используем context.go() вместо context.pop() для надежной навигации
      context.go('/positions');
    },
  ),
  title: Text(_position != null ? _position!.title : 'Детали должности'),
  // ...
),
```

## Статус
Завершено
