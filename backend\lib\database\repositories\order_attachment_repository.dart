import 'dart:typed_data';
import 'package:backend/database/database.dart';
import 'package:backend/models/order_attachment.dart';
import 'package:logging/logging.dart';

/// Репозиторий для работы с вложениями к приказам
class OrderAttachmentRepository {
  static final Logger _logger = Logger('OrderAttachmentRepository');

  /// Получить все вложения к приказам
  List<OrderAttachment> getAll() {
    _logger.info('Getting all order attachments');

    final result = AppDatabase.select('SELECT * FROM order_attachments');

    return result.map((row) {
      return OrderAttachment(
        id: row['id'] as int,
        orderId: row['order_id'] as int,
        fileName: row['file_name'] as String,
        data: _convertBlobToBytes(row['data']),
        uploadedAt: row['uploaded_at'] as String?,
        createdAt: row['created_at'] as String?,
        updatedAt: row['updated_at'] as String?,
      );
    }).toList();
  }

  /// Получить вложения к приказу по ID приказа
  List<OrderAttachment> getByOrderId(int orderId) {
    _logger.info('Getting order attachments for order ID: $orderId');

    final result = AppDatabase.select(
      'SELECT * FROM order_attachments WHERE order_id = ?',
      [orderId],
    );

    return result.map((row) {
      return OrderAttachment(
        id: row['id'] as int,
        orderId: row['order_id'] as int,
        fileName: row['file_name'] as String,
        data: _convertBlobToBytes(row['data']),
        uploadedAt: row['uploaded_at'] as String?,
        createdAt: row['created_at'] as String?,
        updatedAt: row['updated_at'] as String?,
      );
    }).toList();
  }

  /// Получить вложение к приказу по ID
  OrderAttachment? getById(int id) {
    _logger.info('Getting order attachment by ID: $id');

    final result = AppDatabase.select(
      'SELECT * FROM order_attachments WHERE id = ?',
      [id],
    );

    if (result.isEmpty) {
      _logger.warning('Order attachment with ID $id not found');
      return null;
    }

    final row = result.first;

    // Проверяем наличие данных
    if (row['data'] == null) {
      _logger.severe('Data is null for order attachment with ID $id');
    } else {
      _logger.info(
          'Data type for order attachment with ID $id: ${row['data'].runtimeType}');
    }

    final data = _convertBlobToBytes(row['data']);
    _logger.info(
        'Converted data length for order attachment with ID $id: ${data.length}');

    return OrderAttachment(
      id: row['id'] as int,
      orderId: row['order_id'] as int,
      fileName: row['file_name'] as String,
      data: data,
      uploadedAt: row['uploaded_at'] as String?,
      createdAt: row['created_at'] as String?,
      updatedAt: row['updated_at'] as String?,
    );
  }

  /// Создать новое вложение к приказу
  int create(OrderAttachment attachment) {
    _logger.info(
        'Creating new order attachment: ${attachment.fileName} for order ID: ${attachment.orderId}');
    _logger.info('Attachment data length: ${attachment.data.length}');

    if (attachment.data.isEmpty) {
      _logger.severe('Trying to create attachment with empty data');
    }

    AppDatabase.execute(
      '''
      INSERT INTO order_attachments (
        order_id, file_name, data
      ) VALUES (?, ?, ?)
      ''',
      [
        attachment.orderId,
        attachment.fileName,
        attachment.data,
      ],
    );

    final lastId = AppDatabase.database.lastInsertRowId;
    _logger.info('Created order attachment with ID: $lastId');

    // Проверяем, что данные сохранились корректно
    final createdAttachment = getById(lastId);
    if (createdAttachment != null) {
      _logger.info(
          'Verified created attachment data length: ${createdAttachment.data.length}');
    } else {
      _logger.severe('Failed to verify created attachment with ID: $lastId');
    }

    return lastId;
  }

  /// Обновить вложение к приказу
  bool update(OrderAttachment attachment) {
    _logger.info('Updating order attachment: ${attachment.id}');

    if (attachment.id == null) {
      throw ArgumentError('Order attachment ID cannot be null');
    }

    AppDatabase.execute(
      '''
      UPDATE order_attachments SET
        order_id = ?, file_name = ?, data = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
      ''',
      [
        attachment.orderId,
        attachment.fileName,
        attachment.data,
        attachment.id,
      ],
    );

    return AppDatabase.database.updatedRows > 0;
  }

  /// Удалить вложение к приказу
  bool delete(int id) {
    _logger.info('Deleting order attachment: $id');

    AppDatabase.execute(
      'DELETE FROM order_attachments WHERE id = ?',
      [id],
    );

    return AppDatabase.database.updatedRows > 0;
  }

  /// Преобразовать BLOB в List<int>
  List<int> _convertBlobToBytes(dynamic blob) {
    if (blob == null) {
      _logger.severe('Blob is null');
      return [];
    } else if (blob is List<int>) {
      _logger.info('Blob is List<int> with length: ${blob.length}');
      return blob;
    } else if (blob is Uint8List) {
      _logger.info('Blob is Uint8List with length: ${blob.length}');
      return blob.toList();
    } else {
      _logger.warning('Unknown blob type: ${blob.runtimeType}');
      try {
        if (blob.toString() == 'null') {
          _logger.severe('Blob toString() is "null"');
          return [];
        }
        _logger.info('Trying to convert blob to string: ${blob.toString()}');
      } catch (e) {
        _logger.severe('Error converting blob to string: $e');
      }
      return [];
    }
  }
}
