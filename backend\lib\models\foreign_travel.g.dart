// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'foreign_travel.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ForeignTravel _$ForeignTravelFromJson(Map<String, dynamic> json) =>
    ForeignTravel(
      id: (json['id'] as num?)?.toInt(),
      employeeId: (json['employeeId'] as num).toInt(),
      country: json['country'] as String,
      startDate: json['startDate'] as String?,
      endDate: json['endDate'] as String?,
      createdAt: json['createdAt'] as String?,
      updatedAt: json['updatedAt'] as String?,
    );

Map<String, dynamic> _$ForeignTravelToJson(ForeignTravel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'employeeId': instance.employeeId,
      'country': instance.country,
      'startDate': instance.startDate,
      'endDate': instance.endDate,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
    };
