import 'dart:convert';
import 'package:shelf/shelf.dart';
import 'package:backend/database/database.dart';
import 'package:logging/logging.dart';

/// Контроллер для работы с таблицами базы данных
class TableController {
  static final Logger _logger = Logger('TableController');

  /// Получить список таблиц
  Response getTables(Request request) {
    _logger.info('Getting all tables');

    try {
      final tables = _getTables();

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': tables,
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error getting tables: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Получить данные из таблицы
  Response getTableData(Request request, String tableName) {
    _logger.info('Getting data from table: $tableName');

    try {
      // Проверяем, существует ли таблица
      final tables = _getTables();
      if (!tables.contains(tableName)) {
        return Response.notFound(
          jsonEncode({
            'success': false,
            'error': 'Not Found',
            'message': 'Table not found: $tableName',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      // Получаем данные из таблицы
      final data = AppDatabase.select('SELECT * FROM $tableName');

      // Получаем информацию о столбцах таблицы
      final columns = _getTableColumns(tableName);

      // Получаем метаданные столбцов
      final columnsMetadata = _getTableColumnsMetadata(tableName);

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': {
            'columns': columns,
            'columnsMetadata': columnsMetadata,
            'rows': data,
          },
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error getting table data: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Получить метаданные таблицы
  Response getTableMetadata(Request request, String tableName) {
    _logger.info('Getting metadata for table: $tableName');

    try {
      // Проверяем, существует ли таблица
      final tables = _getTables();
      if (!tables.contains(tableName)) {
        return Response.notFound(
          jsonEncode({
            'success': false,
            'error': 'Not Found',
            'message': 'Table not found: $tableName',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      // Получаем метаданные столбцов
      final columnsMetadata = _getTableColumnsMetadata(tableName);

      // Добавляем специальные метаданные для определенных таблиц и полей
      if (tableName == 'employees' && columnsMetadata.any((col) => col['name'] == 'gender')) {
        final genderColumn = columnsMetadata.firstWhere((col) => col['name'] == 'gender');
        genderColumn['enumValues'] = ['Мужской', 'Женский'];
      }

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': {
            'columnsMetadata': columnsMetadata,
          },
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error getting table metadata: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Обновить данные в таблице
  Future<Response> updateTableRow(Request request, String tableName, String id) async {
    _logger.info('Updating row in table: $tableName, id: $id');

    try {
      // Проверяем, существует ли таблица
      final tables = _getTables();
      if (!tables.contains(tableName)) {
        return Response.notFound(
          jsonEncode({
            'success': false,
            'error': 'Not Found',
            'message': 'Table not found: $tableName',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      // Получаем данные из запроса
      final jsonBody = await request.readAsString();
      final Map<String, dynamic> data = jsonDecode(jsonBody);

      // Получаем информацию о столбцах таблицы
      final columns = _getTableColumns(tableName);

      // Формируем SQL-запрос для обновления данных
      final setClause = columns
          .where((column) => column != 'id' && data.containsKey(column))
          .map((column) => '$column = ?')
          .join(', ');

      final params = columns
          .where((column) => column != 'id' && data.containsKey(column))
          .map((column) => data[column])
          .toList();

      params.add(id);

      // Обновляем данные
      AppDatabase.execute(
        'UPDATE $tableName SET $setClause WHERE id = ?',
        params,
      );

      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'Row updated successfully',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error updating table row: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Добавить строку в таблицу
  Future<Response> addTableRow(Request request, String tableName) async {
    _logger.info('Adding row to table: $tableName');

    try {
      // Проверяем, существует ли таблица
      final tables = _getTables();
      if (!tables.contains(tableName)) {
        return Response.notFound(
          jsonEncode({
            'success': false,
            'error': 'Not Found',
            'message': 'Table not found: $tableName',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      // Получаем данные из запроса
      final jsonBody = await request.readAsString();
      final Map<String, dynamic> data = jsonDecode(jsonBody);

      // Получаем информацию о столбцах таблицы
      final columns = _getTableColumns(tableName)
          .where((column) => column != 'id' && data.containsKey(column))
          .toList();

      // Формируем SQL-запрос для добавления данных
      final columnsClause = columns.join(', ');
      final valuesPlaceholders = List.filled(columns.length, '?').join(', ');

      final params = columns.map((column) => data[column]).toList();

      // Добавляем данные
      AppDatabase.execute(
        'INSERT INTO $tableName ($columnsClause) VALUES ($valuesPlaceholders)',
        params,
      );

      final id = AppDatabase.database.lastInsertRowId;

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': {'id': id},
          'message': 'Row added successfully',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error adding table row: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Удалить строку из таблицы
  Response deleteTableRow(Request request, String tableName, String id) {
    _logger.info('Deleting row from table: $tableName, id: $id');

    try {
      // Проверяем, существует ли таблица
      final tables = _getTables();
      if (!tables.contains(tableName)) {
        return Response.notFound(
          jsonEncode({
            'success': false,
            'error': 'Not Found',
            'message': 'Table not found: $tableName',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      // Удаляем данные
      AppDatabase.execute(
        'DELETE FROM $tableName WHERE id = ?',
        [id],
      );

      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'Row deleted successfully',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error deleting table row: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Получить список таблиц
  List<String> _getTables() {
    final result = AppDatabase.select(
      "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' AND name != 'migrations'",
    );

    return result.map((row) => row['name'] as String).toList();
  }

  /// Получить информацию о столбцах таблицы
  List<String> _getTableColumns(String tableName) {
    final result = AppDatabase.select('PRAGMA table_info($tableName)');

    return result.map((row) => row['name'] as String).toList();
  }

  /// Получить метаданные столбцов таблицы
  List<Map<String, dynamic>> _getTableColumnsMetadata(String tableName) {
    final result = AppDatabase.select('PRAGMA table_info($tableName)');

    return result.map((row) {
      // Определяем тип данных на основе типа в SQLite
      String dataType = 'text';
      final sqliteType = (row['type'] as String).toLowerCase();

      if (sqliteType.contains('int')) {
        dataType = 'integer';
      } else if (sqliteType.contains('real') || sqliteType.contains('float') || sqliteType.contains('double')) {
        dataType = 'real';
      } else if (sqliteType.contains('blob')) {
        dataType = 'blob';
      } else if (sqliteType.contains('date') && !sqliteType.contains('datetime')) {
        dataType = 'date';
      } else if (sqliteType.contains('time') && !sqliteType.contains('datetime')) {
        dataType = 'time';
      } else if (sqliteType.contains('datetime') || sqliteType.contains('timestamp')) {
        dataType = 'datetime';
      } else if (sqliteType.contains('bool')) {
        dataType = 'boolean';
      }

      // Для полей с определенными именами устанавливаем специальные типы
      final name = row['name'] as String;
      if (name.endsWith('_date') || name == 'date' || name == 'birth_date' || name == 'start_date' || name == 'end_date') {
        dataType = 'date';
      } else if (name == 'gender') {
        dataType = 'enumType';
      } else if (name.contains('is_') || name.endsWith('_allowed') || name.contains('_enabled')) {
        dataType = 'boolean';
      }

      return {
        'name': name,
        'dataType': dataType,
        'isRequired': (row['notnull'] as int) == 1,
        'isPrimaryKey': (row['pk'] as int) == 1,
        'defaultValue': row['dflt_value'],
      };
    }).toList();
  }
}
