# Подзадача: Создание страниц для работы с послужным списком

## Описание
Необходимо создать страницы для работы с послужным списком сотрудников, включая список записей послужного списка, страницу деталей записи и форму создания/редактирования записи. Послужной список является важной частью системы отдела кадров, так как он отражает историю службы сотрудника.

## Текущее состояние
- API для работы с послужным списком уже реализовано на бэкенде
- Модель `ServiceHistory` определена как в бэкенде, так и во фронтенде
- Отсутствуют страницы для работы с послужным списком во фронтенде

## Необходимые изменения
1. Создать провайдер для работы с послужным списком:
   - `frontend/lib/providers/service_history_provider.dart` - провайдер для управления состоянием послужного списка

2. Создать сервис для работы с API послужного списка:
   - `frontend/lib/services/service_history_service.dart` - сервис для взаимодействия с API послужного списка

3. Создать страницы для работы с послужным списком:
   - `frontend/lib/pages/service_history/service_history_list_page.dart` - страница со списком записей послужного списка
   - `frontend/lib/pages/service_history/service_history_details_page.dart` - страница с деталями записи
   - `frontend/lib/pages/service_history/service_history_form_page.dart` - форма создания/редактирования записи

4. Обновить маршруты в `frontend/lib/routes.dart`:
   - Добавить маршруты для страниц послужного списка

5. Обновить страницу деталей сотрудника в `frontend/lib/pages/employee/employee_details_page.dart`:
   - Добавить раздел с послужным списком сотрудника
   - Добавить кнопку для добавления новой записи в послужной список

## Детали реализации
1. Страница списка записей послужного списка (`service_history_list_page.dart`):
   - Отображение списка записей с основной информацией (сотрудник, должность, период)
   - Возможность фильтрации по сотруднику и периоду
   - Кнопка для создания новой записи
   - Кнопки для просмотра и редактирования каждой записи

2. Страница деталей записи послужного списка (`service_history_details_page.dart`):
   - Отображение всей информации о записи
   - Отображение связанного сотрудника и должности
   - Отображение связанного приказа
   - Кнопка для редактирования записи
   - Кнопка для удаления записи

3. Форма создания/редактирования записи послужного списка (`service_history_form_page.dart`):
   - Поля для ввода всех данных записи
   - Выбор сотрудника из выпадающего списка
   - Выбор должности из выпадающего списка
   - Выбор приказа из выпадающего списка
   - Поля для ввода дат (начало, окончание, приём дел, сдача дел)
   - Валидация обязательных полей
   - Кнопки для сохранения и отмены

## Ожидаемый результат
- Полнофункциональные страницы для работы с послужным списком
- Возможность просмотра, создания, редактирования и удаления записей послужного списка
- Интеграция с API бэкенда
- Удобный пользовательский интерфейс с валидацией ввода
- Интеграция с деталями сотрудника для быстрого доступа к послужному списку конкретного сотрудника
