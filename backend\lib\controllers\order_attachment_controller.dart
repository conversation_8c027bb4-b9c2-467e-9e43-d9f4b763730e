import 'dart:convert';
import 'dart:typed_data';
import 'package:backend/database/repositories/order_attachment_repository.dart';
import 'package:backend/models/order_attachment.dart';
import 'package:logging/logging.dart';
import 'package:shelf/shelf.dart';

/// Контроллер для обработки запросов к API вложений к приказам
class OrderAttachmentController {
  static final Logger _logger = Logger('OrderAttachmentController');
  final OrderAttachmentRepository _repository = OrderAttachmentRepository();

  /// Получить все вложения к приказам
  Response getAll(Request request) {
    _logger.info('Getting all order attachments');

    try {
      final attachments = _repository.getAll();

      // Не возвращаем данные файлов в списке, только метаданные
      final attachmentsWithoutData = attachments.map((attachment) {
        final map = attachment.toJson();
        map.remove('data'); // Удаляем данные файла из ответа
        return map;
      }).toList();

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': attachmentsWithoutData,
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error getting all order attachments: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Получить вложения к приказу по ID приказа
  Response getByOrderId(Request request, String orderId) {
    _logger.info('Getting order attachments for order ID: $orderId');

    try {
      final orderIdInt = int.parse(orderId);
      final attachments = _repository.getByOrderId(orderIdInt);

      // Не возвращаем данные файлов в списке, только метаданные
      final attachmentsWithoutData = attachments.map((attachment) {
        final map = attachment.toJson();
        map.remove('data'); // Удаляем данные файла из ответа
        return map;
      }).toList();

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': attachmentsWithoutData,
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe(
          'Error getting order attachments for order ID $orderId: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Получить вложение к приказу по ID
  Response getById(Request request, String id) {
    _logger.info('Getting order attachment by ID: $id');

    try {
      final idInt = int.parse(id);
      final attachment = _repository.getById(idInt);

      if (attachment == null) {
        return Response.notFound(
          jsonEncode({
            'success': false,
            'error': 'Not Found',
            'message': 'Order attachment with ID $id not found',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      // Не возвращаем данные файла, только метаданные
      final attachmentWithoutData = attachment.toJson();
      attachmentWithoutData.remove('data'); // Удаляем данные файла из ответа

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': attachmentWithoutData,
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger
          .severe('Error getting order attachment by ID $id: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Создать новое вложение к приказу
  Future<Response> create(Request request) async {
    _logger.info('Creating new order attachment');

    try {
      final jsonBody = await request.readAsString();
      _logger.info('Received request body length: ${jsonBody.length}');

      final Map<String, dynamic> data = jsonDecode(jsonBody);

      if (!data.containsKey('orderId') ||
          !data.containsKey('fileName') ||
          !data.containsKey('fileData')) {
        _logger.warning('Missing required fields in request');
        return Response.badRequest(
          body: jsonEncode({
            'success': false,
            'error': 'Bad Request',
            'message': 'Missing required fields: orderId, fileName, fileData',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final orderId = data['orderId'] as int;
      final fileName = data['fileName'] as String;
      final fileDataBase64 = data['fileData'] as String;

      _logger.info('Processing attachment: $fileName for order ID: $orderId');
      _logger.info('Base64 data length: ${fileDataBase64.length}');

      if (fileDataBase64.isEmpty) {
        _logger.warning('Empty file data received');
        return Response.badRequest(
          body: jsonEncode({
            'success': false,
            'error': 'Bad Request',
            'message': 'File data is empty',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      // Декодируем данные файла из base64
      final fileData = base64Decode(fileDataBase64);
      _logger.info('Decoded file data length: ${fileData.length} bytes');

      if (fileData.isEmpty) {
        _logger.warning('Decoded file data is empty');
        return Response.badRequest(
          body: jsonEncode({
            'success': false,
            'error': 'Bad Request',
            'message': 'Decoded file data is empty',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final attachment = OrderAttachment(
        orderId: orderId,
        fileName: fileName,
        data: fileData,
      );

      final id = _repository.create(attachment);
      _logger.info('Created order attachment with ID: $id');

      // Проверяем, что данные сохранились корректно
      final savedAttachment = _repository.getById(id);
      if (savedAttachment != null) {
        _logger.info(
            'Verified saved attachment data length: ${savedAttachment.data.length} bytes');
      } else {
        _logger.severe('Failed to verify saved attachment with ID: $id');
      }

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': {'id': id},
          'message': 'Order attachment created successfully',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error creating order attachment: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Обновить вложение к приказу
  Future<Response> update(Request request, String id) async {
    _logger.info('Updating order attachment with ID: $id');

    try {
      final idInt = int.parse(id);
      final existingAttachment = _repository.getById(idInt);

      if (existingAttachment == null) {
        return Response.notFound(
          jsonEncode({
            'success': false,
            'error': 'Not Found',
            'message': 'Order attachment with ID $id not found',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final jsonBody = await request.readAsString();
      final Map<String, dynamic> data = jsonDecode(jsonBody);

      final orderId = data['orderId'] as int? ?? existingAttachment.orderId;
      final fileName =
          data['fileName'] as String? ?? existingAttachment.fileName;

      List<int> fileData = existingAttachment.data;
      if (data.containsKey('fileData')) {
        final fileDataBase64 = data['fileData'] as String;
        fileData = base64Decode(fileDataBase64);
      }

      final attachment = OrderAttachment(
        id: idInt,
        orderId: orderId,
        fileName: fileName,
        data: fileData,
      );

      final success = _repository.update(attachment);

      if (!success) {
        return Response.internalServerError(
          body: jsonEncode({
            'success': false,
            'error': 'Internal Server Error',
            'message': 'Failed to update order attachment',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'Order attachment updated successfully',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe(
          'Error updating order attachment with ID $id: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Удалить вложение к приказу
  Response delete(Request request, String id) {
    _logger.info('Deleting order attachment with ID: $id');

    try {
      final idInt = int.parse(id);
      final existingAttachment = _repository.getById(idInt);

      if (existingAttachment == null) {
        return Response.notFound(
          jsonEncode({
            'success': false,
            'error': 'Not Found',
            'message': 'Order attachment with ID $id not found',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final success = _repository.delete(idInt);

      if (!success) {
        return Response.internalServerError(
          body: jsonEncode({
            'success': false,
            'error': 'Internal Server Error',
            'message': 'Failed to delete order attachment',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'Order attachment deleted successfully',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe(
          'Error deleting order attachment with ID $id: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Скачать вложение к приказу
  Response download(Request request, String id) {
    _logger.info('Downloading order attachment with ID: $id');

    try {
      final idInt = int.parse(id);
      final attachment = _repository.getById(idInt);

      if (attachment == null) {
        _logger.warning('Order attachment with ID $id not found');
        return Response.notFound(
          jsonEncode({
            'success': false,
            'error': 'Not Found',
            'message': 'Order attachment with ID $id not found',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      // Проверяем данные файла
      if (attachment.data.isEmpty) {
        _logger.severe('Attachment data is empty for ID: $id');
        return Response.internalServerError(
          body: jsonEncode({
            'success': false,
            'error': 'Internal Server Error',
            'message': 'Attachment data is empty',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      // Определяем тип контента на основе расширения файла
      String contentType = _getContentTypeFromFileName(attachment.fileName);
      _logger.info('Content type for ${attachment.fileName}: $contentType');
      _logger.info('File size: ${attachment.data.length} bytes');
      _logger.info('File name: ${attachment.fileName}');

      // Кодируем имя файла для заголовка Content-Disposition
      String contentDisposition = _encodeFileName(attachment.fileName);
      _logger.info('Content-Disposition: $contentDisposition');

      // Возвращаем файл с правильными заголовками для скачивания
      return Response.ok(
        Uint8List.fromList(attachment.data),
        headers: {
          'Content-Type': contentType,
          'Content-Disposition': contentDisposition,
          'Content-Length': '${attachment.data.length}',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
          'X-Content-Type-Options': 'nosniff',
          'X-Filename': Uri.encodeComponent(attachment.fileName),
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
          'Access-Control-Allow-Headers':
              'Origin, Content-Type, Accept, Authorization, X-Requested-With',
          'Access-Control-Allow-Credentials': 'true',
          'Access-Control-Expose-Headers':
              'Content-Disposition, Content-Type, Content-Length, X-Filename',
        },
      );
    } catch (e, stackTrace) {
      _logger.severe(
          'Error downloading order attachment with ID $id: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Определить тип контента на основе расширения файла
  String _getContentTypeFromFileName(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();

    switch (extension) {
      case 'pdf':
        return 'application/pdf';
      case 'doc':
        return 'application/msword';
      case 'docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case 'xls':
        return 'application/vnd.ms-excel';
      case 'xlsx':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case 'ppt':
        return 'application/vnd.ms-powerpoint';
      case 'pptx':
        return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'txt':
        return 'text/plain';
      case 'html':
      case 'htm':
        return 'text/html';
      case 'csv':
        return 'text/csv';
      case 'zip':
        return 'application/zip';
      case 'rar':
        return 'application/x-rar-compressed';
      case '7z':
        return 'application/x-7z-compressed';
      default:
        return 'application/octet-stream'; // Бинарный поток по умолчанию
    }
  }

  /// Кодирует имя файла для использования в заголовке Content-Disposition
  ///
  /// Решает проблему с кириллическими символами в имени файла
  String _encodeFileName(String fileName) {
    // Метод 1: RFC 5987 с URL-кодированием
    // return "attachment; filename*=UTF-8''${Uri.encodeComponent(fileName)}";

    // Метод 2: Использование ASCII-имени с добавлением оригинального имени в кодировке Base64
    // Получаем расширение файла
    final extension = fileName.split('.').last;

    // Создаем ASCII-имя для совместимости
    final asciiName = 'file.$extension';

    // Кодируем оригинальное имя в UTF-8 для URL-кодирования
    // Формируем заголовок с двумя вариантами имени файла
    return 'attachment; filename="$asciiName"; filename*=UTF-8\'\'${Uri.encodeComponent(fileName)}';
  }
}
