import 'package:json_annotation/json_annotation.dart';

part 'attestation.g.dart';

@JsonSerializable()
class Attestation {
  final int? id;
  final int employeeId;
  final String date;
  final String? result;
  final String? createdAt;
  final String? updatedAt;

  Attestation({
    this.id,
    required this.employeeId,
    required this.date,
    this.result,
    this.createdAt,
    this.updatedAt,
  });

  factory Attestation.fromJson(Map<String, dynamic> json) => _$AttestationFromJson(json);

  Map<String, dynamic> toJson() => _$AttestationToJson(this);
}
