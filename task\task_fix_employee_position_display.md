# Task: Исправление отображения и обновления должности сотрудника

**Goal:** Исправить отображение должности сотрудника в форме редактирования и на странице деталей сотрудника, а также корректно обновлять должность при редактировании.

**Sub-tasks:**

1. **[X] `task_1_create_main_task_file.md`**: Создание файла задачи.
   * Status: Completed
2. **[X] `task_2_load_current_position.md`**: Добавить загрузку текущей должности сотрудника при редактировании.
   * Status: Completed
3. **[X] `task_3_display_position_in_details.md`**: Добавить отображение должности на странице деталей сотрудника.
   * Status: Completed
4. **[X] `task_4_fix_position_update.md`**: Исправить обновление должности при редактировании сотрудника.
   * Status: Completed
5. **[X] `task_5_test_position_update.md`**: Протестировать решение и убедиться, что должность корректно обновляется.
   * Status: Completed

**Desired Outcome:**
* При редактировании сотрудника в выпадающем списке должностей предварительно выбрана текущая должность сотрудника
* На странице деталей сотрудника отображается информация о его текущей должности
* При изменении должности сотрудника создается новая запись в послужном списке, а предыдущая запись закрывается
* Изменение должности сотрудника корректно сохраняется и отображается при повторном редактировании
