import 'dart:io';
import 'package:logging/logging.dart';

/// Скрипт для запуска всех тестов API
void main() async {
  // Настройка логгера
  Logger.root.level = Level.ALL;
  Logger.root.onRecord.listen((record) {
    print('${record.level.name}: ${record.time}: ${record.message}');
  });

  final logger = Logger('TestRunner');

  logger.info('Запуск тестов API...');

  try {
    // Проверка доступности API
    logger.info('Проверка доступности API...');
    final result = await Process.run('dart', ['run', 'bin/test_api.dart']);

    if (result.exitCode != 0) {
      logger.severe('Ошибка при проверке доступности API:');
      print(result.stdout);
      print(result.stderr);
      exit(1);
    }

    logger.info('API доступен. Запуск тестов...');

    // Запуск тестов для сотрудников
    logger.info('Запуск тестов для сотрудников...');
    final employeeTestResult = await Process.run('dart', ['test', 'test/api/employee_api_test.dart']);

    if (employeeTestResult.exitCode != 0) {
      logger.severe('Ошибка при выполнении тестов для сотрудников:');
      print(employeeTestResult.stdout);
      print(employeeTestResult.stderr);
    } else {
      logger.info('Тесты для сотрудников успешно выполнены');
      print(employeeTestResult.stdout);
    }

    // Запуск тестов для должностей
    logger.info('Запуск тестов для должностей...');
    final positionTestResult = await Process.run('dart', ['test', 'test/api/position_api_test.dart']);

    if (positionTestResult.exitCode != 0) {
      logger.severe('Ошибка при выполнении тестов для должностей:');
      print(positionTestResult.stdout);
      print(positionTestResult.stderr);
    } else {
      logger.info('Тесты для должностей успешно выполнены');
      print(positionTestResult.stdout);
    }

    // Запуск тестов для операций с сотрудниками
    logger.info('Запуск тестов для операций с сотрудниками...');
    final operationsTestResult = await Process.run('dart', ['test', 'test/api/employee_operations_test.dart']);

    if (operationsTestResult.exitCode != 0) {
      logger.severe('Ошибка при выполнении тестов для операций с сотрудниками:');
      print(operationsTestResult.stdout);
      print(operationsTestResult.stderr);
    } else {
      logger.info('Тесты для операций с сотрудниками успешно выполнены');
      print(operationsTestResult.stdout);
    }

    // Запуск тестов для вложений к приказам
    logger.info('Запуск тестов для вложений к приказам...');
    final attachmentTestResult = await Process.run('dart', ['test', 'test/api/order_attachment_api_test.dart']);

    if (attachmentTestResult.exitCode != 0) {
      logger.severe('Ошибка при выполнении тестов для вложений к приказам:');
      print(attachmentTestResult.stdout);
      print(attachmentTestResult.stderr);
    } else {
      logger.info('Тесты для вложений к приказам успешно выполнены');
      print(attachmentTestResult.stdout);
    }

    logger.info('Все тесты выполнены');
  } catch (e, stackTrace) {
    logger.severe('Ошибка при запуске тестов: $e\n$stackTrace');
  }
}
