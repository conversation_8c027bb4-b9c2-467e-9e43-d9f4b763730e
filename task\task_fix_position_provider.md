# Task: Исправление ошибки в PositionProvider

**Goal:** Исправить ошибку в PositionProvider, которая вызывает проблемы с уведомлениями и приводит к сбоям в Flutter приложении.

**Sub-tasks:**

1. **[X] `task_1_create_main_task_file.md`**: Создание файла задачи.
   * Status: Completed
2. **[X] `task_2_fix_position_provider_notifications.md`**: Исправить проблему с уведомлениями в PositionProvider.
   * Status: Completed
3. **[X] `task_3_update_position_model.md`**: Обновить модель Position во frontend для соответствия с backend моделью.
   * Status: Completed
4. **[X] `task_4_test_position_provider.md`**: Протестировать исправленный PositionProvider.
   * Status: Completed

**Desired Outcome:**
* Исправленный PositionProvider, который корректно отправляет уведомления
* Обновленная модель Position, которая соответствует backend модели
* Отсутствие ошибок при работе с PositionProvider
* Стабильная работа приложения без сбоев, связанных с PositionProvider
