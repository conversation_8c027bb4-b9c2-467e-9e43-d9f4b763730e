# Task: Настройка бэкенда на Dart с Shelf

**Goal:** Настроить бэкенд для системы отдела кадров, используя Dart и Shelf.

**Steps:**

1. Проанализировать текущую структуру бэкенда в папке `backend`
2. Обновить `pubspec.yaml` для добавления необходимых зависимостей:
   - `shelf` - для создания веб-сервера
   - `shelf_router` - для маршрутизации запросов
   - `sqlite3` - для работы с SQLite
   - `path` - для работы с путями к файлам
   - `shelf_static` - для раздачи статических файлов
   - `shelf_cors` - для обработки CORS-запросов
   - `json_serializable` - для сериализации/десериализации JSON
3. Создать структуру директорий в папке `lib`:
   ```
   backend/lib/
   ├── controllers/        # Контроллеры для обработки запросов
   ├── models/             # Модели данных
   ├── routes/             # Маршруты API
   ├── database/           # Работа с базой данных
   ├── middleware/         # Промежуточное ПО
   └── utils/              # Вспомогательные функции
   ```
4. Создать директорию `migrations` для SQL-файлов миграций
5. Настроить подключение к базе данных SQLite в `lib/database/database.dart`
6. Обновить `bin/server.dart` для настройки базового сервера с поддержкой CORS и логированием
7. Настроить раздачу статических файлов из директории `web`
8. Создать базовый маршрутизатор в `lib/routes/router.dart`

**Expected Result:**
- Работающий Dart-сервер на базе Shelf, запускающийся на указанном порту
- Настроенное подключение к базе данных SQLite
- Базовая структура проекта для дальнейшей разработки API
- Настроенная раздача статических файлов

**Status:** Completed
