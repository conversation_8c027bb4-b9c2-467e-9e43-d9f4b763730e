import 'package:logging/logging.dart';

/// Класс для валидации входных данных
class Validator {
  static final Logger _logger = Logger('Validator');

  /// Проверяет, что строка не пустая
  static bool isNotEmpty(String? value) {
    return value != null && value.trim().isNotEmpty;
  }

  /// Проверяет, что значение является целым числом
  static bool isInteger(dynamic value) {
    if (value is int) return true;
    if (value is String) {
      try {
        int.parse(value);
        return true;
      } catch (_) {
        return false;
      }
    }
    return false;
  }

  /// Проверяет, что значение является числом
  static bool isNumeric(dynamic value) {
    if (value is num) return true;
    if (value is String) {
      try {
        num.parse(value);
        return true;
      } catch (_) {
        return false;
      }
    }
    return false;
  }

  /// Проверяет, что строка является корректной датой в формате YYYY-MM-DD
  static bool isDate(String? value) {
    if (value == null || value.isEmpty) return false;
    
    try {
      final parts = value.split('-');
      if (parts.length != 3) return false;
      
      final year = int.parse(parts[0]);
      final month = int.parse(parts[1]);
      final day = int.parse(parts[2]);
      
      if (year < 1900 || year > 2100) return false;
      if (month < 1 || month > 12) return false;
      if (day < 1 || day > 31) return false;
      
      // Проверка на корректность дня в месяце
      if (month == 2) {
        // Проверка на високосный год
        final isLeapYear = (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
        if (day > (isLeapYear ? 29 : 28)) return false;
      } else if ([4, 6, 9, 11].contains(month) && day > 30) {
        return false;
      }
      
      return true;
    } catch (_) {
      return false;
    }
  }

  /// Проверяет, что строка является корректным email
  static bool isEmail(String? value) {
    if (value == null || value.isEmpty) return false;
    
    final emailRegExp = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    
    return emailRegExp.hasMatch(value);
  }

  /// Проверяет, что строка содержит только буквы
  static bool isAlpha(String? value) {
    if (value == null || value.isEmpty) return false;
    
    final alphaRegExp = RegExp(r'^[a-zA-Zа-яА-Я]+$');
    
    return alphaRegExp.hasMatch(value);
  }

  /// Проверяет, что строка содержит только буквы и цифры
  static bool isAlphanumeric(String? value) {
    if (value == null || value.isEmpty) return false;
    
    final alphanumericRegExp = RegExp(r'^[a-zA-Zа-яА-Я0-9]+$');
    
    return alphanumericRegExp.hasMatch(value);
  }

  /// Проверяет, что значение находится в указанном диапазоне
  static bool isInRange(num? value, num min, num max) {
    if (value == null) return false;
    
    return value >= min && value <= max;
  }

  /// Проверяет, что значение находится в указанном списке
  static bool isInList<T>(T? value, List<T> list) {
    if (value == null) return false;
    
    return list.contains(value);
  }

  /// Проверяет, что объект содержит все указанные поля
  static bool hasRequiredFields(Map<String, dynamic> data, List<String> requiredFields) {
    for (final field in requiredFields) {
      if (!data.containsKey(field) || data[field] == null) {
        _logger.warning('Required field missing: $field');
        return false;
      }
    }
    
    return true;
  }

  /// Валидирует данные сотрудника
  static Map<String, String> validateEmployee(Map<String, dynamic> data) {
    final errors = <String, String>{};
    
    // Проверка обязательных полей
    if (!isNotEmpty(data['lastName'] as String?)) {
      errors['lastName'] = 'Фамилия обязательна для заполнения';
    }
    
    if (!isNotEmpty(data['firstName'] as String?)) {
      errors['firstName'] = 'Имя обязательно для заполнения';
    }
    
    if (data.containsKey('gender') && data['gender'] != null) {
      final gender = data['gender'];
      if (!isInteger(gender) || !isInList(gender, [0, 1])) {
        errors['gender'] = 'Пол должен быть 0 (женский) или 1 (мужской)';
      }
    }
    
    if (data.containsKey('birthDate') && data['birthDate'] != null) {
      if (!isDate(data['birthDate'] as String?)) {
        errors['birthDate'] = 'Дата рождения должна быть в формате YYYY-MM-DD';
      }
    }
    
    if (data.containsKey('childrenUnder16') && data['childrenUnder16'] != null) {
      if (!isInteger(data['childrenUnder16']) || !isInRange(data['childrenUnder16'] as num?, 0, 20)) {
        errors['childrenUnder16'] = 'Количество детей до 16 лет должно быть целым числом от 0 до 20';
      }
    }
    
    if (data.containsKey('veteranSince') && data['veteranSince'] != null) {
      if (!isInteger(data['veteranSince']) || !isInRange(data['veteranSince'] as num?, 1950, 2100)) {
        errors['veteranSince'] = 'Год получения статуса ветерана должен быть целым числом от 1950 до 2100';
      }
    }
    
    return errors;
  }

  /// Валидирует данные должности
  static Map<String, String> validatePosition(Map<String, dynamic> data) {
    final errors = <String, String>{};
    
    // Проверка обязательных полей
    if (!isNotEmpty(data['title'] as String?)) {
      errors['title'] = 'Название должности обязательно для заполнения';
    }
    
    if (data.containsKey('womenAllowed') && data['womenAllowed'] != null) {
      if (!isInteger(data['womenAllowed']) || !isInList(data['womenAllowed'], [0, 1])) {
        errors['womenAllowed'] = 'Разрешено для женщин должно быть 0 (нет) или 1 (да)';
      }
    }
    
    if (data.containsKey('isFlightCrew') && data['isFlightCrew'] != null) {
      if (!isInteger(data['isFlightCrew']) || !isInList(data['isFlightCrew'], [0, 1])) {
        errors['isFlightCrew'] = 'Летный состав должен быть 0 (нет) или 1 (да)';
      }
    }
    
    if (data.containsKey('antiCorruption') && data['antiCorruption'] != null) {
      if (!isInteger(data['antiCorruption']) || !isInList(data['antiCorruption'], [0, 1])) {
        errors['antiCorruption'] = 'Антикоррупционная должность должна быть 0 (нет) или 1 (да)';
      }
    }
    
    if (data.containsKey('requiredEducationLevel') && data['requiredEducationLevel'] != null) {
      if (!isInteger(data['requiredEducationLevel']) || !isInRange(data['requiredEducationLevel'] as num?, 0, 5)) {
        errors['requiredEducationLevel'] = 'Требуемый уровень образования должен быть целым числом от 0 до 5';
      }
    }
    
    return errors;
  }
}
