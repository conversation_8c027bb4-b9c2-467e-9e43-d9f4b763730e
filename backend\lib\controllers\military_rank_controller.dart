import 'dart:convert';
import 'package:shelf/shelf.dart';
import 'package:backend/database/repositories/military_rank_repository.dart';
import 'package:backend/models/military_rank.dart';
import 'package:logging/logging.dart';

class MilitaryRankController {
  static final Logger _logger = Logger('MilitaryRankController');
  final MilitaryRankRepository _repository = MilitaryRankRepository();

  /// Получить все воинские звания
  Response getAll(Request request) {
    _logger.info('Getting all military ranks');

    try {
      final militaryRanks = _repository.getAll();

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': militaryRanks.map((e) => e.toJson()).toList(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error getting all military ranks: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Получить воинское звание по ID
  Response getById(Request request, String id) {
    _logger.info('Getting military rank by ID: $id');

    try {
      final militaryRankId = int.parse(id);
      final militaryRank = _repository.getById(militaryRankId);

      if (militaryRank == null) {
        return Response.notFound(
          jsonEncode({
            'success': false,
            'error': 'Not Found',
            'message': 'Military rank with ID $id not found',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': militaryRank.toJson(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error getting military rank by ID: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Создать новое воинское звание
  Future<Response> create(Request request) async {
    _logger.info('Creating new military rank');

    try {
      final jsonBody = await request.readAsString();
      final Map<String, dynamic> data = jsonDecode(jsonBody);

      final militaryRank = MilitaryRank(
        employeeId: data['employeeId'],
        rankTypeId: data['rankTypeId'],
        dateAssigned: data['dateAssigned'],
        orderId: data['orderId'],
      );

      final id = _repository.create(militaryRank);

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': {'id': id},
          'message': 'Military rank created successfully',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error creating military rank: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Обновить воинское звание
  Future<Response> update(Request request, String id) async {
    _logger.info('Updating military rank with ID: $id');

    try {
      final militaryRankId = int.parse(id);
      final existingMilitaryRank = _repository.getById(militaryRankId);

      if (existingMilitaryRank == null) {
        return Response.notFound(
          jsonEncode({
            'success': false,
            'error': 'Not Found',
            'message': 'Military rank with ID $id not found',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final jsonBody = await request.readAsString();
      final Map<String, dynamic> data = jsonDecode(jsonBody);

      final militaryRank = MilitaryRank(
        id: militaryRankId,
        employeeId: data['employeeId'] ?? existingMilitaryRank.employeeId,
        rankTypeId: data['rankTypeId'] ?? existingMilitaryRank.rankTypeId,
        dateAssigned: data['dateAssigned'] ?? existingMilitaryRank.dateAssigned,
        orderId: data['orderId'] ?? existingMilitaryRank.orderId,
      );

      final success = _repository.update(militaryRank);

      if (!success) {
        return Response.internalServerError(
          body: jsonEncode({
            'success': false,
            'error': 'Internal Server Error',
            'message': 'Failed to update military rank',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'Military rank updated successfully',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error updating military rank: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Удалить воинское звание
  Response delete(Request request, String id) {
    _logger.info('Deleting military rank with ID: $id');

    try {
      final militaryRankId = int.parse(id);
      final existingMilitaryRank = _repository.getById(militaryRankId);

      if (existingMilitaryRank == null) {
        return Response.notFound(
          jsonEncode({
            'success': false,
            'error': 'Not Found',
            'message': 'Military rank with ID $id not found',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final success = _repository.delete(militaryRankId);

      if (!success) {
        return Response.internalServerError(
          body: jsonEncode({
            'success': false,
            'error': 'Internal Server Error',
            'message': 'Failed to delete military rank',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'Military rank deleted successfully',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error deleting military rank: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }
}
