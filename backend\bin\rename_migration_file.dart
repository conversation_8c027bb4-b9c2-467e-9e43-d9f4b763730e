import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:backend/database/database.dart';
import 'package:backend/utils/logger.dart';

/// Скрипт для переименования файла миграции и обновления записи в таблице migrations
void main() async {
  // Инициализируем логгер
  initLogger();

  print('Renaming migration file...');
  
  // Получаем путь к исполняемому файлу
  final executablePath = Platform.script.toFilePath();
  print('Executable path: $executablePath');
  
  // Получаем директорию, в которой находится исполняемый файл
  final executableDir = path.dirname(executablePath);
  print('Executable directory: $executableDir');
  
  // Получаем корневую директорию проекта (на один уровень выше bin)
  final rootDir = path.dirname(executableDir);
  print('Root directory: $rootDir');
  
  // Создаем пути к директориям относительно корневой директории
  final dbDirPath = path.join(rootDir, 'db');
  final migrationsDirPath = path.join(rootDir, 'migrations');
  
  print('DB directory path: $dbDirPath');
  print('Migrations directory path: $migrationsDirPath');
  
  // Инициализируем базу данных
  print('Initializing database...');
  await AppDatabase.initialize(customDbDir: dbDirPath);
  print('Database initialized successfully');

  // Переименовываем файл миграции
  final oldFileName = '19_add_position_id_to_employees.sql';
  final newFileName = '21_add_position_id_to_employees.sql';
  
  final oldFilePath = path.join(migrationsDirPath, oldFileName);
  final newFilePath = path.join(migrationsDirPath, newFileName);
  
  print('Renaming file from $oldFilePath to $newFilePath');
  
  try {
    final oldFile = File(oldFilePath);
    if (await oldFile.exists()) {
      await oldFile.rename(newFilePath);
      print('File renamed successfully');
      
      // Обновляем запись в таблице миграций
      print('Updating migration record in database...');
      AppDatabase.execute(
        'UPDATE migrations SET name = ? WHERE name = ?',
        [newFileName, oldFileName],
      );
      
      print('Migration record updated successfully');
      
      // Проверяем, что запись обновлена
      final migrations = AppDatabase.select(
        'SELECT name FROM migrations WHERE name = ?',
        [newFileName],
      ).map((row) => row['name'] as String).toList();
      
      if (migrations.isNotEmpty) {
        print('Migration name updated to: ${migrations.first}');
      } else {
        print('Failed to update migration name');
      }
    } else {
      print('File $oldFilePath does not exist');
    }
  } catch (e) {
    print('Error renaming file: $e');
  }
  
  // Закрываем соединение с базой данных
  AppDatabase.close();
  print('\nDone.');
}
