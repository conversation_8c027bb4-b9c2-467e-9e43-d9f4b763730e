import 'package:flutter/material.dart';
import 'package:frontend/components/app_drawer.dart';
import 'package:frontend/models/employee.dart';
import 'package:frontend/providers/employee_provider.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

class EmployeeListPage extends StatefulWidget {
  const EmployeeListPage({super.key});

  @override
  State<EmployeeListPage> createState() => _EmployeeListPageState();
}

class _EmployeeListPageState extends State<EmployeeListPage> {
  @override
  void initState() {
    super.initState();
    // Загружаем данные при инициализации страницы
    Future.microtask(() {
      if (mounted) {
        Provider.of<EmployeeProvider>(context, listen: false).fetchEmployees();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final employeeProvider = Provider.of<EmployeeProvider>(context);

    return Scaffold(
      appBar: AppBar(title: const Text('Сотрудники')),
      drawer: const AppDrawer(),
      body:
          employeeProvider.isLoading
              ? const Center(child: CircularProgressIndicator())
              : employeeProvider.error != null
              ? Center(
                child: Text(
                  'Ошибка: ${employeeProvider.error}',
                  style: const TextStyle(color: Colors.red),
                ),
              )
              : employeeProvider.employees.isEmpty
              ? const Center(child: Text('Нет данных о сотрудниках'))
              : ListView.builder(
                itemCount: employeeProvider.employees.length,
                itemBuilder: (context, index) {
                  final employee = employeeProvider.employees[index];
                  return _buildEmployeeListItem(context, employee);
                },
              ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          context.go('/employees/new');
        },
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        tooltip: 'Добавить нового сотрудника',
        child: const Icon(Icons.add, size: 28),
      ),
    );
  }

  Widget _buildEmployeeListItem(BuildContext context, Employee employee) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        title: Text(
          employee.fullName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (employee.personalNumber != null)
              Text('Личный номер: ${employee.personalNumber}'),
            Text('Пол: ${employee.genderText}'),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () {
                context.go('/employees/${employee.id}/edit');
              },
            ),
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: () {
                _showDeleteConfirmationDialog(context, employee);
              },
            ),
          ],
        ),
        onTap: () {
          context.go('/employees/${employee.id}');
        },
      ),
    );
  }

  Future<void> _showDeleteConfirmationDialog(
    BuildContext context,
    Employee employee,
  ) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('Подтверждение удаления'),
          content: SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                Text(
                  'Вы уверены, что хотите удалить сотрудника ${employee.fullName}?',
                ),
                const Text('Это действие нельзя будет отменить.'),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Отмена'),
              onPressed: () {
                Navigator.of(dialogContext).pop();
              },
            ),
            TextButton(
              child: const Text('Удалить'),
              onPressed: () {
                Navigator.of(dialogContext).pop();
                _deleteEmployee(employee);
              },
            ),
          ],
        );
      },
    );
  }

  // Метод для удаления сотрудника
  Future<void> _deleteEmployee(Employee employee) async {
    if (employee.id != null) {
      // Сохраняем ссылку на провайдер перед асинхронной операцией
      final provider = Provider.of<EmployeeProvider>(context, listen: false);
      final success = await provider.deleteEmployee(employee.id!);

      // Проверяем, что виджет все еще в дереве виджетов
      if (!mounted) return;

      // Используем BuildContext только после проверки mounted
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Сотрудник успешно удален')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Ошибка при удалении сотрудника: ${provider.error}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
