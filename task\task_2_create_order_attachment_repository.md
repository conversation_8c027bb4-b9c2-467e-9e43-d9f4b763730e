# Подзадача: Создание репозитория для работы с вложениями к приказам

## Описание
Необходимо создать репозиторий для работы с вложениями к приказам, который будет обеспечивать базовые CRUD-операции для таблицы `order_attachments`.

## Текущее состояние
- Таблица `order_attachments` уже создана в базе данных
- Модель `OrderAttachment` уже определена в `backend/lib/models/order_attachment.dart`
- Отсутствует репозиторий для работы с вложениями к приказам

## Шаги выполнения
1. Создать файл `backend/lib/database/repositories/order_attachment_repository.dart`
2. Реализовать класс `OrderAttachmentRepository` со следующими методами:
   - `List<OrderAttachment> getAll()` - получить все вложения
   - `List<OrderAttachment> getByOrderId(int orderId)` - получить вложения по ID приказа
   - `OrderAttachment? getById(int id)` - получить вложение по ID
   - `int create(OrderAttachment attachment)` - создать новое вложение
   - `bool update(OrderAttachment attachment)` - обновить вложение
   - `bool delete(int id)` - удалить вложение

## Ожидаемый результат
Репозиторий для работы с вложениями к приказам, который обеспечивает все необходимые операции для работы с таблицей `order_attachments` в базе данных.
