import 'package:flutter/material.dart';
import 'package:frontend/models/column_metadata.dart';
import 'package:frontend/services/table_service.dart';

class TableProvider with ChangeNotifier {
  final TableService _tableService = TableService();

  List<String> _tables = [];
  String? _selectedTable;
  Map<String, dynamic> _tableData = {};
  List<ColumnMetadata> _columnsMetadata = [];
  bool _isLoading = false;
  String? _error;

  List<String> get tables => _tables;
  String? get selectedTable => _selectedTable;
  Map<String, dynamic> get tableData => _tableData;
  List<ColumnMetadata> get columnsMetadata => _columnsMetadata;
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// Получить список таблиц
  Future<void> fetchTables() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _tables = await _tableService.getTables();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  /// Выбрать таблицу и загрузить ее данные
  Future<void> selectTable(String tableName) async {
    _selectedTable = tableName;
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Загружаем данные таблицы
      _tableData = await _tableService.getTableData(tableName);

      // Загружаем метаданные таблицы
      _columnsMetadata = await _tableService.getTableMetadata(tableName);

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  /// Получить метаданные для столбца
  ColumnMetadata? getColumnMetadata(String columnName) {
    try {
      return _columnsMetadata.firstWhere((metadata) => metadata.name == columnName);
    } catch (e) {
      return null;
    }
  }

  /// Обновить строку в таблице
  Future<void> updateTableRow(String id, Map<String, dynamic> rowData) async {
    if (_selectedTable == null) {
      _error = 'No table selected';
      notifyListeners();
      return;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Преобразуем данные в соответствии с типами столбцов
      final processedData = _processRowData(rowData);

      await _tableService.updateTableRow(_selectedTable!, id, processedData);

      // Обновляем данные в таблице
      await selectTable(_selectedTable!);
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  /// Добавить строку в таблицу
  Future<void> addTableRow(Map<String, dynamic> rowData) async {
    if (_selectedTable == null) {
      _error = 'No table selected';
      notifyListeners();
      return;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Преобразуем данные в соответствии с типами столбцов
      final processedData = _processRowData(rowData);

      await _tableService.addTableRow(_selectedTable!, processedData);

      // Обновляем данные в таблице
      await selectTable(_selectedTable!);
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  /// Удалить строку из таблицы
  Future<void> deleteTableRow(String id) async {
    if (_selectedTable == null) {
      _error = 'No table selected';
      notifyListeners();
      return;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _tableService.deleteTableRow(_selectedTable!, id);

      // Обновляем данные в таблице
      await selectTable(_selectedTable!);
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  /// Преобразовать данные строки в соответствии с типами столбцов
  Map<String, dynamic> _processRowData(Map<String, dynamic> rowData) {
    final result = <String, dynamic>{};

    rowData.forEach((key, value) {
      final metadata = getColumnMetadata(key);

      if (metadata != null) {
        switch (metadata.dataType) {
          case ColumnDataType.integer:
            if (value is String && value.isNotEmpty) {
              result[key] = int.tryParse(value) ?? 0;
            } else if (value is int) {
              result[key] = value;
            } else {
              result[key] = null;
            }
            break;
          case ColumnDataType.real:
            if (value is String && value.isNotEmpty) {
              result[key] = double.tryParse(value) ?? 0.0;
            } else if (value is num) {
              result[key] = value;
            } else {
              result[key] = null;
            }
            break;
          case ColumnDataType.boolean:
            if (value is String) {
              result[key] = value.toLowerCase() == 'true' || value == '1' ? 1 : 0;
            } else if (value is bool) {
              result[key] = value ? 1 : 0;
            } else if (value is int) {
              result[key] = value > 0 ? 1 : 0;
            } else {
              result[key] = 0;
            }
            break;
          default:
            result[key] = value;
        }
      } else {
        result[key] = value;
      }
    });

    return result;
  }
}
