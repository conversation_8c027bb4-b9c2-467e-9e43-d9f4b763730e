import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:test/test.dart';

void main() {
  const String baseUrl = 'http://localhost:8080/api';
  int? testOrderId;
  int? testAttachmentId;
  
  // Создаем тестовый файл .docx для тестирования
  final testFilePath = 'test/api/test_file.docx';
  late Uint8List testFileData;
  
  setUpAll(() async {
    // Проверяем, существует ли тестовый файл
    final testFile = File(testFilePath);
    if (!await testFile.exists()) {
      // Если файл не существует, создаем простой .docx файл
      // Это просто пример, в реальности нужно использовать настоящий .docx файл
      testFileData = Uint8List.fromList(utf8.encode('Test file content'));
      await testFile.writeAsBytes(testFileData);
    } else {
      testFileData = await testFile.readAsBytes();
    }
    
    // Создаем тестовый приказ для использования в тестах
    final orderResponse = await http.post(
      Uri.parse('$baseUrl/orders'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'number': 'TEST-${DateTime.now().millisecondsSinceEpoch}',
        'date': DateTime.now().toIso8601String().split('T').first,
        'issuedBy': 'Test API',
        'description': 'Test order for attachment API tests',
      }),
    );
    
    if (orderResponse.statusCode != 200) {
      fail('Failed to create test order: ${orderResponse.body}');
    }
    
    final orderData = jsonDecode(orderResponse.body);
    testOrderId = orderData['data']['id'];
    
    print('Created test order with ID: $testOrderId');
  });
  
  tearDownAll(() async {
    // Удаляем тестовый приказ после завершения тестов
    if (testOrderId != null) {
      await http.delete(Uri.parse('$baseUrl/orders/$testOrderId'));
      print('Deleted test order with ID: $testOrderId');
    }
  });
  
  group('Order Attachment API Tests', () {
    test('Create attachment', () async {
      // Кодируем данные файла в base64
      final fileDataBase64 = base64Encode(testFileData);
      
      final response = await http.post(
        Uri.parse('$baseUrl/order-attachments'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'orderId': testOrderId,
          'fileName': 'test_file.docx',
          'fileData': fileDataBase64,
        }),
      );
      
      expect(response.statusCode, equals(200));
      
      final data = jsonDecode(response.body);
      expect(data['success'], isTrue);
      expect(data['data'], isNotNull);
      expect(data['data']['id'], isNotNull);
      
      testAttachmentId = data['data']['id'];
      print('Created test attachment with ID: $testAttachmentId');
    });
    
    test('Get all attachments', () async {
      final response = await http.get(Uri.parse('$baseUrl/order-attachments'));
      
      expect(response.statusCode, equals(200));
      
      final data = jsonDecode(response.body);
      expect(data['success'], isTrue);
      expect(data['data'], isA<List>());
    });
    
    test('Get attachments by order ID', () async {
      final response = await http.get(Uri.parse('$baseUrl/order-attachments/order/$testOrderId'));
      
      expect(response.statusCode, equals(200));
      
      final data = jsonDecode(response.body);
      expect(data['success'], isTrue);
      expect(data['data'], isA<List>());
      expect(data['data'], isNotEmpty);
      
      final attachments = data['data'] as List;
      final attachment = attachments.firstWhere(
        (a) => a['id'] == testAttachmentId,
        orElse: () => null,
      );
      
      expect(attachment, isNotNull);
      expect(attachment['fileName'], equals('test_file.docx'));
      expect(attachment['orderId'], equals(testOrderId));
    });
    
    test('Get attachment by ID', () async {
      final response = await http.get(Uri.parse('$baseUrl/order-attachments/$testAttachmentId'));
      
      expect(response.statusCode, equals(200));
      
      final data = jsonDecode(response.body);
      expect(data['success'], isTrue);
      expect(data['data'], isNotNull);
      expect(data['data']['id'], equals(testAttachmentId));
      expect(data['data']['fileName'], equals('test_file.docx'));
      expect(data['data']['orderId'], equals(testOrderId));
    });
    
    test('Download attachment', () async {
      final response = await http.get(Uri.parse('$baseUrl/order-attachments/$testAttachmentId/download'));
      
      expect(response.statusCode, equals(200));
      expect(response.headers['content-type'], contains('application/vnd.openxmlformats-officedocument.wordprocessingml.document'));
      expect(response.headers['content-disposition'], contains('attachment'));
      expect(response.headers['content-disposition'], contains('test_file.docx'));
      
      // Проверяем, что содержимое файла совпадает с тестовыми данными
      expect(response.bodyBytes, equals(testFileData));
    });
    
    test('Update attachment', () async {
      final response = await http.put(
        Uri.parse('$baseUrl/order-attachments/$testAttachmentId'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'orderId': testOrderId,
          'fileName': 'updated_test_file.docx',
        }),
      );
      
      expect(response.statusCode, equals(200));
      
      final data = jsonDecode(response.body);
      expect(data['success'], isTrue);
      
      // Проверяем, что имя файла обновилось
      final getResponse = await http.get(Uri.parse('$baseUrl/order-attachments/$testAttachmentId'));
      final getData = jsonDecode(getResponse.body);
      
      expect(getData['data']['fileName'], equals('updated_test_file.docx'));
    });
    
    test('Delete attachment', () async {
      final response = await http.delete(Uri.parse('$baseUrl/order-attachments/$testAttachmentId'));
      
      expect(response.statusCode, equals(200));
      
      final data = jsonDecode(response.body);
      expect(data['success'], isTrue);
      
      // Проверяем, что вложение удалено
      final getResponse = await http.get(Uri.parse('$baseUrl/order-attachments/$testAttachmentId'));
      
      expect(getResponse.statusCode, equals(404));
    });
  });
}
