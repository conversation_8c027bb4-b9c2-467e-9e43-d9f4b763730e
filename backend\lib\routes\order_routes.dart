import 'package:shelf_router/shelf_router.dart';
import 'package:backend/controllers/order_controller.dart';

/// Создает маршрутизатор для работы с приказами
Router orderRoutes() {
  final controller = OrderController();
  final router = Router();

  // Получить все приказы
  router.get('/', controller.getAll);

  // Получить приказ по ID
  router.get('/<id>', controller.getById);

  // Создать новый приказ
  router.post('/', controller.create);

  // Обновить приказ
  router.put('/<id>', controller.update);

  // Удалить приказ
  router.delete('/<id>', controller.delete);

  return router;
}
