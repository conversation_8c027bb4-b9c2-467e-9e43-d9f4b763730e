# Fixing SQLite DLL Architecture Mismatch

This guide helps you fix the error:
```
Failed to load dynamic library 'sqlite3.dll': %1 is not a valid Win32 application. (error code: 193)
```

## Understanding the Issue

This error occurs when there's an architecture mismatch between your application and the SQLite DLL:
- Your application is 32-bit, but you're using a 64-bit SQLite DLL, or
- Your application is 64-bit, but you're using a 32-bit SQLite DLL

We've determined that the HR system executable (`hr_system.exe`) is a **32-bit application**, so you need a 32-bit SQLite DLL.

## Automatic Fix

Run the `download_sqlite_32bit.bat` script to automatically download and install the correct 32-bit SQLite DLL.

## Manual Fix

If the automatic script doesn't work, follow these steps:

### 1. Download the correct SQLite DLL

For a **32-bit application** (which is what you have):
1. Visit the SQLite download page: https://www.sqlite.org/download.html
2. Download the 32-bit DLL package: "sqlite-dll-win32-x86-3430200.zip" (or newer version)

For a 64-bit application (not applicable in your case):
1. Visit the SQLite download page: https://www.sqlite.org/download.html
2. Download the 64-bit DLL package: "sqlite-dll-win64-x64-3430200.zip" (or newer version)

### 2. Extract the DLL

1. Extract the downloaded zip file
2. Locate the `sqlite3.dll` file in the extracted contents

### 3. Replace the existing DLL

1. Navigate to your `dist` directory
2. Backup the existing `sqlite3.dll` if present (rename it to `sqlite3.dll.bak`)
3. Copy the new `sqlite3.dll` to the `dist` directory

### 4. Verify the fix

1. Run the HR system using `dist\start_hr_system.bat` or directly with `dist\hr_system.exe`
2. Check if the application starts without the architecture mismatch error
3. Verify that the database directory (`db`) is created
4. Confirm you can access the application at http://localhost:8080

## Troubleshooting

If you still encounter issues:

1. **Check for other DLL dependencies**: The application might depend on other DLLs that are missing or have architecture mismatches
2. **Verify file permissions**: Ensure the application has permission to read and execute the SQLite DLL
3. **Check for antivirus interference**: Some antivirus software might block the application from loading DLLs
4. **Run as administrator**: Try running the application with administrator privileges

## Prevention for Future Builds

To prevent this issue in future builds, make sure to:

1. Use the updated build script that includes the correct SQLite DLL
2. Match the SQLite DLL architecture with the application architecture
3. Test the production build before deployment
