# Инструкции по тестированию моделей и репозиториев

В этом документе описаны инструкции по тестированию моделей данных и репозиториев, созданных для HR-системы.

## Тестирование через скрипт Dart

Для тестирования моделей и репозиториев через скрипт Dart выполните следующие шаги:

1. Перейдите в директорию `backend`:

```bash
cd backend
```

2. Запустите тестовый скрипт:

```bash
dart bin/test_models.dart
```

Скрипт выполнит следующие операции:
- Инициализирует базу данных и применит миграции
- Создаст тестовые данные (сотрудник, должность, тип звания, воинское звание, приказ, контракт)
- Получит данные из базы данных и выведет их в консоль
- Обновит некоторые данные
- Удалит некоторые данные

Результаты каждой операции будут выведены в консоль.

## Тестирование через Flutter UI

Для тестирования взаимодействия с API через Flutter UI выполните следующие шаги:

1. Запустите бэкенд-сервер:

```bash
cd backend
dart bin/server.dart
```

2. В отдельном терминале запустите Flutter-приложение:

```bash
cd frontend
flutter run -d chrome
```

3. В приложении откройте страницу "Тестирование API" через боковую панель навигации.

4. На странице тестирования API вы можете:
   - Получить список сотрудников
   - Получить список должностей
   - Создать тестового сотрудника
   - Создать тестовую должность
   - Обновить тестового сотрудника
   - Удалить тестового сотрудника
   - Запустить полный тест, который выполнит все операции последовательно

Результаты каждой операции будут отображены на странице.

## Структура тестового скрипта

Тестовый скрипт `bin/test_models.dart` состоит из следующих функций:

- `main()` - основная функция, которая инициализирует базу данных и вызывает другие функции
- `createTestData()` - создает тестовые данные
- `retrieveData()` - получает данные из базы данных
- `updateData()` - обновляет данные
- `deleteData()` - удаляет данные

## Структура страницы тестирования API

Страница тестирования API `lib/pages/api_test_page.dart` содержит следующие методы:

- `_fetchEmployees()` - получает список сотрудников
- `_fetchPositions()` - получает список должностей
- `_createTestEmployee()` - создает тестового сотрудника
- `_createTestPosition()` - создает тестовую должность
- `_updateTestEmployee()` - обновляет тестового сотрудника
- `_deleteTestEmployee()` - удаляет тестового сотрудника
- `_runFullTest()` - запускает полный тест

## Возможные проблемы и их решение

1. **Ошибка "Target of URI hasn't been generated"**:
   - Запустите команду `dart run build_runner build --delete-conflicting-outputs` для генерации файлов `.g.dart`

2. **Ошибка "Database is locked"**:
   - Убедитесь, что у вас нет других процессов, которые используют базу данных
   - Перезапустите сервер

3. **Ошибка "Connection refused"**:
   - Убедитесь, что бэкенд-сервер запущен и работает на порту 8080
   - Проверьте настройки CORS в бэкенде

4. **Ошибка "No such file or directory"**:
   - Убедитесь, что вы находитесь в правильной директории
   - Проверьте пути к файлам

## Дополнительные тесты

Вы можете расширить тестовый скрипт или страницу тестирования API, добавив тесты для других моделей и репозиториев, таких как:

- `ServiceHistory` - послужной список
- `StateAward` - государственные награды
- `DepartmentalAward` - ведомственные награды
- `CombatService` - участие в боевых действиях
- `Family` - семья
- `EducationDetail` - образование
- `ForeignTravel` - зарубежные поездки
- `Attestation` - аттестации
- `PreliminaryCandidate` - предварительные кандидаты
- `Hierarchy` - иерархия
- `OrderAttachment` - вложения к приказам
