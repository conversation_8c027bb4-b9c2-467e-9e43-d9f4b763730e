import 'dart:convert';
import 'package:shelf/shelf.dart';
import 'package:backend/database/repositories/employee_repository.dart';
import 'package:backend/database/repositories/service_history_repository.dart';
import 'package:backend/database/repositories/position_repository.dart';
import 'package:backend/models/service_history.dart';
import 'package:backend/utils/validator.dart';
import 'package:logging/logging.dart';

/// Контроллер для специфических операций с сотрудниками
class EmployeeOperationsController {
  static final Logger _logger = Logger('EmployeeOperationsController');
  final EmployeeRepository _employeeRepository = EmployeeRepository();
  final ServiceHistoryRepository _serviceHistoryRepository =
      ServiceHistoryRepository();
  final PositionRepository _positionRepository = PositionRepository();

  /// Перемещение сотрудника на новую должность
  Future<Response> moveToPosition(Request request, String employeeId) async {
    _logger.info('Moving employee with ID $employeeId to a new position');

    try {
      final empId = int.parse(employeeId);
      final employee = _employeeRepository.getById(empId);

      if (employee == null) {
        return Response.notFound(
          jsonEncode({
            'success': false,
            'error': 'Not Found',
            'message': 'Employee with ID $employeeId not found',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final jsonBody = await request.readAsString();
      final Map<String, dynamic> data = jsonDecode(jsonBody);

      // Проверяем наличие необходимых полей
      final requiredFields = ['positionId', 'startDate'];
      if (!Validator.hasRequiredFields(data, requiredFields)) {
        return Response.badRequest(
          body: jsonEncode({
            'success': false,
            'error': 'Bad Request',
            'message': 'Required fields missing: positionId, startDate',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      // Валидация даты
      if (!Validator.isDate(data['startDate'] as String?)) {
        return Response.badRequest(
          body: jsonEncode({
            'success': false,
            'error': 'Validation Error',
            'message':
                'Invalid date format for startDate. Use YYYY-MM-DD format.',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final positionId = data['positionId'] as int;
      final position = _positionRepository.getById(positionId);

      if (position == null) {
        return Response.badRequest(
          body: jsonEncode({
            'success': false,
            'error': 'Bad Request',
            'message': 'Position with ID $positionId not found',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      // Проверяем, занята ли должность другим сотрудником
      final occupyingEmployee = _employeeRepository
          .getEmployeeByPositionId(positionId, excludeEmployeeId: empId);

      if (occupyingEmployee != null) {
        // Если должность занята другим сотрудником, возвращаем предупреждение
        return Response.ok(
          jsonEncode({
            'success': false,
            'warning': true,
            'data': {
              'occupyingEmployee': {
                'id': occupyingEmployee.id,
                'fullName':
                    '${occupyingEmployee.lastName} ${occupyingEmployee.firstName} ${occupyingEmployee.middleName ?? ''}',
              },
              'message': 'Должность уже занята другим сотрудником',
            },
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      // Получаем текущую активную запись в послужном списке
      final currentRecords = _serviceHistoryRepository
          .getByEmployeeId(empId)
          .where((record) => record.endDate == null)
          .toList();

      // Если есть активная запись, закрываем ее
      if (currentRecords.isNotEmpty) {
        final currentRecord = currentRecords.first;
        final updatedRecord = ServiceHistory(
          id: currentRecord.id,
          employeeId: currentRecord.employeeId,
          positionId: currentRecord.positionId,
          externalPositionTitle: currentRecord.externalPositionTitle,
          startDate: currentRecord.startDate,
          endDate: data[
              'startDate'], // Дата начала новой должности = дата окончания текущей
          acceptanceDate: currentRecord.acceptanceDate,
          handoverDate: data['handoverDate'], // Дата сдачи дел
          orderId: currentRecord.orderId,
          externalOrderInfo: currentRecord.externalOrderInfo,
          notes: currentRecord.notes,
        );

        _serviceHistoryRepository.update(updatedRecord);
      }

      // Создаем новую запись в послужном списке
      final newRecord = ServiceHistory(
        employeeId: empId,
        positionId: positionId,
        startDate: data['startDate'],
        acceptanceDate: data['acceptanceDate'],
        orderId: data['orderId'],
        notes: data['notes'],
      );

      final recordId = _serviceHistoryRepository.create(newRecord);

      // Обновляем прямую ссылку на должность в таблице сотрудников
      _employeeRepository.updatePosition(empId, positionId);

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': {
            'serviceHistoryId': recordId,
            'message': 'Employee successfully moved to new position',
          },
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error moving employee to new position: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Присвоение воинского звания сотруднику
  Future<Response> assignMilitaryRank(
      Request request, String employeeId) async {
    _logger.info('Assigning military rank to employee with ID $employeeId');

    try {
      final empId = int.parse(employeeId);
      final employee = _employeeRepository.getById(empId);

      if (employee == null) {
        return Response.notFound(
          jsonEncode({
            'success': false,
            'error': 'Not Found',
            'message': 'Employee with ID $employeeId not found',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      // Здесь будет логика присвоения звания
      // Это потребует создания дополнительных моделей и репозиториев

      return Response.ok(
        jsonEncode({
          'success': true,
          'message':
              'Military rank assignment functionality not implemented yet',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger
          .severe('Error assigning military rank to employee: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }
}
