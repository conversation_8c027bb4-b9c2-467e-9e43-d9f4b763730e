class Order {
  final int? id;
  final String number;
  final String date;
  final String issuedBy;
  final String? description;
  final String? createdAt;
  final String? updatedAt;

  Order({
    this.id,
    required this.number,
    required this.date,
    required this.issuedBy,
    this.description,
    this.createdAt,
    this.updatedAt,
  });

  factory Order.fromJson(Map<String, dynamic> json) {
    return Order(
      id: json['id'],
      number: json['number'],
      date: json['date'],
      issuedBy: json['issuedBy'],
      description: json['description'],
      createdAt: json['createdAt'],
      updatedAt: json['updatedAt'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'number': number,
      'date': date,
      'issuedBy': issuedBy,
      'description': description,
    };
  }

  Order copyWith({
    int? id,
    String? number,
    String? date,
    String? issuedBy,
    String? description,
    String? createdAt,
    String? updatedAt,
  }) {
    return Order(
      id: id ?? this.id,
      number: number ?? this.number,
      date: date ?? this.date,
      issuedBy: issuedBy ?? this.issuedBy,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
