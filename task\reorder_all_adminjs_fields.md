# Task: Оптимизация порядка полей в AdminJS для всех таблиц

**Goal:** Изменить порядок отображения полей в интерфейсе AdminJS для всех таблиц (кроме `employees`, которая уже настроена) на более логичный и удобный для пользователя.

**Sub-tasks:**

1.  **[X] `task_1_create_main_task_file.md`**: Create this task tracking file.
    *   Status: Completed
2.  **[X] `task_2_cleanup_resource_options_file.md`**: Удалить неиспользуемый импорт `Employee` из `ok_admin/adminjs-resource-options.js`.
    *   Status: Completed
3.  **[X] `task_3_reorder_rank_types.md`**: Определить и применить оптимальный порядок полей для таблицы `rank_types` в AdminJS.
    *   Status: Completed
4.  **[X] `task_4_reorder_military_ranks.md`**: Определить и применить оптимальный порядок полей для таблицы `military_ranks` в AdminJS.
    *   Status: Completed
5.  **[X] `task_5_reorder_positions.md`**: Определить и применить оптимальный порядок полей для таблицы `positions` в AdminJS.
    *   Status: Completed
6.  **[X] `task_6_reorder_preliminary_candidates.md`**: Определить и применить оптимальный порядок полей для таблицы `preliminary_candidates` в AdminJS.
    *   Status: Completed
7.  **[X] `task_7_reorder_hierarchy.md`**: Определить и применить оптимальный порядок полей для таблицы `hierarchy` в AdminJS.
    *   Status: Completed
8.  **[X] `task_8_reorder_orders.md`**: Определить и применить оптимальный порядок полей для таблицы `orders` в AdminJS.
    *   Status: Completed
9.  **[X] `task_9_reorder_order_attachments.md`**: Определить и применить оптимальный порядок полей для таблицы `order_attachments` в AdminJS.
    *   Status: Completed
10. **[X] `task_10_reorder_contracts.md`**: Определить и применить оптимальный порядок полей для таблицы `contracts` в AdminJS.
    *   Status: Completed
11. **[X] `task_11_reorder_attestations.md`**: Определить и применить оптимальный порядок полей для таблицы `attestations` в AdminJS.
    *   Status: Completed
12. **[X] `task_12_reorder_education_details.md`**: Определить и применить оптимальный порядок полей для таблицы `education_details` в AdminJS.
    *   Status: Completed
13. **[X] `task_13_reorder_foreign_travel.md`**: Определить и применить оптимальный порядок полей для таблицы `foreign_travel` в AdminJS.
    *   Status: Completed
14. **[X] `task_14_reorder_state_awards.md`**: Определить и применить оптимальный порядок полей для таблицы `state_awards` в AdminJS.
    *   Status: Completed
15. **[X] `task_15_reorder_departmental_awards.md`**: Определить и применить оптимальный порядок полей для таблицы `departmental_awards` в AdminJS.
    *   Status: Completed
16. **[X] `task_16_reorder_combat_service.md`**: Определить и применить оптимальный порядок полей для таблицы `combat_service` в AdminJS.
    *   Status: Completed
17. **[X] `task_17_reorder_family.md`**: Определить и применить оптимальный порядок полей для таблицы `family` в AdminJS.
    *   Status: Completed
18. **[X] `task_18_reorder_service_history.md`**: Определить и применить оптимальный порядок полей для таблицы `service_history` в AdminJS.
    *   Status: Completed
19. **[X] `task_19_apply_all_options.md`**: Применить все сгенерированные опции к файлу `ok_admin/adminjs-resource-options.js`.
    *   Status: Completed


**Desired Outcome:**
*   Для каждой таблицы в AdminJS (кроме `employees`) поля отображаются в логическом порядке.
*   Списки (`listProperties`), формы редактирования (`editProperties`), страницы просмотра (`showProperties`) и фильтры (`filterProperties`) настроены для каждой таблицы.
*   Файл `ok_admin/adminjs-resource-options.js` содержит все эти конфигурации. 