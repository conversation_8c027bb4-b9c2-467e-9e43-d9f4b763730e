// ignore_for_file: use_build_context_synchronously
import 'package:flutter/material.dart';
import 'package:frontend/models/employee.dart';
import 'package:frontend/models/position.dart';
import 'package:frontend/models/service_history.dart';
import 'package:frontend/providers/employee_provider.dart';
import 'package:frontend/providers/position_provider.dart';
import 'package:frontend/providers/service_history_provider.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

class EmployeeDetailsPage extends StatefulWidget {
  final int employeeId;

  const EmployeeDetailsPage({super.key, required this.employeeId});

  @override
  State<EmployeeDetailsPage> createState() => _EmployeeDetailsPageState();
}

class _EmployeeDetailsPageState extends State<EmployeeDetailsPage> {
  Employee? _employee;
  bool _isLoading = true;
  String? _error;
  ServiceHistory? _currentServiceRecord;
  Position? _currentPosition;

  @override
  void initState() {
    super.initState();
    _loadEmployee();
  }

  Future<void> _loadEmployee() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final employee =
          await Provider.of<EmployeeProvider>(context, listen: false)
              .getEmployee(widget.employeeId);

      setState(() {
        _employee = employee;
        _isLoading = false;
      });

      // После загрузки сотрудника загружаем его текущую должность
      await _loadCurrentPosition();
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _loadCurrentPosition() async {
    try {
      // Загружаем послужной список сотрудника
      final serviceHistoryProvider =
          Provider.of<ServiceHistoryProvider>(context, listen: false);
      await serviceHistoryProvider.fetchByEmployeeId(widget.employeeId);

      // Ищем активную запись (без даты окончания)
      final activeRecords = serviceHistoryProvider.records
          .where(
              (record) => record.endDate == null && record.positionId != null)
          .toList();

      if (activeRecords.isNotEmpty) {
        // Берем первую активную запись
        final currentRecord = activeRecords.first;

        // Загружаем информацию о должности
        if (currentRecord.positionId != null) {
          final position =
              await Provider.of<PositionProvider>(context, listen: false)
                  .getPosition(currentRecord.positionId!);

          setState(() {
            _currentServiceRecord = currentRecord;
            _currentPosition = position;
          });
        }
      }
    } catch (e) {
      // Ошибку при загрузке должности не показываем пользователю,
      // просто логируем или игнорируем
      debugPrint('Ошибка при загрузке должности: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            // Используем context.go() вместо context.pop() для надежной навигации
            context.go('/employees');
          },
        ),
        title:
            Text(_employee != null ? _employee!.fullName : 'Детали сотрудника'),
        actions: [
          if (_employee != null)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () {
                context.go('/employees/${_employee!.id}/edit');
              },
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(
                  child: Text(
                    'Ошибка: $_error',
                    style: const TextStyle(color: Colors.red),
                  ),
                )
              : _employee == null
                  ? const Center(
                      child: Text('Сотрудник не найден'),
                    )
                  : SingleChildScrollView(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Card(
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'Основная информация',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const Divider(),
                                  _buildInfoRow('ФИО', _employee!.fullName),
                                  _buildInfoRow('Пол', _employee!.genderText),
                                  if (_employee!.birthDate != null)
                                    _buildInfoRow('Дата рождения',
                                        _formatDate(_employee!.birthDate!)),
                                  if (_employee!.placeOfBirth != null)
                                    _buildInfoRow('Место рождения',
                                        _employee!.placeOfBirth!),
                                  if (_employee!.nationality != null)
                                    _buildInfoRow('Национальность',
                                        _employee!.nationality!),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),
                          Card(
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'Служебная информация',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const Divider(),
                                  if (_currentPosition != null)
                                    _buildInfoRow('Должность',
                                        _currentPosition!.fullTitle),
                                  if (_currentServiceRecord != null)
                                    _buildInfoRow(
                                        'Дата назначения',
                                        _formatDate(
                                            _currentServiceRecord!.startDate)),
                                  if (_employee!.personalNumber != null)
                                    _buildInfoRow('Личный номер',
                                        _employee!.personalNumber!),
                                  if (_employee!.academicDegree != null)
                                    _buildInfoRow('Ученая степень',
                                        _employee!.academicDegree!),
                                  if (_employee!.veteranSince != null)
                                    _buildInfoRow('Ветеран с',
                                        _employee!.veteranSince.toString()),
                                  if (_employee!.childrenUnder16 != null)
                                    _buildInfoRow('Дети до 16 лет',
                                        _employee!.childrenUnder16.toString()),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 150,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  String _formatDate(String date) {
    try {
      final dateTime = DateTime.parse(date);
      return DateFormat('dd.MM.yyyy').format(dateTime);
    } catch (e) {
      return date;
    }
  }
}
