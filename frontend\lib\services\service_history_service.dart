import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:frontend/models/service_history.dart';

/// Сервис для работы с API послужного списка
class ServiceHistoryService {
  static const String baseUrl = 'http://localhost:8080/api/service-history';
  static const String employeesUrl = 'http://localhost:8080/api/employees';

  /// Получить все записи послужного списка
  Future<List<ServiceHistory>> getAll() async {
    final response = await http.get(Uri.parse(baseUrl));

    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      if (data['success'] == true && data['data'] != null) {
        return (data['data'] as List)
            .map((item) => ServiceHistory.fromJson(item))
            .toList();
      }
      return [];
    } else {
      throw Exception('Failed to load service history records');
    }
  }

  /// Получить запись послужного списка по ID
  Future<ServiceHistory?> getById(int id) async {
    final response = await http.get(Uri.parse('$baseUrl/$id'));

    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      if (data['success'] == true && data['data'] != null) {
        return ServiceHistory.fromJson(data['data']);
      }
      return null;
    } else if (response.statusCode == 404) {
      return null;
    } else {
      throw Exception('Failed to load service history record');
    }
  }

  /// Получить записи послужного списка сотрудника
  Future<List<ServiceHistory>> getByEmployeeId(int employeeId) async {
    final response = await http.get(Uri.parse('$baseUrl/employee/$employeeId'));

    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      if (data['success'] == true && data['data'] != null) {
        return (data['data'] as List)
            .map((item) => ServiceHistory.fromJson(item))
            .toList();
      }
      return [];
    } else {
      throw Exception('Failed to load service history records for employee');
    }
  }

  /// Создать новую запись послужного списка
  Future<int> create(ServiceHistory record) async {
    final response = await http.post(
      Uri.parse(baseUrl),
      headers: {'Content-Type': 'application/json'},
      body: json.encode(record.toJson()),
    );

    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      if (data['success'] == true && data['data'] != null) {
        return data['data']['id'];
      }
      throw Exception(
          'Failed to create service history record: No ID returned');
    } else {
      throw Exception(
          'Failed to create service history record: ${response.body}');
    }
  }

  /// Обновить запись послужного списка
  Future<bool> update(ServiceHistory record) async {
    if (record.id == null) {
      throw ArgumentError('Service history record ID cannot be null');
    }

    final response = await http.put(
      Uri.parse('$baseUrl/${record.id}'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode(record.toJson()),
    );

    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      return data['success'] == true;
    } else {
      throw Exception(
          'Failed to update service history record: ${response.body}');
    }
  }

  /// Удалить запись послужного списка
  Future<bool> delete(int id) async {
    final response = await http.delete(Uri.parse('$baseUrl/$id'));

    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      return data['success'] == true;
    } else {
      throw Exception(
          'Failed to delete service history record: ${response.body}');
    }
  }

  /// Переместить сотрудника на новую должность
  Future<Map<String, dynamic>> moveEmployeeToPosition(
      int employeeId, ServiceHistory serviceHistory) async {
    final response = await http.post(
      Uri.parse('$employeesUrl/$employeeId/move'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode(serviceHistory.toJson()),
    );

    final Map<String, dynamic> data = json.decode(response.body);

    if (response.statusCode == 200) {
      if (data['success'] == true) {
        return data['data'];
      } else if (data['warning'] == true) {
        // Должность занята другим сотрудником
        throw Exception(data['data']['message']);
      }
    }

    throw Exception(data['message'] ?? 'Failed to move employee to position');
  }

  /// Проверить, занята ли должность активной записью
  Future<bool> isPositionOccupied(int positionId) async {
    final response =
        await http.get(Uri.parse('$baseUrl/active/position/$positionId'));
    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      if (data['success'] == true && data['data'] != null) {
        return (data['data'] as List).isNotEmpty;
      }
      return false;
    } else {
      throw Exception('Failed to check position occupancy: ${response.body}');
    }
  }
}
