import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:frontend/models/position.dart';
import 'package:frontend/providers/position_provider.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

class PositionFormPage extends StatefulWidget {
  final int? positionId;

  const PositionFormPage({super.key, this.positionId});

  @override
  State<PositionFormPage> createState() => _PositionFormPageState();
}

class _PositionFormPageState extends State<PositionFormPage> {
  final _formKey = GlobalKey<FormBuilderState>();
  bool _isLoading = false;
  bool _isEdit = false;
  Position? _position;

  @override
  void initState() {
    super.initState();
    _isEdit = widget.positionId != null;
    if (_isEdit) {
      _loadPosition();
    }
  }

  Future<void> _loadPosition() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final position = await Provider.of<PositionProvider>(context, listen: false)
          .getPosition(widget.positionId!);

      setState(() {
        _position = position;
        _isLoading = false;
      });

      // Примечание: мы больше не используем patchValue здесь,
      // так как данные будут установлены через initialValue в FormBuilder
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Ошибка при загрузке данных: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _savePosition() async {
    if (_formKey.currentState?.saveAndValidate() ?? false) {
      setState(() {
        _isLoading = true;
      });

      final formData = _formKey.currentState!.value;

      final position = Position(
        id: _isEdit ? widget.positionId : null,
        title: formData['title'],
        department: formData['department'],
        unitName: formData['unitName'],
        womenAllowed: formData['womenAllowed'],
        vus: formData['vus'],
        vusPss: formData['vusPss'],
        positionCodePss: formData['positionCodePss'],
        tariffCategory: formData['tariffCategory'],
        isFlightCrew: formData['isFlightCrew'],
        antiCorruption: formData['antiCorruption'],
      );

      try {
        bool success;
        if (_isEdit) {
          success = await Provider.of<PositionProvider>(context, listen: false)
              .updatePosition(position);
        } else {
          success = await Provider.of<PositionProvider>(context, listen: false)
              .createPosition(position);
        }

        setState(() {
          _isLoading = false;
        });

        if (success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Должность успешно ${_isEdit ? 'обновлена' : 'создана'}'),
            ),
          );
          context.go('/positions');
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Ошибка при ${_isEdit ? 'обновлении' : 'создании'} должности: ${Provider.of<PositionProvider>(context, listen: false).error}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        setState(() {
          _isLoading = false;
        });
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Ошибка: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEdit ? 'Редактирование должности' : 'Новая должность'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/positions'),
        ),
      ),
      body: _isLoading && _isEdit
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: FormBuilder(
                key: _formKey,
                initialValue: _isEdit && _position != null ? {
                  'title': _position!.title,
                  'department': _position!.department ?? '',
                  'unitName': _position!.unitName ?? '',
                  'womenAllowed': _position!.womenAllowed ?? 1,
                  'vus': _position!.vus ?? '',
                  'vusPss': _position!.vusPss ?? '',
                  'positionCodePss': _position!.positionCodePss ?? '',
                  'tariffCategory': _position!.tariffCategory ?? '',
                  'isFlightCrew': _position!.isFlightCrew ?? 0,
                  'antiCorruption': _position!.antiCorruption ?? 0,
                } : {},
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Основная информация',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    FormBuilderTextField(
                      name: 'title',
                      decoration: const InputDecoration(
                        labelText: 'Наименование должности',
                        border: OutlineInputBorder(),
                      ),
                      validator: FormBuilderValidators.compose([
                        FormBuilderValidators.required(
                            errorText: 'Поле обязательно для заполнения'),
                      ]),
                    ),
                    const SizedBox(height: 16),
                    FormBuilderTextField(
                      name: 'department',
                      decoration: const InputDecoration(
                        labelText: 'Подразделение',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    FormBuilderTextField(
                      name: 'unitName',
                      decoration: const InputDecoration(
                        labelText: 'Условное наименование части',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    FormBuilderDropdown<int>(
                      name: 'womenAllowed',
                      decoration: const InputDecoration(
                        labelText: 'Комплектуется женщинами',
                        border: OutlineInputBorder(),
                      ),
                      initialValue: 1,
                      items: const [
                        DropdownMenuItem(
                          value: 1,
                          child: Text('Да'),
                        ),
                        DropdownMenuItem(
                          value: 0,
                          child: Text('Нет'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),
                    const Text(
                      'Дополнительная информация',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    FormBuilderTextField(
                      name: 'vus',
                      decoration: const InputDecoration(
                        labelText: 'ВУС',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    FormBuilderTextField(
                      name: 'vusPss',
                      decoration: const InputDecoration(
                        labelText: 'ВУС ПСС',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    FormBuilderTextField(
                      name: 'positionCodePss',
                      decoration: const InputDecoration(
                        labelText: 'Код ПСС',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    FormBuilderTextField(
                      name: 'tariffCategory',
                      decoration: const InputDecoration(
                        labelText: 'Тарифная категория',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    FormBuilderDropdown<int>(
                      name: 'isFlightCrew',
                      decoration: const InputDecoration(
                        labelText: 'Летный состав',
                        border: OutlineInputBorder(),
                      ),
                      initialValue: 0,
                      items: const [
                        DropdownMenuItem(
                          value: 1,
                          child: Text('Да'),
                        ),
                        DropdownMenuItem(
                          value: 0,
                          child: Text('Нет'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    FormBuilderDropdown<int>(
                      name: 'antiCorruption',
                      decoration: const InputDecoration(
                        labelText: 'Антикоррупционная',
                        border: OutlineInputBorder(),
                      ),
                      initialValue: 0,
                      items: const [
                        DropdownMenuItem(
                          value: 1,
                          child: Text('Да'),
                        ),
                        DropdownMenuItem(
                          value: 0,
                          child: Text('Нет'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _savePosition,
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Text(
                            _isLoading
                                ? 'Сохранение...'
                                : _isEdit
                                    ? 'Сохранить изменения'
                                    : 'Создать должность',
                            style: const TextStyle(fontSize: 16),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
