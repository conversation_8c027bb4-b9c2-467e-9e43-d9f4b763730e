import 'dart:convert';
import 'package:shelf/shelf.dart';
import 'package:backend/database/repositories/rank_type_repository.dart';
import 'package:backend/models/rank_type.dart';
import 'package:logging/logging.dart';

class RankTypeController {
  static final Logger _logger = Logger('RankTypeController');
  final RankTypeRepository _repository = RankTypeRepository();

  /// Получить все типы званий
  Response getAll(Request request) {
    _logger.info('Getting all rank types');

    try {
      final rankTypes = _repository.getAll();

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': rankTypes.map((e) => e.toJson()).toList(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error getting all rank types: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Получить тип звания по ID
  Response getById(Request request, String id) {
    _logger.info('Getting rank type by ID: $id');

    try {
      final rankTypeId = int.parse(id);
      final rankType = _repository.getById(rankTypeId);

      if (rankType == null) {
        return Response.notFound(
          jsonEncode({
            'success': false,
            'error': 'Not Found',
            'message': 'Rank type with ID $id not found',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': rankType.toJson(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error getting rank type by ID: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Создать новый тип звания
  Future<Response> create(Request request) async {
    _logger.info('Creating new rank type');

    try {
      final jsonBody = await request.readAsString();
      final Map<String, dynamic> data = jsonDecode(jsonBody);

      final rankType = RankType(
        name: data['name'],
        serviceYearsRequired: data['serviceYearsRequired'],
        category: data['category'],
      );

      final id = _repository.create(rankType);

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': {'id': id},
          'message': 'Rank type created successfully',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error creating rank type: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Обновить тип звания
  Future<Response> update(Request request, String id) async {
    _logger.info('Updating rank type with ID: $id');

    try {
      final rankTypeId = int.parse(id);
      final existingRankType = _repository.getById(rankTypeId);

      if (existingRankType == null) {
        return Response.notFound(
          jsonEncode({
            'success': false,
            'error': 'Not Found',
            'message': 'Rank type with ID $id not found',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final jsonBody = await request.readAsString();
      final Map<String, dynamic> data = jsonDecode(jsonBody);

      final rankType = RankType(
        id: rankTypeId,
        name: data['name'] ?? existingRankType.name,
        serviceYearsRequired: data['serviceYearsRequired'] ?? existingRankType.serviceYearsRequired,
        category: data['category'] ?? existingRankType.category,
      );

      final success = _repository.update(rankType);

      if (!success) {
        return Response.internalServerError(
          body: jsonEncode({
            'success': false,
            'error': 'Internal Server Error',
            'message': 'Failed to update rank type',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'Rank type updated successfully',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error updating rank type: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Удалить тип звания
  Response delete(Request request, String id) {
    _logger.info('Deleting rank type with ID: $id');

    try {
      final rankTypeId = int.parse(id);
      final existingRankType = _repository.getById(rankTypeId);

      if (existingRankType == null) {
        return Response.notFound(
          jsonEncode({
            'success': false,
            'error': 'Not Found',
            'message': 'Rank type with ID $id not found',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final success = _repository.delete(rankTypeId);

      if (!success) {
        return Response.internalServerError(
          body: jsonEncode({
            'success': false,
            'error': 'Internal Server Error',
            'message': 'Failed to delete rank type',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'Rank type deleted successfully',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error deleting rank type: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }
}
