import 'dart:io';

import 'package:shelf/shelf.dart';
import 'package:shelf/shelf_io.dart';
import 'package:shelf_static/shelf_static.dart';
import 'package:path/path.dart' as path;

import 'package:backend/database/database.dart';
import 'package:backend/middleware/cors_middleware.dart';
import 'package:backend/middleware/error_middleware.dart';
import 'package:backend/middleware/logging_middleware.dart';
import 'package:backend/routes/router.dart';
import 'package:backend/utils/logger.dart';

void main(List<String> args) async {
  try {
    print('Starting server initialization...');

    // Инициализируем логгер
    print('Initializing logger...');
    initLogger();
    print('Logger initialized successfully');

    // Проверяем наличие директорий
    print('Checking directories...');

    // Получаем путь к исполняемому файлу
    final executablePath = Platform.script.toFilePath();
    print('Executable path: $executablePath');

    // Получаем директорию, в которой находится исполняемый файл
    final executableDir = path.dirname(executablePath);
    print('Executable directory: $executableDir');

    // В production-сборке используем директорию исполняемого файла как базовую
    // В режиме разработки используем директорию на уровень выше (корень проекта)
    final bool isProductionBuild =
        executablePath.toLowerCase().endsWith('.exe');
    final baseDir =
        isProductionBuild ? executableDir : path.dirname(executableDir);
    print('Base directory: $baseDir');

    // Создаем пути к директориям относительно базовой директории
    final dbDirPath = path.join(baseDir, 'db');
    final migrationsDirPath = path.join(baseDir, 'migrations');

    print('DB directory path: $dbDirPath');
    print('Migrations directory path: $migrationsDirPath');

    // Проверяем и создаем директорию для базы данных
    final dbDir = Directory(dbDirPath);
    if (!dbDir.existsSync()) {
      print('Creating db directory...');
      dbDir.createSync(recursive: true);
      print('db directory created successfully');
    } else {
      print('db directory already exists');
    }

    // Проверяем и создаем директорию для миграций
    final migrationsDir = Directory(migrationsDirPath);
    if (!migrationsDir.existsSync()) {
      print('Creating migrations directory...');
      migrationsDir.createSync(recursive: true);
      print('migrations directory created successfully');
    } else {
      print('migrations directory already exists');
      print('Migrations directory contents:');
      for (var entity in migrationsDir.listSync()) {
        print('  ${entity.path}');
      }
    }

    // Инициализируем базу данных
    print('Initializing database...');
    await AppDatabase.initialize(customDbDir: dbDirPath);
    print('Database initialized successfully');

    // Применяем миграции
    print('Applying migrations...');
    await AppDatabase.applyMigrations(customMigrationsDir: migrationsDirPath);
    print('Migrations applied successfully');

    // Создаем маршрутизатор
    print('Creating router...');
    final router = createRouter();
    print('Router created successfully');

    // Создаем обработчик статических файлов
    // Проверяем наличие директории web
    print('Setting up static file handler...');
    final webDirPath = path.join(baseDir, 'web');
    print('Web directory path: $webDirPath');

    final webDir = Directory(webDirPath);
    if (webDir.existsSync()) {
      print('Web directory exists, contents:');
      for (var entity in webDir.listSync()) {
        print('  ${entity.path}');
      }
    } else {
      print('Web directory does not exist!');
    }

    // Создаем обработчик статических файлов с поддержкой SPA
    Handler staticHandler;
    if (webDir.existsSync()) {
      // Создаем обработчик для статических файлов
      final fileHandler = createStaticHandler(webDirPath);

      // Создаем обработчик для index.html (для SPA)
      final defaultHandler =
          createStaticHandler(webDirPath, defaultDocument: 'index.html');

      // Комбинируем обработчики: сначала пробуем найти статический файл,
      // если не найден - возвращаем index.html для поддержки маршрутизации SPA
      staticHandler = (Request request) async {
        // Проверяем, является ли запрос запросом на статический файл
        final path = request.url.path;
        final hasExtension = path.contains('.');

        // Если путь не содержит расширения (например, /employees, /positions),
        // то это, скорее всего, маршрут SPA, и мы сразу возвращаем index.html
        if (!hasExtension && path.isNotEmpty) {
          print('SPA route detected, serving index.html for path: $path');
          // Создаем новый запрос с путем /index.html
          final indexRequest = Request(
            request.method,
            request.requestedUri.replace(path: '/index.html'),
            headers: request.headers,
            body: request.read(),
            context: request.context,
            encoding: request.encoding,
          );
          return fileHandler(indexRequest);
        }

        // Иначе пробуем найти статический файл
        final response = await fileHandler(request);
        if (response.statusCode == 404) {
          print('File not found, serving index.html for SPA routing');
          // Создаем новый запрос с путем /index.html
          final indexRequest = Request(
            request.method,
            request.requestedUri.replace(path: '/index.html'),
            headers: request.headers,
            body: request.read(),
            context: request.context,
            encoding: request.encoding,
          );
          return fileHandler(indexRequest);
        }
        return response;
      };
    } else {
      staticHandler =
          (Request request) => Response.notFound('Static files not available');
    }
    print('Static file handler set up successfully');

    // Используем любой доступный хост или IP контейнера (обычно `0.0.0.0`).
    final ip = InternetAddress.anyIPv4;
    print('Using IP: ${ip.address}');

    // Настраиваем конвейер с middleware
    print('Setting up request pipeline...');
    final handler = Pipeline()
        .addMiddleware(corsMiddleware())
        .addMiddleware(loggingMiddleware())
        .addMiddleware(errorMiddleware())
        .addHandler((request) {
      // Сначала пробуем обработать запрос как API-запрос
      final path = request.url.path;
      print('Handling request for path: $path');
      if (path == 'health' || path.startsWith('api/')) {
        print('Routing to API handler');
        return router.call(request);
      }
      // Если это не API-запрос, обрабатываем как запрос статического файла
      print('Routing to static file handler');
      return staticHandler(request);
    });
    print('Request pipeline set up successfully');

    // Для запуска в контейнерах учитываем переменную окружения PORT
    final port = int.parse(Platform.environment['PORT'] ?? '8080');
    print('Starting server on port $port...');
    final server = await serve(handler, ip, port);

    print('Server listening on port ${server.port}');
    print('Server is ready to accept connections');

    // Обработка сигнала завершения для корректного закрытия соединений
    print('Setting up signal handlers...');
    ProcessSignal.sigint.watch().listen((_) async {
      print('Shutting down server...');
      await server.close();
      AppDatabase.close();
      print('Server stopped');
      exit(0);
    });
    print('Signal handlers set up successfully');
  } catch (e, stackTrace) {
    print('ERROR: Server initialization failed: $e');
    print('Stack trace: $stackTrace');
    exit(1);
  }
}
