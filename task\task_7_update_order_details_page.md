# Подзадача: Обновление страницы деталей приказа для работы с вложениями

## Описание
Необходимо обновить страницу деталей приказа, чтобы добавить возможность загрузки, просмотра и скачивания вложений к приказу.

## Текущее состояние
- Провайдер для управления состоянием вложений к приказам создан
- Страница деталей приказа существует, но не имеет функциональности для работы с вложениями

## Шаги выполнения
1. Обновить файл `frontend/lib/pages/order/order_details_page.dart`:
   - Добавить секцию для отображения списка вложений к приказу
   - Добавить кнопку для загрузки нового вложения
   - Добавить функциональность для скачивания вложений
   - Добавить функциональность для удаления вложений
2. Создать виджет для выбора и загрузки файла:
   - Создать файл `frontend/lib/components/file_upload_dialog.dart`
   - Реализовать диалог для выбора файла и его загрузки
3. Обновить стили и макет страницы для корректного отображения вложений

## Особенности реализации
- Для выбора файла использовать пакет `file_picker`
- Для отображения списка вложений использовать `ListView` или `DataTable`
- Для скачивания файлов использовать функцию `downloadAttachment` из провайдера

## Ожидаемый результат
Обновленная страница деталей приказа, которая позволяет:
- Просматривать список вложений к приказу
- Загружать новые вложения
- Скачивать существующие вложения
- Удалять вложения
