# Задача: AdminJS + SQLite для локальной БД (Win7, оффлайн)

## Что нужно сделать
- Настроить Node.js сервер с использованием SQLite (создание БД при отсутствии)
- Интегрировать AdminJS для управления БД через web-интерфейс
- Подключить все таблицы из схемы (17 штук)
- Настроить удобные формы для работы с БД (например, перемещение сотрудников по должностям, назначение наград и т.п.)
- Запускать AdminJS на порту 8089
- Обеспечить возможность подключения к интерфейсу по локалке
- Всё должно работать на старых ПК с Windows 7, без интернета

## Итоговый желаемый результат
- При запуске сервера создаётся файл БД, если его нет
- AdminJS доступен по адресу http://localhost:8089
- Можно подключаться к этому адресу с других ПК в локальной сети
- Через AdminJS можно просматривать и редактировать все данные в БД, включая сложные формы (например, перемещение сотрудников)
- Решение не требует сложной установки и работает на win7, полностью оффлайн

## Мини-таски
- task 1: Установить и настроить AdminJS + Express + SQLite
- task 2: Подключить все таблицы к AdminJS
- task 3: Настроить формы и связи (например, перемещение сотрудников)
- task 4: Проверить доступ по локалке
- task 5: Обновить README/инструкцию по запуску
- task 6: Проверить запуск на Win7 (по чек-листу)

## Статус
В процессе 