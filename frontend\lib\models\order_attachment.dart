/// Модель вложения к приказу
class OrderAttachment {
  final int? id;
  final int orderId;
  final String fileName;
  final List<int>? data; // Данные файла (используются только при загрузке/скачивании)
  final String? uploadedAt;
  final String? createdAt;
  final String? updatedAt;

  OrderAttachment({
    this.id,
    required this.orderId,
    required this.fileName,
    this.data,
    this.uploadedAt,
    this.createdAt,
    this.updatedAt,
  });

  factory OrderAttachment.fromJson(Map<String, dynamic> json) {
    return OrderAttachment(
      id: json['id'],
      orderId: json['orderId'],
      fileName: json['fileName'],
      data: json['data'] != null ? List<int>.from(json['data']) : null,
      uploadedAt: json['uploadedAt'],
      createdAt: json['createdAt'],
      updatedAt: json['updatedAt'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'orderId': orderId,
      'fileName': fileName,
    };

    if (id != null) data['id'] = id;
    if (this.data != null) data['data'] = this.data;
    if (uploadedAt != null) data['uploadedAt'] = uploadedAt;
    if (createdAt != null) data['createdAt'] = createdAt;
    if (updatedAt != null) data['updatedAt'] = updatedAt;

    return data;
  }
}
