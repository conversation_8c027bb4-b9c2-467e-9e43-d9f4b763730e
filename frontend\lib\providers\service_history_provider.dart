import 'package:flutter/foundation.dart';
import 'package:frontend/models/service_history.dart';
import 'package:frontend/services/service_history_service.dart';

/// Провайдер для управления состоянием послужного списка
class ServiceHistoryProvider with ChangeNotifier {
  final ServiceHistoryService _service = ServiceHistoryService();
  
  List<ServiceHistory> _records = [];
  bool _isLoading = false;
  String? _error;
  
  List<ServiceHistory> get records => _records;
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  /// Получить все записи послужного списка
  Future<void> fetchAll() async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      _records = await _service.getAll();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }
  
  /// Получить записи послужного списка сотрудника
  Future<void> fetchByEmployeeId(int employeeId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      _records = await _service.getByEmployeeId(employeeId);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }
  
  /// Получить запись послужного списка по ID
  Future<ServiceHistory?> getById(int id) async {
    try {
      return await _service.getById(id);
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }
  
  /// Создать новую запись послужного списка
  Future<int> create(ServiceHistory record) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      final id = await _service.create(record);
      
      // Обновляем список записей
      await fetchByEmployeeId(record.employeeId);
          
      return id;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      rethrow;
    }
  }
  
  /// Обновить запись послужного списка
  Future<bool> update(ServiceHistory record) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      final success = await _service.update(record);
      
      // Обновляем список записей
      if (success) {
        await fetchByEmployeeId(record.employeeId);
            }
      
      return success;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      rethrow;
    }
  }
  
  /// Удалить запись послужного списка
  Future<bool> delete(int id, int employeeId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      final success = await _service.delete(id);
      
      // Обновляем список записей
      if (success) {
        await fetchByEmployeeId(employeeId);
      }
      
      return success;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      rethrow;
    }
  }
}
