import 'package:json_annotation/json_annotation.dart';

part 'education_detail.g.dart';

@JsonSerializable()
class EducationDetail {
  final int? id;
  final int employeeId;
  final int? educationType;
  final String institution;
  final String? specialty;
  final String? graduationDate;
  final String? createdAt;
  final String? updatedAt;

  EducationDetail({
    this.id,
    required this.employeeId,
    this.educationType,
    required this.institution,
    this.specialty,
    this.graduationDate,
    this.createdAt,
    this.updatedAt,
  });

  factory EducationDetail.fromJson(Map<String, dynamic> json) {
    // Преобразование строкового значения educationType в int
    var educationTypeValue = json['educationType'];
    int? educationType;

    if (educationTypeValue != null) {
      if (educationTypeValue is String) {
        educationType = int.tryParse(educationTypeValue);
      } else if (educationTypeValue is int) {
        educationType = educationTypeValue;
      }
    }

    return EducationDetail(
      id: json['id'] != null ? (json['id'] as num).toInt() : null,
      employeeId: (json['employeeId'] as num).toInt(),
      educationType: educationType,
      institution: json['institution'] as String,
      specialty: json['specialty'] as String?,
      graduationDate: json['graduationDate'] as String?,
      createdAt: json['createdAt'] as String?,
      updatedAt: json['updatedAt'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'employeeId': employeeId,
      'educationType': educationType,
      'institution': institution,
      'specialty': specialty,
      'graduationDate': graduationDate,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  /// Возвращает текстовое представление типа образования
  String get educationTypeText {
    switch (educationType) {
      case 0:
        return 'Не указано';
      case 1:
        return 'Основное общее образование';
      case 2:
        return 'Среднее общее образование';
      case 3:
        return 'Среднее профессиональное образование';
      case 4:
        return 'Высшее образование';
      default:
        return 'Не указано';
    }
  }
}
