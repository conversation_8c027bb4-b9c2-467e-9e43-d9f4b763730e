import 'package:shelf_router/shelf_router.dart';
import 'package:backend/controllers/service_history_controller.dart';

/// Создает маршрутизатор для работы с послужным списком
Router serviceHistoryRoutes() {
  final controller = ServiceHistoryController();
  final router = Router();

  // Получить все записи послужного списка
  router.get('/', controller.getAll);

  // Получить запись послужного списка по ID
  router.get('/<id>', controller.getById);

  // Получить записи послужного списка сотрудника
  router.get('/employee/<employeeId>', controller.getByEmployeeId);

  // Создать новую запись послужного списка
  router.post('/', controller.create);

  // Обновить запись послужного списка
  router.put('/<id>', controller.update);

  // Удалить запись послужного списка
  router.delete('/<id>', controller.delete);

  // Получить активные записи послужного списка по должности
  router.get('/active/position/<positionId>', controller.getActiveByPosition);

  return router;
}
