# Task: Реализация моделей данных и подключения к SQLite в Dart

**Goal:** Реализовать модели данных и репозитории для работы с базой данных SQLite в бэкенде на Dart.

**Steps:**

1. ✅ Проанализировать существующую структуру базы данных из файла `db_schema.md`
2. ✅ Настроить подключение к SQLite в `lib/database/database.dart`:
   - ✅ Использовать пакет `sqlite3` для работы с SQLite
   - ✅ Создать класс `Database` для управления подключением
   - ✅ Реализовать методы для выполнения запросов
3. ✅ Создать модели данных в директории `lib/models/`:
   - ✅ `employee.dart` - модель сотрудника
   - ✅ `position.dart` - модель должности
   - ✅ `military_rank.dart` - модель воинского звания
   - ✅ `rank_type.dart` - модель типа звания
   - ✅ `service_history.dart` - модель послужного списка
   - ✅ `contract.dart` - модель контракта
   - ✅ `order.dart` - модель приказа
   - ✅ `order_attachment.dart` - модель вложения к приказу
   - ✅ `hierarchy.dart` - модель иерархии
   - ✅ `preliminary_candidate.dart` - модель предварительного кандидата
   - ✅ `attestation.dart` - модель аттестации
   - ✅ `education_detail.dart` - модель образования
   - ✅ `foreign_travel.dart` - модель зарубежной поездки
   - ✅ `state_award.dart` - модель государственной награды
   - ✅ `departmental_award.dart` - модель ведомственной награды
   - ✅ `combat_service.dart` - модель участия в боевых действиях
   - ✅ `family.dart` - модель семьи
4. ✅ Реализовать сериализацию/десериализацию моделей с помощью `json_serializable`
5. ✅ Создать репозитории для работы с моделями в директории `lib/database/repositories/`:
   - ✅ `employee_repository.dart` - репозиторий для работы с сотрудниками
   - ✅ `position_repository.dart` - репозиторий для работы с должностями
   - ✅ `military_rank_repository.dart` - репозиторий для работы с воинскими званиями
   - ✅ `rank_type_repository.dart` - репозиторий для работы с типами званий
   - ✅ `service_history_repository.dart` - репозиторий для работы с послужным списком
   - ✅ `contract_repository.dart` - репозиторий для работы с контрактами
   - ✅ `order_repository.dart` - репозиторий для работы с приказами
6. ✅ Создать SQL-файлы миграций в директории `migrations/`:
   - ✅ `01_create_employees.sql` - создание таблицы сотрудников
   - ✅ `02_create_positions.sql` - создание таблицы должностей
   - ✅ `03_create_rank_types.sql` - создание таблицы типов званий
   - ✅ `04_create_military_ranks.sql` - создание таблицы воинских званий
   - ✅ `05_create_preliminary_candidates.sql` - создание таблицы предварительных кандидатов
   - ✅ `06_create_hierarchy.sql` - создание таблицы иерархии
   - ✅ `07_create_orders.sql` - создание таблицы приказов
   - ✅ `08_create_order_attachments.sql` - создание таблицы вложений к приказам
   - ✅ `09_create_contracts.sql` - создание таблицы контрактов
   - ✅ `10_create_attestations.sql` - создание таблицы аттестаций
   - ✅ `11_create_education_details.sql` - создание таблицы образования
   - ✅ `12_create_foreign_travel.sql` - создание таблицы зарубежных поездок
   - ✅ `13_create_state_awards.sql` - создание таблицы государственных наград
   - ✅ `14_create_departmental_awards.sql` - создание таблицы ведомственных наград
   - ✅ `15_create_combat_service.sql` - создание таблицы участия в боевых действиях
   - ✅ `16_create_family.sql` - создание таблицы семьи
   - ✅ `17_create_service_history.sql` - создание таблицы послужного списка
7. ✅ Реализовать механизм применения миграций при запуске сервера

**Result:**
- ✅ Настроенное подключение к базе данных SQLite
- ✅ Полный набор моделей данных, соответствующих структуре базы данных
- ✅ Репозитории для работы с моделями
- ✅ Миграции для создания всех необходимых таблиц
- ✅ Механизм применения миграций при запуске сервера

**Status:** Completed
