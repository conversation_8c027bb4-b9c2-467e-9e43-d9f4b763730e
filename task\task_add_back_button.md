# Задача: Добавление кнопки "Назад" на страницы детального просмотра

**Цель:** Добавить кнопку "Назад" на страницы детального просмотра записей, чтобы пользователь мог легко вернуться к предыдущему экрану без использования кнопки браузера.

**Подзадачи:**

1. **[X] `task_1_create_main_task_file.md`**: Создание этого файла задачи.
   * Status: Completed
2. **[X] `task_2_add_back_button_to_employee_details.md`**: Добавить кнопку "Назад" на страницу детального просмотра сотрудника.
   * Status: Completed
3. **[X] `task_3_add_back_button_to_position_details.md`**: Добавить кнопку "Назад" на страницу детального просмотра должности.
   * Status: Completed

**Желаемый результат:**
* На страницах детального просмотра записей добавлена кнопка "Назад" в левой части AppBar
* Кнопка "Назад" корректно возвращает пользователя к соответствующему списку (сотрудников или должностей)
* Кнопка "Назад" работает надежно даже при прямом переходе на страницу деталей
* Кнопка "Назад" соответствует общему стилю приложения
* Улучшена навигация в приложении, что повышает удобство использования
