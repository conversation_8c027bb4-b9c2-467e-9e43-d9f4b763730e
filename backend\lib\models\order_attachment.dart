import 'package:json_annotation/json_annotation.dart';

part 'order_attachment.g.dart';

@JsonSerializable()
class OrderAttachment {
  final int? id;
  final int orderId;
  final String fileName;
  final List<int> data;
  final String? uploadedAt;
  final String? createdAt;
  final String? updatedAt;

  OrderAttachment({
    this.id,
    required this.orderId,
    required this.fileName,
    required this.data,
    this.uploadedAt,
    this.createdAt,
    this.updatedAt,
  });

  factory OrderAttachment.fromJson(Map<String, dynamic> json) => _$OrderAttachmentFromJson(json);

  Map<String, dynamic> toJson() => _$OrderAttachmentToJson(this);
}
