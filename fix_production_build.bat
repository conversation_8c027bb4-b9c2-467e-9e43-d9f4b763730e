@echo off
echo ===== Fixing Production Build =====
echo.

echo 1. Checking for SQLite DLL...
if exist dist\sqlite3.dll (
    echo SQLite DLL already exists in dist directory.
    echo Backing up existing sqlite3.dll to sqlite3.dll.bak
    if exist dist\sqlite3.dll.bak del dist\sqlite3.dll.bak
    rename dist\sqlite3.dll sqlite3.dll.bak
) else (
    echo SQLite DLL not found in dist directory.
)

echo.
echo 2. Downloading 32-bit SQLite DLL...
echo Creating temporary directory...
if not exist temp mkdir temp
cd temp

echo Downloading 32-bit SQLite DLL from sqlite.org...
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://www.sqlite.org/2023/sqlite-dll-win32-x86-3430200.zip' -OutFile 'sqlite.zip'}"

if not exist sqlite.zip (
    echo.
    echo Download failed. Please download SQLite DLL manually from:
    echo https://www.sqlite.org/download.html
    echo.
    echo Download the "Precompiled Binaries for Windows" (32-bit) zip file:
    echo sqlite-dll-win32-x86-3430200.zip
    echo Extract it and copy sqlite3.dll to the dist directory.
    echo.
    cd ..
    echo Press any key to exit...
    pause
    exit /b 1
)

echo.
echo 3. Extracting SQLite DLL...
powershell -Command "& {Expand-Archive -Path 'sqlite.zip' -DestinationPath '.' -Force}"

if not exist sqlite3.dll (
    echo.
    echo Extraction failed. Please download SQLite DLL manually from:
    echo https://www.sqlite.org/download.html
    echo.
    echo Download the "Precompiled Binaries for Windows" (32-bit) zip file:
    echo sqlite-dll-win32-x86-3430200.zip
    echo Extract it and copy sqlite3.dll to the dist directory.
    echo.
    cd ..
    echo Press any key to exit...
    pause
    exit /b 1
)

echo.
echo 4. Copying 32-bit SQLite DLL to dist directory...
copy sqlite3.dll ..\dist\sqlite3.dll
if %ERRORLEVEL% neq 0 (
    echo Error copying SQLite DLL!
    cd ..
    exit /b %ERRORLEVEL%
)

echo.
echo 5. Cleaning up...
cd ..
rmdir /s /q temp

echo.
echo 6. Creating db and migrations directories in dist...
if not exist dist\db mkdir dist\db
if not exist dist\migrations mkdir dist\migrations

echo.
echo 7. Copying migration files to dist\migrations...
if exist backend\migrations\*.sql (
    xcopy /Y backend\migrations\*.sql dist\migrations\
) else (
    echo Warning: Migration files not found in backend\migrations
    echo You may need to copy migration files manually.
)

echo.
echo ===== Production Build Fix Completed! =====
echo.
echo Your production build should now work correctly.
echo The application will use the dist\db directory for the database
echo and dist\migrations for migrations.
echo.
echo To run the application:
echo 1. Navigate to the dist directory
echo 2. Run hr_system.exe
echo 3. Access the application at http://localhost:8080
echo.
echo Press any key to exit...
pause
