-- Добавление поля position_id в таблицу employees
ALTER TABLE employees ADD COLUMN position_id INTEGER;

-- Добавление внешнего ключа
PRAGMA foreign_keys = OFF;

-- Создание временной таблицы
CREATE TABLE employees_temp (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    last_name TEXT NOT NULL,
    first_name TEXT NOT NULL,
    middle_name TEXT,
    gender INTEGER NOT NULL DEFAULT 1,
    birth_date TEXT,
    place_of_birth TEXT,
    nationality TEXT,
    personal_number TEXT UNIQUE,
    children_under_16 INTEGER,
    academic_degree TEXT,
    veteran_since INTEGER,
    position_id INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    <PERSON>OREIG<PERSON> KEY (position_id) REFERENCES positions(id) ON DELETE SET NULL ON UPDATE CASCADE
);

-- Копирование данных из старой таблицы во временную
INSERT INTO employees_temp (
    id, last_name, first_name, middle_name, gender, birth_date, place_of_birth,
    nationality, personal_number, children_under_16, academic_degree, veteran_since,
    created_at, updated_at
)
SELECT
    id, last_name, first_name, middle_name, gender, birth_date, place_of_birth,
    nationality, personal_number, children_under_16, academic_degree, veteran_since,
    created_at, updated_at
FROM employees;

-- Удаление старой таблицы
DROP TABLE employees;

-- Переименование временной таблицы
ALTER TABLE employees_temp RENAME TO employees;

-- Обновление position_id на основе активных записей в service_history
UPDATE employees
SET position_id = (
    SELECT position_id
    FROM service_history
    WHERE service_history.employee_id = employees.id
    AND service_history.end_date IS NULL
    AND service_history.position_id IS NOT NULL
    ORDER BY service_history.start_date DESC
    LIMIT 1
);

-- Обновление поля updated_at для всех записей
UPDATE employees SET updated_at = CURRENT_TIMESTAMP;

PRAGMA foreign_keys = ON;
