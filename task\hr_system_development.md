# Task: Разработка системы для отдела кадров

**Goal:** Создать систему для отдела кадров с разделением на бэкенд и фронтенд, совместимую с Windows 7.

**Sub-tasks:**

1.  **[X] `task_1_create_main_task_file.md`**: Create this task tracking file.
    *   Status: Completed
2.  **[X] `task_2_setup_backend_dart.md`**: Настройка бэкенда на Dart с Shelf.
    *   Status: Completed
3.  **[X] `task_3_setup_frontend_flutter.md`**: Настройка фронтенда на Flutter.
    *   Status: Completed
4.  **[X] `task_4_database_models_dart.md`**: Реализация моделей данных и подключения к SQLite в Dart.
    *   Status: Completed
5.  **[X] `task_5_add_education_levels.md`**: Добавление полей для уровней образования.
    *   Status: Completed
6.  **[X] `task_6_backend_api_dart_completed.md`**: Разработка REST API на Dart/Shelf для работы с данными.
    *   Status: Completed
7.  **[ ] `task_7_frontend_ui_flutter.md`**: Разработка пользовательского интерфейса на Flutter.
    *   Status: In Progress
8.  **[ ] `task_8_static_file_serving_dart.md`**: Настройка раздачи статических файлов фронтенда через бэкенд на Dart.
    *   Status: Pending
9.  **[ ] `task_9_testing_dart_flutter.md`**: Тестирование системы.
    *   Status: Pending
10. **[ ] `task_10_documentation.md`**: Создание документации по установке и использованию.
    *   Status: Pending

**Desired Outcome:**
*   Полнофункциональная система для отдела кадров, работающая на Windows 7
*   Бэкенд на Dart с Shelf, предоставляющий REST API
*   Фронтенд на Flutter с современным пользовательским интерфейсом
*   Система, способная работать с базой данных SQLite
*   Документация по установке и использованию системы
