import 'package:json_annotation/json_annotation.dart';

part 'position.g.dart';

@JsonSerializable()
class Position {
  final int? id;
  final String title;
  final String? department;
  final String? unitName;
  final int? womenAllowed;
  final int? militaryRankStaffId;
  final String? vus;
  final String? vusPss;
  final String? positionCodePss;
  final String? tariffCategory;
  final int? isFlightCrew;
  final int? antiCorruption;
  final int? requiredEducationLevel;
  final String? createdAt;
  final String? updatedAt;

  Position({
    this.id,
    required this.title,
    this.department,
    this.unitName,
    this.womenAllowed,
    this.militaryRankStaffId,
    this.vus,
    this.vusPss,
    this.positionCodePss,
    this.tariffCategory,
    this.isFlightCrew,
    this.antiCorruption,
    this.requiredEducationLevel,
    this.createdAt,
    this.updatedAt,
  });

  factory Position.fromJson(Map<String, dynamic> json) =>
      _$PositionFromJson(json);

  Map<String, dynamic> toJson() => _$PositionToJson(this);

  @override
  String toString() {
    return 'Position{id: $id, title: $title, department: $department}';
  }

  /// Возвращает текстовое представление требуемого уровня образования
  String get requiredEducationLevelText {
    switch (requiredEducationLevel) {
      case 0:
        return 'Не требуется';
      case 1:
        return 'Основное общее образование';
      case 2:
        return 'Среднее общее образование';
      case 3:
        return 'Среднее профессиональное образование';
      case 4:
        return 'Высшее образование';
      default:
        return 'Не указано';
    }
  }
}
