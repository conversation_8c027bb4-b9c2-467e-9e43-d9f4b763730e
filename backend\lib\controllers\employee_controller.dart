import 'dart:convert';
import 'package:shelf/shelf.dart';
import 'package:backend/database/repositories/employee_repository.dart';
import 'package:backend/models/employee.dart';
import 'package:backend/utils/validator.dart';
import 'package:logging/logging.dart';

class EmployeeController {
  static final Logger _logger = Logger('EmployeeController');
  final EmployeeRepository _repository = EmployeeRepository();

  /// Получить всех сотрудников
  Response getAll(Request request) {
    _logger.info('Getting all employees');

    try {
      final employees = _repository.getAll();

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': employees.map((e) => e.toJson()).toList(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error getting all employees: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Получить сотрудника по ID
  Response getById(Request request, String id) {
    _logger.info('Getting employee by ID: $id');

    try {
      final employeeId = int.parse(id);
      final employee = _repository.getById(employeeId);

      if (employee == null) {
        return Response.notFound(
          jsonEncode({
            'success': false,
            'error': 'Not Found',
            'message': 'Employee with ID $id not found',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': employee.toJson(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error getting employee by ID: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Создать нового сотрудника
  Future<Response> create(Request request) async {
    _logger.info('Creating new employee');

    try {
      final jsonBody = await request.readAsString();
      final Map<String, dynamic> data = jsonDecode(jsonBody);

      // Валидация данных
      final errors = Validator.validateEmployee(data);
      if (errors.isNotEmpty) {
        return Response.badRequest(
          body: jsonEncode({
            'success': false,
            'error': 'Validation Error',
            'validationErrors': errors,
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      // Проверка обязательных полей
      final requiredFields = ['lastName', 'firstName'];
      if (!Validator.hasRequiredFields(data, requiredFields)) {
        return Response.badRequest(
          body: jsonEncode({
            'success': false,
            'error': 'Bad Request',
            'message': 'Required fields missing: lastName, firstName',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final employee = Employee(
        lastName: data['lastName'],
        firstName: data['firstName'],
        middleName: data['middleName'],
        gender: data['gender'] ?? 1,
        birthDate: data['birthDate'],
        placeOfBirth: data['placeOfBirth'],
        nationality: data['nationality'],
        personalNumber: data['personalNumber'],
        childrenUnder16: data['childrenUnder16'],
        academicDegree: data['academicDegree'],
        veteranSince: data['veteranSince'],
      );

      final id = _repository.create(employee);

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': {'id': id},
          'message': 'Employee created successfully',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error creating employee: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Обновить сотрудника
  Future<Response> update(Request request, String id) async {
    _logger.info('Updating employee with ID: $id');

    try {
      final employeeId = int.parse(id);
      final existingEmployee = _repository.getById(employeeId);

      if (existingEmployee == null) {
        return Response.notFound(
          jsonEncode({
            'success': false,
            'error': 'Not Found',
            'message': 'Employee with ID $id not found',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final jsonBody = await request.readAsString();
      final Map<String, dynamic> data = jsonDecode(jsonBody);

      // Валидация данных
      final errors = Validator.validateEmployee(data);
      if (errors.isNotEmpty) {
        return Response.badRequest(
          body: jsonEncode({
            'success': false,
            'error': 'Validation Error',
            'validationErrors': errors,
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final employee = Employee(
        id: employeeId,
        lastName: data['lastName'] ?? existingEmployee.lastName,
        firstName: data['firstName'] ?? existingEmployee.firstName,
        middleName: data['middleName'] ?? existingEmployee.middleName,
        gender: data['gender'] ?? existingEmployee.gender,
        birthDate: data['birthDate'] ?? existingEmployee.birthDate,
        placeOfBirth: data['placeOfBirth'] ?? existingEmployee.placeOfBirth,
        nationality: data['nationality'] ?? existingEmployee.nationality,
        personalNumber: data['personalNumber'] ?? existingEmployee.personalNumber,
        childrenUnder16: data['childrenUnder16'] ?? existingEmployee.childrenUnder16,
        academicDegree: data['academicDegree'] ?? existingEmployee.academicDegree,
        veteranSince: data['veteranSince'] ?? existingEmployee.veteranSince,
      );

      final success = _repository.update(employee);

      if (!success) {
        return Response.internalServerError(
          body: jsonEncode({
            'success': false,
            'error': 'Internal Server Error',
            'message': 'Failed to update employee',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'Employee updated successfully',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error updating employee: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Удалить сотрудника
  Response delete(Request request, String id) {
    _logger.info('Deleting employee with ID: $id');

    try {
      final employeeId = int.parse(id);
      final existingEmployee = _repository.getById(employeeId);

      if (existingEmployee == null) {
        return Response.notFound(
          jsonEncode({
            'success': false,
            'error': 'Not Found',
            'message': 'Employee with ID $id not found',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final success = _repository.delete(employeeId);

      if (!success) {
        return Response.internalServerError(
          body: jsonEncode({
            'success': false,
            'error': 'Internal Server Error',
            'message': 'Failed to delete employee',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'Employee deleted successfully',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error deleting employee: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }
}
