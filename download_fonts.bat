@echo off
echo ===== Downloading Roboto fonts for offline mode =====
echo.

echo Creating fonts directory...
if not exist frontend\web\fonts mkdir frontend\web\fonts

echo.
echo Downloading Roboto fonts from Google Fonts...

cd frontend\web\fonts

echo Downloading Roboto Regular...
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu4mxKKTU1Kg.woff2' -OutFile 'roboto-regular.woff2'}"

echo Downloading Roboto Medium...
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmEU9fBBc4AMP6lQ.woff2' -OutFile 'roboto-medium.woff2'}"

echo Downloading Roboto Bold...
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmWUlfBBc4AMP6lQ.woff2' -OutFile 'roboto-bold.woff2'}"

echo Downloading Roboto Light...
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmSU5fBBc4AMP6lQ.woff2' -OutFile 'roboto-light.woff2'}"

echo Downloading Roboto Thin...
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://fonts.gstatic.com/s/roboto/v30/KFOkCnqEu92Fr1MmgVxIIzIXKMny.woff2' -OutFile 'roboto-thin.woff2'}"

cd ..\..\..

echo.
echo Fonts downloaded successfully!
echo.
echo Press any key to continue...
pause
