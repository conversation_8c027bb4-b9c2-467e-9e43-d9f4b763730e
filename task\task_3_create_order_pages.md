# Подзадача: Создание страниц для работы с приказами

## Описание
Необходимо создать страницы для работы с приказами, включая список приказов, страницу деталей приказа и форму создания/редактирования приказа. Приказы являются важной частью системы отдела кадров, так как они документируют все кадровые изменения.

## Текущее состояние
- API для работы с приказами уже реализовано на бэкенде
- Модель `Order` определена как в бэкенде, так и во фронтенде
- Страницы для работы с приказами реализованы во фронтенде

## Выполненные шаги
1. Создана модель для работы с приказами:
   - `frontend/lib/models/order.dart` - модель приказа

2. Создан сервис для работы с API приказов:
   - `frontend/lib/services/order_service.dart` - сервис для взаимодействия с API приказов

3. Создан провайдер для управления состоянием приказов:
   - `frontend/lib/providers/order_provider.dart` - провайдер для управления состоянием приказов

4. Созданы страницы для работы с приказами:
   - `frontend/lib/pages/order/order_list_page.dart` - страница со списком приказов
   - `frontend/lib/pages/order/order_details_page.dart` - страница с деталями приказа
   - `frontend/lib/pages/order/order_form_page.dart` - форма создания/редактирования приказа

5. Обновлены маршруты в `frontend/lib/routes.dart`:
   - Добавлены маршруты для страниц приказов

6. Обновлено боковое меню в `frontend/lib/components/app_drawer.dart`:
   - Добавлен пункт меню для перехода к списку приказов

7. Обновлен файл `frontend/lib/app.dart`:
   - Добавлен OrderProvider в список провайдеров

## Реализованные функции
1. Страница списка приказов (`order_list_page.dart`):
   - Отображение списка приказов с основной информацией (номер, дата, кем издан)
   - Кнопка для создания нового приказа
   - Кнопки для просмотра, редактирования и удаления каждого приказа
   - Диалог подтверждения при удалении приказа

2. Страница деталей приказа (`order_details_page.dart`):
   - Отображение всей информации о приказе
   - Отображение метаданных (ID, дата создания, дата обновления)
   - Кнопка для редактирования приказа
   - Кнопка "Назад" для возврата к списку приказов

3. Форма создания/редактирования приказа (`order_form_page.dart`):
   - Поля для ввода всех данных приказа (номер, дата, кем издан, описание)
   - Валидация обязательных полей
   - Кнопка для сохранения
   - Кнопка "Назад" для возврата к списку приказов

## Результат
- Полнофункциональные страницы для работы с приказами
- Возможность просмотра, создания, редактирования и удаления приказов
- Интеграция с API бэкенда
- Удобный пользовательский интерфейс с валидацией ввода

## Статус
Завершено
