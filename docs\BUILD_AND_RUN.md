# Инструкция по сборке и запуску проекта

В данном документе описаны шаги для сборки и запуска HR системы, состоящей из Dart/Shelf бэкенда и Flutter фронтенда.

## Требования

- Dart SDK 3.3.0 или выше
- Flutter SDK 3.3.0 или выше
- Microsoft Edge (для разработки)
- Windows 7 или выше

## Структура проекта

```
project/
├── backend/                # Бэкенд на Dart + Shelf
│   ├── bin/                # Исполняемые файлы
│   │   └── server.dart     # Точка входа для запуска сервера
│   ├── lib/                # Библиотеки
│   ├── web/                # Статические файлы фронтенда
│   └── pubspec.yaml        # Зависимости бэкенда
├── frontend/               # Фронтенд на Flutter
│   ├── lib/                # Исходный код
│   └── pubspec.yaml        # Зависимости фронтенда
├── build_and_run.bat       # Скрипт для сборки и запуска в продакшене
└── run_dev.bat             # Скрипт для запуска в режиме разработки
```

## Автоматическая сборка и запуск

Для удобства сборки и запуска проекта созданы два скрипта:

### 1. Запуск в режиме разработки

Для запуска проекта в режиме разработки выполните:

```
run_dev.bat
```

Этот скрипт:
1. Устанавливает зависимости бэкенда
2. Запускает бэкенд в режиме разработки
3. Устанавливает зависимости фронтенда
4. Запускает фронтенд в режиме разработки в Microsoft Edge

После запуска:
- Бэкенд будет доступен по адресу: http://localhost:8080
- Фронтенд будет доступен по адресу: http://localhost:3000

### 2. Сборка и запуск для продакшена

Для сборки проекта для продакшена выполните:

```
build_and_run.bat
```

Этот скрипт:
1. Устанавливает зависимости бэкенда
2. Устанавливает зависимости фронтенда
3. Собирает веб-версию фронтенда
4. Копирует собранные файлы фронтенда в директорию бэкенда
5. Компилирует бэкенд в исполняемый файл

После сборки для запуска сервера выполните:

```
cd backend
bin\server.exe
```

Приложение будет доступно по адресу: http://localhost:8080

## Ручная сборка и запуск

Если вы хотите выполнить сборку и запуск вручную, следуйте этим шагам:

### 1. Сборка бэкенда на Dart/Shelf

```bash
# Установка зависимостей бэкенда
cd backend
dart pub get

# Компиляция бэкенда в исполняемый файл (опционально)
dart compile exe bin/server.dart -o bin/server.exe
```

### 2. Сборка веб-версии Flutter фронтенда

```bash
# Установка зависимостей фронтенда
cd frontend
flutter pub get

# Сборка веб-версии фронтенда
flutter build web --release
```

### 3. Настройка раздачи статических файлов фронтенда

```bash
# Создание директории для статических файлов
mkdir -p backend/web

# Копирование собранных файлов фронтенда
xcopy /E /Y frontend/build/web backend/web/
```

### 4. Запуск бэкенда

```bash
# Запуск бэкенда в режиме разработки
cd backend
dart run bin/server.dart

# ИЛИ запуск скомпилированного бэкенда
cd backend
bin\server.exe
```

После запуска приложение будет доступно по адресу: http://localhost:8080

## Доступ к приложению

После запуска сервера:

1. Откройте браузер (рекомендуется Microsoft Edge)
2. Перейдите по адресу http://localhost:8080
3. Вы увидите интерфейс HR системы

## Примечания

- Порт по умолчанию: 8080 (можно изменить через переменную окружения PORT)
- Для разработки фронтенда используется порт 3000
- Бэкенд автоматически обслуживает API запросы и раздает статические файлы фронтенда
