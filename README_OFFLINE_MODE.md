# Flutter Web Offline Mode - Инструкция по использованию

## Обзор

Данное решение позволяет Flutter веб-приложению работать полностью автономно без интернет-соединения. Все ресурсы, включая CanvasKit, встроены в сборку приложения.

## Быстрый старт

### 1. Сборка приложения
```bash
# Запустите обновленный скрипт сборки
.\build_production_en.bat
```

### 2. Запуск приложения
```bash
# Перейдите в директорию dist и запустите
cd dist
.\hr_system.exe

# ИЛИ используйте startup скрипт
.\dist\start_hr_system.bat
```

### 3. Открытие в браузере
Откройте http://localhost:8080 в любом современном браузере.

## Тестирование автономного режима

### Автоматический тест
```bash
.\test_offline_mode.bat
```

### Ручной тест
1. Запустите приложение
2. Откройте http://localhost:8080
3. **Отключите интернет-соединение**
4. Обновите страницу (F5)
5. Приложение должно работать нормально

### Проверка в консоли браузера
Откройте Developer Tools (F12) и проверьте консоль на наличие сообщений:
- ✅ "Loading offline enhancer..."
- ✅ "Flutter offline configuration loaded"
- ✅ "Offline enhancer loaded successfully"
- ❌ Не должно быть ошибок загрузки CanvasKit

## Что изменилось

### Новые файлы
- `frontend/web/flutter_config.js` - конфигурация Flutter для автономного режима
- `frontend/web/offline_enhancer.js` - блокировка внешних запросов
- `test_offline_mode.bat` - скрипт для тестирования
- Документация: `OFFLINE_MODE_SETUP.md`, `FLUTTER_OFFLINE_SOLUTION.md`

### Изменённые файлы
- `frontend/web/index.html` - добавлена конфигурация автономного режима
- `build_production_en.bat` - обновлены флаги сборки и копирование файлов

### Команда сборки
Теперь используется:
```bash
flutter build web --release --web-renderer canvaskit --dart-define=FLUTTER_WEB_CANVASKIT_URL=/canvaskit/
```

## Структура файлов

```
dist/
├── hr_system.exe           # Основное приложение
├── sqlite3.dll             # SQLite библиотека
├── db/                     # База данных
├── migrations/             # SQL миграции
└── web/                    # Веб-ресурсы
    ├── canvaskit/          # Локальные файлы CanvasKit
    ├── flutter_config.js   # Конфигурация Flutter
    ├── offline_enhancer.js # Улучшения автономного режима
    ├── index.html          # Главная страница
    ├── main.dart.js        # Скомпилированное приложение
    └── flutter_service_worker.js # Service Worker
```

## Устранение неполадок

### Проблема: Приложение не загружается
**Решение:**
1. Убедитесь, что файлы `flutter_config.js` и `offline_enhancer.js` присутствуют в `dist/web/`
2. Проверьте консоль браузера на ошибки
3. Очистите кеш браузера (Ctrl+Shift+R)

### Проблема: Ошибки CanvasKit
**Решение:**
1. Проверьте наличие директории `dist/web/canvaskit/` с файлами
2. Убедитесь, что используется правильная команда сборки
3. Пересоберите приложение с помощью `build_production_en.bat`

### Проблема: Внешние запросы всё ещё происходят
**Решение:**
1. Проверьте, что `offline_enhancer.js` загружается (в консоли браузера)
2. Убедитесь, что скрипт выполняется до загрузки Flutter
3. Проверьте Network tab в Developer Tools

## Технические детали

### Блокируемые домены
- unpkg.com
- cdn.jsdelivr.net
- gstatic.com
- googleapis.com
- fonts.googleapis.com
- fonts.gstatic.com

### Механизм работы
1. `flutter_config.js` настраивает Flutter на использование локального CanvasKit
2. `offline_enhancer.js` блокирует все внешние запросы
3. Service Worker кеширует все ресурсы
4. Запросы к CanvasKit перенаправляются на локальные файлы

## Поддержка

Если возникают проблемы:
1. Проверьте логи сервера в консоли
2. Изучите консоль браузера на наличие ошибок
3. Убедитесь, что все файлы конфигурации присутствуют
4. Попробуйте пересобрать приложение

## Заключение

Приложение теперь полностью автономно и может работать на серверах без интернет-соединения. Все необходимые ресурсы встроены в сборку.
