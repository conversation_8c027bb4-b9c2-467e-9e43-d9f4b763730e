// ignore_for_file: avoid_print

import 'dart:convert';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:frontend/models/order_attachment.dart';

/// Сервис для работы с API вложений к приказам
class OrderAttachmentService {
  static const String baseUrl = 'http://localhost:8080/api/order-attachments';

  /// Получить все вложения
  Future<List<OrderAttachment>> getAttachments() async {
    final response = await http.get(Uri.parse(baseUrl));

    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      if (data['success'] == true && data['data'] != null) {
        return (data['data'] as List)
            .map((item) => OrderAttachment.fromJson(item))
            .toList();
      }
      return [];
    } else {
      throw Exception('Failed to load order attachments');
    }
  }

  /// Получить вложения по ID приказа
  Future<List<OrderAttachment>> getAttachmentsByOrderId(int orderId) async {
    final response = await http.get(Uri.parse('$baseUrl/order/$orderId'));

    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      if (data['success'] == true && data['data'] != null) {
        return (data['data'] as List)
            .map((item) => OrderAttachment.fromJson(item))
            .toList();
      }
      return [];
    } else {
      throw Exception('Failed to load order attachments for order $orderId');
    }
  }

  /// Получить вложение по ID
  Future<OrderAttachment?> getAttachment(int id) async {
    final response = await http.get(Uri.parse('$baseUrl/$id'));

    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      if (data['success'] == true && data['data'] != null) {
        return OrderAttachment.fromJson(data['data']);
      }
      return null;
    } else if (response.statusCode == 404) {
      return null;
    } else {
      throw Exception('Failed to load order attachment');
    }
  }

  /// Создать новое вложение
  Future<int> createAttachment(
      int orderId, String fileName, List<int> fileData) async {
    // Проверяем, что данные файла не пустые
    if (fileData.isEmpty) {
      throw Exception('Файл пустой. Невозможно загрузить пустой файл.');
    }

    print(
        'Подготовка к загрузке файла: $fileName, размер: ${fileData.length} байт');

    // Кодируем данные файла в base64
    final fileDataBase64 = base64Encode(fileData);
    print(
        'Данные закодированы в base64, длина строки: ${fileDataBase64.length}');

    try {
      final response = await http.post(
        Uri.parse(baseUrl),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'orderId': orderId,
          'fileName': fileName,
          'fileData': fileDataBase64,
        }),
      );

      print('Статус ответа: ${response.statusCode}');
      print('Тело ответа: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          final id = data['data']['id'];
          print('Файл успешно загружен, ID: $id');
          return id;
        }
        throw Exception('Failed to create order attachment: No ID returned');
      } else {
        throw Exception('Failed to create order attachment: ${response.body}');
      }
    } catch (e) {
      print('Исключение при загрузке файла: $e');
      rethrow;
    }
  }

  /// Обновить вложение
  Future<bool> updateAttachment(OrderAttachment attachment) async {
    if (attachment.id == null) {
      throw ArgumentError('Order attachment ID cannot be null');
    }

    // Подготавливаем данные для отправки
    final Map<String, dynamic> requestData = {
      'orderId': attachment.orderId,
      'fileName': attachment.fileName,
    };

    // Если есть данные файла, кодируем их в base64
    if (attachment.data != null) {
      requestData['fileData'] = base64Encode(attachment.data!);
    }

    final response = await http.put(
      Uri.parse('$baseUrl/${attachment.id}'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode(requestData),
    );

    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      return data['success'] == true;
    } else {
      throw Exception('Failed to update order attachment: ${response.body}');
    }
  }

  /// Удалить вложение
  Future<bool> deleteAttachment(int id) async {
    final response = await http.delete(Uri.parse('$baseUrl/$id'));

    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      return data['success'] == true;
    } else {
      throw Exception('Failed to delete order attachment: ${response.body}');
    }
  }

  /// Скачать вложение
  Future<Uint8List> downloadAttachment(int id) async {
    try {
      final url = Uri.parse('$baseUrl/$id/download');
      print('Запрос на скачивание файла: $url');

      final response = await http.get(url);

      print('Статус ответа: ${response.statusCode}');
      print('Заголовки ответа: ${response.headers}');
      print('Размер полученных данных: ${response.bodyBytes.length} байт');

      if (response.statusCode == 200) {
        if (response.bodyBytes.isEmpty) {
          throw Exception('Получены пустые данные файла');
        }
        return response.bodyBytes;
      } else {
        throw Exception(
            'Ошибка при скачивании файла: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      print('Исключение при скачивании файла: $e');
      rethrow;
    }
  }
}
