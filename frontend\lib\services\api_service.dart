import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:frontend/models/employee.dart';
import 'package:frontend/models/position.dart';

class ApiService {
  static const String baseUrl = 'http://localhost:8080/api';

  // Employees
  Future<List<Employee>> getEmployees() async {
    final response = await http.get(Uri.parse('$baseUrl/employees'));
    
    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      if (data['success'] == true && data['data'] != null) {
        return (data['data'] as List)
            .map((item) => Employee.fromJson(item))
            .toList();
      }
      return [];
    } else {
      throw Exception('Failed to load employees');
    }
  }

  Future<Employee?> getEmployee(int id) async {
    final response = await http.get(Uri.parse('$baseUrl/employees/$id'));
    
    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      if (data['success'] == true && data['data'] != null) {
        return Employee.fromJson(data['data']);
      }
      return null;
    } else if (response.statusCode == 404) {
      return null;
    } else {
      throw Exception('Failed to load employee');
    }
  }

  Future<int> createEmployee(Employee employee) async {
    final response = await http.post(
      Uri.parse('$baseUrl/employees'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode(employee.toJson()),
    );
    
    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      if (data['success'] == true && data['data'] != null) {
        return data['data']['id'];
      }
      throw Exception('Failed to create employee: ${data['message']}');
    } else {
      throw Exception('Failed to create employee');
    }
  }

  Future<void> updateEmployee(Employee employee) async {
    if (employee.id == null) {
      throw Exception('Employee ID cannot be null');
    }
    
    final response = await http.put(
      Uri.parse('$baseUrl/employees/${employee.id}'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode(employee.toJson()),
    );
    
    if (response.statusCode != 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      throw Exception('Failed to update employee: ${data['message']}');
    }
  }

  Future<void> deleteEmployee(int id) async {
    final response = await http.delete(Uri.parse('$baseUrl/employees/$id'));
    
    if (response.statusCode != 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      throw Exception('Failed to delete employee: ${data['message']}');
    }
  }

  // Positions
  Future<List<Position>> getPositions() async {
    final response = await http.get(Uri.parse('$baseUrl/positions'));
    
    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      if (data['success'] == true && data['data'] != null) {
        return (data['data'] as List)
            .map((item) => Position.fromJson(item))
            .toList();
      }
      return [];
    } else {
      throw Exception('Failed to load positions');
    }
  }

  Future<Position?> getPosition(int id) async {
    final response = await http.get(Uri.parse('$baseUrl/positions/$id'));
    
    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      if (data['success'] == true && data['data'] != null) {
        return Position.fromJson(data['data']);
      }
      return null;
    } else if (response.statusCode == 404) {
      return null;
    } else {
      throw Exception('Failed to load position');
    }
  }

  Future<int> createPosition(Position position) async {
    final response = await http.post(
      Uri.parse('$baseUrl/positions'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode(position.toJson()),
    );
    
    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      if (data['success'] == true && data['data'] != null) {
        return data['data']['id'];
      }
      throw Exception('Failed to create position: ${data['message']}');
    } else {
      throw Exception('Failed to create position');
    }
  }

  Future<void> updatePosition(Position position) async {
    if (position.id == null) {
      throw Exception('Position ID cannot be null');
    }
    
    final response = await http.put(
      Uri.parse('$baseUrl/positions/${position.id}'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode(position.toJson()),
    );
    
    if (response.statusCode != 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      throw Exception('Failed to update position: ${data['message']}');
    }
  }

  Future<void> deletePosition(int id) async {
    final response = await http.delete(Uri.parse('$baseUrl/positions/$id'));
    
    if (response.statusCode != 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      throw Exception('Failed to delete position: ${data['message']}');
    }
  }
}
