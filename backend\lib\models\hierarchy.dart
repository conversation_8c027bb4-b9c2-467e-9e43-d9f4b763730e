import 'package:json_annotation/json_annotation.dart';

part 'hierarchy.g.dart';

@JsonSerializable()
class Hierarchy {
  final int? id;
  final int? parentId;
  final String entityType;
  final int entityId;
  final String? createdAt;
  final String? updatedAt;

  Hierarchy({
    this.id,
    this.parentId,
    required this.entityType,
    required this.entityId,
    this.createdAt,
    this.updatedAt,
  });

  factory Hierarchy.fromJson(Map<String, dynamic> json) => _$HierarchyFromJson(json);

  Map<String, dynamic> toJson() => _$HierarchyToJson(this);
}
