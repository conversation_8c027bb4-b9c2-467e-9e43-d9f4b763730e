import 'package:backend/database/database.dart';
import 'package:backend/models/rank_type.dart';
import 'package:logging/logging.dart';

class RankTypeRepository {
  static final Logger _logger = Logger('RankTypeRepository');

  /// Получить все типы званий
  List<RankType> getAll() {
    _logger.info('Getting all rank types');

    final result = AppDatabase.select('SELECT * FROM rank_types');

    return result.map((row) {
      return RankType(
        id: row['id'] as int,
        name: row['name'] as String,
        serviceYearsRequired: row['service_years_required'] as int?,
        category: row['category'] as String?,
        createdAt: row['created_at'] as String?,
        updatedAt: row['updated_at'] as String?,
      );
    }).toList();
  }

  /// Получить тип звания по ID
  RankType? getById(int id) {
    _logger.info('Getting rank type by ID: $id');

    final result = AppDatabase.select('SELECT * FROM rank_types WHERE id = ?', [
      id,
    ]);

    if (result.isEmpty) {
      return null;
    }

    final row = result.first;

    return RankType(
      id: row['id'] as int,
      name: row['name'] as String,
      serviceYearsRequired: row['service_years_required'] as int?,
      category: row['category'] as String?,
      createdAt: row['created_at'] as String?,
      updatedAt: row['updated_at'] as String?,
    );
  }

  /// Создать новый тип звания
  int create(RankType rankType) {
    _logger.info('Creating new rank type: $rankType');

    AppDatabase.execute(
      '''
      INSERT INTO rank_types (
        name, service_years_required, category
      ) VALUES (?, ?, ?)
      ''',
      [
        rankType.name,
        rankType.serviceYearsRequired,
        rankType.category,
      ],
    );

    return AppDatabase.database.lastInsertRowId;
  }

  /// Обновить тип звания
  bool update(RankType rankType) {
    _logger.info('Updating rank type: $rankType');

    if (rankType.id == null) {
      throw ArgumentError('Rank type ID cannot be null');
    }

    AppDatabase.execute(
      '''
      UPDATE rank_types SET
        name = ?, service_years_required = ?, category = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
      ''',
      [
        rankType.name,
        rankType.serviceYearsRequired,
        rankType.category,
        rankType.id,
      ],
    );

    return AppDatabase.database.updatedRows > 0;
  }

  /// Удалить тип звания
  bool delete(int id) {
    _logger.info('Deleting rank type with ID: $id');

    AppDatabase.execute('DELETE FROM rank_types WHERE id = ?', [id]);

    return AppDatabase.database.updatedRows > 0;
  }
}
