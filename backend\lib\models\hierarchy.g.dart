// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hierarchy.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Hierarchy _$HierarchyFromJson(Map<String, dynamic> json) => Hierarchy(
  id: (json['id'] as num?)?.toInt(),
  parentId: (json['parentId'] as num?)?.toInt(),
  entityType: json['entityType'] as String,
  entityId: (json['entityId'] as num).toInt(),
  createdAt: json['createdAt'] as String?,
  updatedAt: json['updatedAt'] as String?,
);

Map<String, dynamic> _$HierarchyToJson(Hierarchy instance) => <String, dynamic>{
  'id': instance.id,
  'parentId': instance.parentId,
  'entityType': instance.entityType,
  'entityId': instance.entityId,
  'createdAt': instance.createdAt,
  'updatedAt': instance.updatedAt,
};
