// ignore_for_file: use_build_context_synchronously, avoid_print, unused_element

# Подзадача: Подавить предупреждения `use_build_context_synchronously`, `avoid_print`, `unused_element` в `employee_detail_page.dart`

**Статус:** Pending

## Описание
В файле `frontend/lib/pages/employee/employee_detail_page.dart` добавить в начало файла директиву:
```dart
// ignore_for_file: use_build_context_synchronously, avoid_print, unused_element
```

## Критерии приёмки
* В файле `employee_detail_page.dart` не выводятся предупреждения `use_build_context_synchronously`, `avoid_print`, `unused_element` при запуске `flutter analyze`.
* Функциональность страницы работоспособна. 