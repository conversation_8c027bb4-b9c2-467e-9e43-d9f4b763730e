import 'package:json_annotation/json_annotation.dart';

part 'contract.g.dart';

@JsonSerializable()
class Contract {
  final int? id;
  final int employeeId;
  final String startDate;
  final String? endDate;
  final int? orderId;
  final String? createdAt;
  final String? updatedAt;

  Contract({
    this.id,
    required this.employeeId,
    required this.startDate,
    this.endDate,
    this.orderId,
    this.createdAt,
    this.updatedAt,
  });

  factory Contract.fromJson(Map<String, dynamic> json) => _$ContractFromJson(json);

  Map<String, dynamic> toJson() => _$ContractToJson(this);
}
