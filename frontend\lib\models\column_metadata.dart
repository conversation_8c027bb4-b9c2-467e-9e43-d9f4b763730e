/// Модель для хранения метаданных столбца таблицы
class ColumnMetadata {
  /// Название столбца
  final String name;

  /// Тип данных столбца
  final ColumnDataType dataType;

  /// Является ли столбец обязательным
  final bool isRequired;

  /// Является ли столбец первичным ключом
  final bool isPrimaryKey;

  /// Является ли столбец уникальным
  final bool isUnique;

  /// Значение по умолчанию
  final dynamic defaultValue;

  /// Минимальное значение (для числовых типов)
  final num? minValue;

  /// Максимальное значение (для числовых типов)
  final num? maxValue;

  /// Максимальная длина (для текстовых типов)
  final int? maxLength;

  /// Возможные значения (для перечислений)
  final List<String>? enumValues;

  ColumnMetadata({
    required this.name,
    required this.dataType,
    this.isRequired = false,
    this.isPrimaryKey = false,
    this.isUnique = false,
    this.defaultValue,
    this.minValue,
    this.maxValue,
    this.maxLength,
    this.enumValues,
  });

  /// Создать экземпляр из JSON
  factory ColumnMetadata.fromJson(Map<String, dynamic> json) {
    return ColumnMetadata(
      name: json['name'] as String,
      dataType: _parseDataType(json['dataType'] as String),
      isRequired: json['isRequired'] as bool? ?? false,
      isPrimaryKey: json['isPrimaryKey'] as bool? ?? false,
      isUnique: json['isUnique'] as bool? ?? false,
      defaultValue: json['defaultValue'],
      minValue: json['minValue'] != null ? num.parse(json['minValue'].toString()) : null,
      maxValue: json['maxValue'] != null ? num.parse(json['maxValue'].toString()) : null,
      maxLength: json['maxLength'] as int?,
      enumValues: json['enumValues'] != null
          ? List<String>.from(json['enumValues'] as List)
          : null,
    );
  }

  /// Преобразовать в JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'dataType': dataType.toString().split('.').last,
      'isRequired': isRequired,
      'isPrimaryKey': isPrimaryKey,
      'isUnique': isUnique,
      'defaultValue': defaultValue,
      'minValue': minValue,
      'maxValue': maxValue,
      'maxLength': maxLength,
      'enumValues': enumValues,
    };
  }

  /// Парсинг типа данных из строки
  static ColumnDataType _parseDataType(String dataType) {
    switch (dataType.toLowerCase()) {
      case 'integer':
        return ColumnDataType.integer;
      case 'real':
        return ColumnDataType.real;
      case 'text':
        return ColumnDataType.text;
      case 'blob':
        return ColumnDataType.blob;
      case 'date':
        return ColumnDataType.date;
      case 'time':
        return ColumnDataType.time;
      case 'datetime':
        return ColumnDataType.datetime;
      case 'boolean':
        return ColumnDataType.boolean;
      case 'enum':
        return ColumnDataType.enumType;
      default:
        return ColumnDataType.text;
    }
  }
}

/// Перечисление типов данных столбцов
enum ColumnDataType {
  /// Целое число
  integer,

  /// Число с плавающей точкой
  real,

  /// Текст
  text,

  /// Бинарные данные
  blob,

  /// Дата
  date,

  /// Время
  time,

  /// Дата и время
  datetime,

  /// Логическое значение
  boolean,

  /// Перечисление
  enumType,
}
