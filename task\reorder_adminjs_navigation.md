# Task: Переопределение порядка таблиц в навигации AdminJS

**Goal:** Изменить порядок отображения ресурсов (таблиц) в боковой панели навигации AdminJS на более логичный, с приоритетом для `Сотрудники` и `Должности`.

**Sub-tasks:**

1.  **[X] `task_1_create_main_task_file.md`**: Create this task tracking file.
    *   Status: Completed
2.  **[X] `task_2_reorder_resources_in_options_file.md`**: В файле `ok_admin/adminjs-resource-options.js`, в функции `getResourcesWithOptions`, изменить порядок следования объектов ресурсов в возвращаемом массиве в соответствии с определенным приоритетом.
    *   Status: Completed

**Desired Outcome:**
*   В навигационной панели AdminJS первыми отображаются "Сотрудники", затем "Должности".
*   Остальные таблицы следуют в логически обоснованном порядке.
*   Файл `ok_admin/adminjs-resource-options.js` отражает этот новый порядок. 