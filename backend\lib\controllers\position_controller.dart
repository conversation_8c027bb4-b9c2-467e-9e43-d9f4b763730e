import 'dart:convert';
import 'package:shelf/shelf.dart';
import 'package:backend/database/repositories/position_repository.dart';
import 'package:backend/models/position.dart';
import 'package:backend/utils/validator.dart';
import 'package:logging/logging.dart';

class PositionController {
  static final Logger _logger = Logger('PositionController');
  final PositionRepository _repository = PositionRepository();

  /// Получить все должности
  Response getAll(Request request) {
    _logger.info('Getting all positions');

    try {
      final positions = _repository.getAll();

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': positions.map((p) => p.toJson()).toList(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error getting all positions: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Получить должность по ID
  Response getById(Request request, String id) {
    _logger.info('Getting position by ID: $id');

    try {
      final positionId = int.parse(id);
      final position = _repository.getById(positionId);

      if (position == null) {
        return Response.notFound(
          jsonEncode({
            'success': false,
            'error': 'Not Found',
            'message': 'Position with ID $id not found',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': position.toJson(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error getting position by ID: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Создать новую должность
  Future<Response> create(Request request) async {
    _logger.info('Creating new position');

    try {
      final jsonBody = await request.readAsString();
      final Map<String, dynamic> data = jsonDecode(jsonBody);

      // Валидация данных
      final errors = Validator.validatePosition(data);
      if (errors.isNotEmpty) {
        return Response.badRequest(
          body: jsonEncode({
            'success': false,
            'error': 'Validation Error',
            'validationErrors': errors,
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      // Проверка обязательных полей
      final requiredFields = ['title'];
      if (!Validator.hasRequiredFields(data, requiredFields)) {
        return Response.badRequest(
          body: jsonEncode({
            'success': false,
            'error': 'Bad Request',
            'message': 'Required field missing: title',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final position = Position(
        title: data['title'],
        department: data['department'],
        unitName: data['unitName'],
        womenAllowed: data['womenAllowed'],
        militaryRankStaffId: data['militaryRankStaffId'],
        vus: data['vus'],
        vusPss: data['vusPss'],
        positionCodePss: data['positionCodePss'],
        tariffCategory: data['tariffCategory'],
        isFlightCrew: data['isFlightCrew'],
        antiCorruption: data['antiCorruption'],
        requiredEducationLevel: data['requiredEducationLevel'],
      );

      final id = _repository.create(position);

      return Response.ok(
        jsonEncode({
          'success': true,
          'data': {'id': id},
          'message': 'Position created successfully',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error creating position: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Обновить должность
  Future<Response> update(Request request, String id) async {
    _logger.info('Updating position with ID: $id');

    try {
      final positionId = int.parse(id);
      final existingPosition = _repository.getById(positionId);

      if (existingPosition == null) {
        return Response.notFound(
          jsonEncode({
            'success': false,
            'error': 'Not Found',
            'message': 'Position with ID $id not found',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final jsonBody = await request.readAsString();
      final Map<String, dynamic> data = jsonDecode(jsonBody);

      // Валидация данных
      final errors = Validator.validatePosition(data);
      if (errors.isNotEmpty) {
        return Response.badRequest(
          body: jsonEncode({
            'success': false,
            'error': 'Validation Error',
            'validationErrors': errors,
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final position = Position(
        id: positionId,
        title: data['title'] ?? existingPosition.title,
        department: data['department'] ?? existingPosition.department,
        unitName: data['unitName'] ?? existingPosition.unitName,
        womenAllowed: data['womenAllowed'] ?? existingPosition.womenAllowed,
        militaryRankStaffId: data['militaryRankStaffId'] ?? existingPosition.militaryRankStaffId,
        vus: data['vus'] ?? existingPosition.vus,
        vusPss: data['vusPss'] ?? existingPosition.vusPss,
        positionCodePss: data['positionCodePss'] ?? existingPosition.positionCodePss,
        tariffCategory: data['tariffCategory'] ?? existingPosition.tariffCategory,
        isFlightCrew: data['isFlightCrew'] ?? existingPosition.isFlightCrew,
        antiCorruption: data['antiCorruption'] ?? existingPosition.antiCorruption,
        requiredEducationLevel: data['requiredEducationLevel'] ?? existingPosition.requiredEducationLevel,
      );

      final success = _repository.update(position);

      if (!success) {
        return Response.internalServerError(
          body: jsonEncode({
            'success': false,
            'error': 'Internal Server Error',
            'message': 'Failed to update position',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'Position updated successfully',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error updating position: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  /// Удалить должность
  Response delete(Request request, String id) {
    _logger.info('Deleting position with ID: $id');

    try {
      final positionId = int.parse(id);
      final existingPosition = _repository.getById(positionId);

      if (existingPosition == null) {
        return Response.notFound(
          jsonEncode({
            'success': false,
            'error': 'Not Found',
            'message': 'Position with ID $id not found',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      final success = _repository.delete(positionId);

      if (!success) {
        return Response.internalServerError(
          body: jsonEncode({
            'success': false,
            'error': 'Internal Server Error',
            'message': 'Failed to delete position',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      return Response.ok(
        jsonEncode({
          'success': true,
          'message': 'Position deleted successfully',
        }),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e, stackTrace) {
      _logger.severe('Error deleting position: $e\n$stackTrace');

      return Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Internal Server Error',
          'message': e.toString(),
        }),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }
}
