import 'package:shelf_router/shelf_router.dart';
import 'package:backend/controllers/table_controller.dart';

/// Создает маршрутизатор для работы с таблицами
Router tableRoutes() {
  final controller = TableController();
  final router = Router();

  // Получить список таблиц
  router.get('/', controller.getTables);

  // Получить данные из таблицы
  router.get('/<tableName>', controller.getTableData);

  // Получить метаданные таблицы
  router.get('/<tableName>/metadata', controller.getTableMetadata);

  // Обновить строку в таблице
  router.put('/<tableName>/<id>', controller.updateTableRow);

  // Добавить строку в таблицу
  router.post('/<tableName>', controller.addTableRow);

  // Удалить строку из таблицы
  router.delete('/<tableName>/<id>', controller.deleteTableRow);

  return router;
}
