/// Модель записи послужного списка
class ServiceHistory {
  final int? id;
  final int employeeId;
  final int? positionId;
  final String? externalPositionTitle;
  final String startDate;
  final String? endDate;
  final String? acceptanceDate;
  final String? handoverDate;
  final int? orderId;
  final String? externalOrderInfo;
  final String? notes;
  final String? createdAt;
  final String? updatedAt;

  ServiceHistory({
    this.id,
    required this.employeeId,
    this.positionId,
    this.externalPositionTitle,
    required this.startDate,
    this.endDate,
    this.acceptanceDate,
    this.handoverDate,
    this.orderId,
    this.externalOrderInfo,
    this.notes,
    this.createdAt,
    this.updatedAt,
  });

  factory ServiceHistory.fromJson(Map<String, dynamic> json) {
    return ServiceHistory(
      id: json['id'],
      employeeId: json['employeeId'],
      positionId: json['positionId'],
      externalPositionTitle: json['externalPositionTitle'],
      startDate: json['startDate'],
      endDate: json['endDate'],
      acceptanceDate: json['acceptanceDate'],
      handoverDate: json['handoverDate'],
      orderId: json['orderId'],
      externalOrderInfo: json['externalOrderInfo'],
      notes: json['notes'],
      createdAt: json['createdAt'],
      updatedAt: json['updatedAt'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'employeeId': employeeId,
      if (positionId != null) 'positionId': positionId,
      if (externalPositionTitle != null) 'externalPositionTitle': externalPositionTitle,
      'startDate': startDate,
      if (endDate != null) 'endDate': endDate,
      if (acceptanceDate != null) 'acceptanceDate': acceptanceDate,
      if (handoverDate != null) 'handoverDate': handoverDate,
      if (orderId != null) 'orderId': orderId,
      if (externalOrderInfo != null) 'externalOrderInfo': externalOrderInfo,
      if (notes != null) 'notes': notes,
    };
  }
}
