# Task: Обновить миграцию для таблицы Employees

**Goal:** Изменить тип поля `veteran_since` на INTEGER и удалить поле `combat_participation` в файле миграции `ok_admin/migrations/20250509105025-create-employees.js`.

**Sub-tasks:**

1.  **[X] `task_1_create_main_task_file.md`**: Create this task tracking file.
    *   Status: Completed
2.  **[X] `task_2_modify_migration_up_method.md`**: В методе `up` файла миграции изменить тип `veteran_since` на `DataTypes.INTEGER` и удалить определение поля `combat_participation`.
    *   Status: Completed
3.  **[X] `task_3_modify_migration_down_method.md`**: В методе `down` файла миграции соответственно обновить операции для `veteran_since` (изменение типа обратно на `DataTypes.DATE`) и `combat_participation` (добавление поля обратно с типом `DataTypes.BOOLEAN`).
    *   Status: Completed

**Desired Outcome:**
*   Файл миграции `ok_admin/migrations/20250509105025-create-employees.js` корректно обновлен для отражения изменений в структуре таблицы `Employees`.
*   Метод `up` изменяет `veteran_since` на `INTEGER` и удаляет `combat_participation`.
*   Метод `down` корректно отменяет эти изменения (в данном случае, удаляет таблицу, так как это первоначальная миграция).
*   Файл задачи `task/task_update_employee_migration.md` отражает текущий статус выполнения. 