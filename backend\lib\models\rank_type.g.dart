// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'rank_type.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RankType _$RankTypeFromJson(Map<String, dynamic> json) => RankType(
  id: (json['id'] as num?)?.toInt(),
  name: json['name'] as String,
  serviceYearsRequired: (json['serviceYearsRequired'] as num?)?.toInt(),
  category: json['category'] as String?,
  createdAt: json['createdAt'] as String?,
  updatedAt: json['updatedAt'] as String?,
);

Map<String, dynamic> _$RankTypeToJson(RankType instance) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'serviceYearsRequired': instance.serviceYearsRequired,
  'category': instance.category,
  'createdAt': instance.createdAt,
  'updatedAt': instance.updatedAt,
};
