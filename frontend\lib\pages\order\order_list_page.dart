import 'package:flutter/material.dart';
import 'package:frontend/components/app_drawer.dart';
import 'package:frontend/models/order.dart';
import 'package:frontend/providers/order_provider.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

class OrderListPage extends StatefulWidget {
  const OrderListPage({super.key});

  @override
  State<OrderListPage> createState() => _OrderListPageState();
}

class _OrderListPageState extends State<OrderListPage> {
  @override
  void initState() {
    super.initState();
    // Загружаем данные при инициализации страницы
    Future.microtask(() {
      if (mounted) {
        Provider.of<OrderProvider>(context, listen: false).fetchOrders();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final orderProvider = Provider.of<OrderProvider>(context);

    return Scaffold(
      appBar: AppBar(title: const Text('Приказы')),
      drawer: const AppDrawer(),
      body:
          orderProvider.isLoading
              ? const Center(child: CircularProgressIndicator())
              : orderProvider.error != null
              ? Center(
                child: Text(
                  'Ошибка: ${orderProvider.error}',
                  style: const TextStyle(color: Colors.red),
                ),
              )
              : orderProvider.orders.isEmpty
              ? const Center(child: Text('Нет данных о приказах'))
              : ListView.builder(
                itemCount: orderProvider.orders.length,
                itemBuilder: (context, index) {
                  final order = orderProvider.orders[index];
                  return _buildOrderListItem(context, order);
                },
              ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          context.go('/orders/new');
        },
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        tooltip: 'Добавить новый приказ',
        child: const Icon(Icons.add, size: 28),
      ),
    );
  }

  Widget _buildOrderListItem(BuildContext context, Order order) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        title: Text(
          'Приказ №${order.number} от ${order.date}',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Издан: ${order.issuedBy}'),
            if (order.description != null && order.description!.isNotEmpty)
              Text(
                'Описание: ${order.description}',
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () {
                context.go('/orders/${order.id}/edit');
              },
            ),
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: () {
                _showDeleteConfirmationDialog(context, order);
              },
            ),
          ],
        ),
        onTap: () {
          context.go('/orders/${order.id}');
        },
      ),
    );
  }

  Future<void> _showDeleteConfirmationDialog(
    BuildContext context,
    Order order,
  ) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('Подтверждение удаления'),
          content: SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                Text(
                  'Вы уверены, что хотите удалить приказ №${order.number} от ${order.date}?',
                ),
                const Text('Это действие нельзя будет отменить.'),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Отмена'),
              onPressed: () {
                Navigator.of(dialogContext).pop();
              },
            ),
            TextButton(
              child: const Text('Удалить'),
              onPressed: () {
                Navigator.of(dialogContext).pop();
                _deleteOrder(order);
              },
            ),
          ],
        );
      },
    );
  }

  // Метод для удаления приказа
  Future<void> _deleteOrder(Order order) async {
    if (order.id != null) {
      // Сохраняем ссылку на провайдер перед асинхронной операцией
      final provider = Provider.of<OrderProvider>(context, listen: false);
      final success = await provider.deleteOrder(order.id!);

      // Проверяем, что виджет все еще в дереве виджетов
      if (!mounted) return;

      // Используем BuildContext только после проверки mounted
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Приказ успешно удален')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Ошибка при удалении приказа: ${provider.error}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
