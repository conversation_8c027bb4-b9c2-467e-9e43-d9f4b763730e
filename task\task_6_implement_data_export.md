# Подзадача: Реализация экспорта данных в CSV или Excel

## Описание
Необходимо реализовать функциональность экспорта данных из системы в форматы CSV или Excel для возможности дальнейшей обработки данных в других программах или для создания отчетов. Эта функциональность особенно важна для отдела кадров, где часто требуется формировать различные отчеты.

## Текущее состояние
- Отсутствует возможность экспорта данных из системы
- Данные доступны только через пользовательский интерфейс
- Нет возможности сохранить результаты фильтрации или поиска

## Необходимые изменения
1. Создать сервис для экспорта данных:
   - `frontend/lib/services/export_service.dart` - сервис для экспорта данных в различные форматы

2. Создать компонент для управления экспортом:
   - `frontend/lib/components/export_panel.dart` - компонент для выбора формата и параметров экспорта

3. Обновить страницы списков для добавления возможности экспорта:
   - `frontend/lib/pages/employee/employee_list_page.dart`
   - `frontend/lib/pages/position/position_list_page.dart`
   - `frontend/lib/pages/order/order_list_page.dart`
   - `frontend/lib/pages/service_history/service_history_list_page.dart`

4. Добавить зависимости для работы с CSV и Excel:
   - Добавить пакет `csv` для работы с CSV
   - Добавить пакет `excel` для работы с Excel

## Детали реализации
1. Сервис экспорта данных (`export_service.dart`):
   - Методы для конвертации данных в форматы CSV и Excel
   - Методы для сохранения файлов на устройстве пользователя
   - Поддержка различных кодировок (UTF-8, Windows-1251) для совместимости с Excel

2. Компонент управления экспортом (`export_panel.dart`):
   - Выбор формата экспорта (CSV, Excel)
   - Выбор столбцов для экспорта
   - Выбор кодировки
   - Кнопка для запуска экспорта

3. Обновление страниц списков:
   - Добавление кнопки экспорта в панель инструментов
   - Отображение компонента управления экспортом при нажатии на кнопку
   - Передача текущих данных (с учетом фильтров) в сервис экспорта

4. Форматы экспорта:
   - CSV:
     - Разделитель: запятая или точка с запятой (на выбор пользователя)
     - Кодировка: UTF-8 или Windows-1251 (на выбор пользователя)
     - Заголовки столбцов: включены
   - Excel:
     - Формат: XLSX
     - Заголовки столбцов: включены
     - Автоматическая настройка ширины столбцов
     - Базовое форматирование (заголовки жирным шрифтом)

## Экспортируемые данные для каждой сущности
1. Сотрудники:
   - ФИО
   - Пол
   - Дата рождения
   - Место рождения
   - Национальность
   - Личный номер
   - Другие поля по выбору пользователя

2. Должности:
   - Название
   - Отдел
   - Подразделение
   - Доступность для женщин
   - Другие поля по выбору пользователя

3. Приказы:
   - Номер
   - Тип
   - Дата
   - Статус
   - Другие поля по выбору пользователя

4. Послужной список:
   - Сотрудник
   - Должность
   - Дата начала
   - Дата окончания
   - Приказ
   - Другие поля по выбору пользователя

## Ожидаемый результат
- Возможность экспорта данных из системы в форматы CSV и Excel
- Удобный пользовательский интерфейс для управления экспортом
- Поддержка различных кодировок для совместимости с Excel
- Возможность выбора столбцов для экспорта
- Экспорт данных с учетом примененных фильтров
