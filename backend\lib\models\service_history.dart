import 'package:json_annotation/json_annotation.dart';

part 'service_history.g.dart';

@JsonSerializable()
class ServiceHistory {
  final int? id;
  final int employeeId;
  final int? positionId;
  final String? externalPositionTitle;
  final String startDate;
  final String? endDate;
  final String? acceptanceDate;
  final String? handoverDate;
  final int? orderId;
  final String? externalOrderInfo;
  final String? notes;
  final String? createdAt;
  final String? updatedAt;

  ServiceHistory({
    this.id,
    required this.employeeId,
    this.positionId,
    this.externalPositionTitle,
    required this.startDate,
    this.endDate,
    this.acceptanceDate,
    this.handoverDate,
    this.orderId,
    this.externalOrderInfo,
    this.notes,
    this.createdAt,
    this.updatedAt,
  });

  factory ServiceHistory.fromJson(Map<String, dynamic> json) => _$ServiceHistoryFromJson(json);

  Map<String, dynamic> toJson() => _$ServiceHistoryToJson(this);
}
