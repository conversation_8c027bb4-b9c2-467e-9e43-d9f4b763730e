import 'package:flutter/foundation.dart';
import 'package:frontend/models/employee.dart';
import 'package:frontend/services/api_service.dart';

class EmployeeProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();
  
  List<Employee> _employees = [];
  bool _isLoading = false;
  String? _error;
  
  List<Employee> get employees => _employees;
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  Future<void> fetchEmployees() async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      _employees = await _apiService.getEmployees();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }
  
  Future<Employee?> getEmployee(int id) async {
    try {
      return await _apiService.getEmployee(id);
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }
  
  Future<bool> createEmployee(Employee employee) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      final id = await _apiService.createEmployee(employee);
      final newEmployee = employee.copyWith(id: id);
      _employees.add(newEmployee);
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }
  
  Future<bool> updateEmployee(Employee employee) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      await _apiService.updateEmployee(employee);
      
      final index = _employees.indexWhere((e) => e.id == employee.id);
      if (index != -1) {
        _employees[index] = employee;
      }
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }
  
  Future<bool> deleteEmployee(int id) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      await _apiService.deleteEmployee(id);
      
      _employees.removeWhere((e) => e.id == id);
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }
}
