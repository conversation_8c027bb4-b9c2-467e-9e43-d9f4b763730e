# Task: Добавить поля даты приёма и сдачи дел в ServiceHistory

**Goal:** Добавить поля `acceptance_date` и `handover_date` в таблицу `service_history` и соответствующим образом обновить схему БД, миграцию и конфигурацию AdminJS.

**Sub-tasks:**

1.  **[X] `task_1_create_main_task_file.md`**: Create this task tracking file.
    *   Status: Completed
2.  **[X] `task_2_update_db_schema_service_history.md`**: Добавить поля `acceptance_date DATE` и `handover_date DATE` в определение таблицы `service_history` в файле `db_schema.md`.
    *   Status: Completed
3.  **[X] `task_3_update_migration_service_history.md`**: Добавить поля `acceptance_date: Sequelize.DATE` и `handover_date: Sequelize.DATE` в метод `up` файла миграции `ok_admin/migrations/20250509105657-create-service-history.js`. Обновить метод `down` для корректного отката.
    *   Status: Completed
4.  **[X] `task_4_update_adminjs_options_service_history.md`**: Добавить поля `acceptance_date` и `handover_date` в `properties`, `listProperties`, `showProperties`, `editProperties` и `filterProperties` для `serviceHistoryResourceOptions` в файле `ok_admin/adminjs-resource-options.js`.
    *   Status: Completed

**Desired Outcome:**
*   Таблица `service_history` в базе данных содержит новые поля `acceptance_date` и `handover_date` типа DATE.
*   Файл `db_schema.md` отражает эти изменения.
*   Файл миграции `ok_admin/migrations/20250509105657-create-service-history.js` корректно создает и удаляет эти поля.
*   Интерфейс AdminJS для `ServiceHistory` позволяет просматривать и редактировать новые поля.
*   Файл задачи `task/task_add_acceptance_handover_dates_to_service_history.md` отражает текущий статус выполнения. 