// Flutter Web Offline Configuration
// This file configures Flutter Web to work completely offline

(function() {
  'use strict';

  // Override the default CanvasKit loader to use local files
  window._flutter = window._flutter || {};
  
  // Configure Flutter to use local CanvasKit
  window._flutter.loader = window._flutter.loader || {};
  
  // Set up offline configuration
  const offlineConfig = {
    // Use local CanvasKit files
    canvasKitBaseUrl: '/canvaskit/',
    canvasKitVariant: 'auto',
    
    // Disable external resource loading
    serviceWorkerSettings: {
      allResourcesCachedByDefault: true,
      skipWaiting: true,
      clientsClaim: true
    },
    
    // Force local resource usage
    assetBase: '/',
    
    // Disable CDN fallbacks
    disableCdnFallback: true
  };

  // Apply configuration
  Object.assign(window._flutter, offlineConfig);
  
  // Override fetch to prevent external requests for CanvasKit
  const originalFetch = window.fetch;
  window.fetch = function(resource, init) {
    const url = typeof resource === 'string' ? resource : resource.url;
    
    // Block external CanvasKit requests and redirect to local
    if (url.includes('unpkg.com') && url.includes('canvaskit')) {
      console.log('Redirecting external CanvasKit request to local:', url);
      const localPath = url.split('/').pop();
      return originalFetch('/canvaskit/' + localPath, init);
    }
    
    // Block other external CDN requests
    if (url.includes('unpkg.com') || url.includes('cdn.jsdelivr.net') || url.includes('gstatic.com')) {
      console.warn('Blocked external CDN request:', url);
      return Promise.reject(new Error('External requests blocked for offline mode'));
    }
    
    return originalFetch(resource, init);
  };

  console.log('Flutter offline configuration loaded');
})();
