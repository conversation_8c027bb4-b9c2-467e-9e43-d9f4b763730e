# Task: Обновить поля Veteran Since и Combat Participation в модели Employee

**Goal:** Изменить тип поля `veteran_since` на числовой (год) и удалить избыточное поле `combat_participation` из конфигурации модели `Employee` в AdminJS.

**Sub-tasks:**

1.  **[X] `task_1_create_main_task_file.md`**: Create this task tracking file.
    *   Status: Completed
2.  **[X] `task_2_modify_veteran_since_field.md`**: Изменить тип поля `veteran_since` с 'date' на 'number' в `employeeResourceOptions` в файле `ok_admin/adminjs-resource-options.js`.
    *   Status: Completed
3.  **[X] `task_3_remove_combat_participation_field.md`**: Удалить поле `combat_participation` из `properties`, `listProperties`, `showProperties`, `editProperties` и `filterProperties` в `employeeResourceOptions` в файле `ok_admin/adminjs-resource-options.js`.
    *   Status: Completed

**Desired Outcome:**
*   Поле `veteran_since` в интерфейсе AdminJS для модели `Employee` принимает и отображает только год.
*   Поле `combat_participation` полностью удалено из интерфейса AdminJS для модели `Employee`.
*   Файл `ok_admin/adminjs-resource-options.js` обновлен с учетом этих изменений.
*   Файл задачи `task/task_update_employee_fields.md` отражает текущий статус выполнения. 