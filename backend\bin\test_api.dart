import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:logging/logging.dart';

/// Тестовый скрипт для проверки API
void main() async {
  // Настройка логгера
  Logger.root.level = Level.ALL;
  Logger.root.onRecord.listen((record) {
    print('${record.level.name}: ${record.time}: ${record.message}');
  });

  final logger = Logger('ApiTest');

  // Базовый URL API
  const baseUrl = 'http://localhost:8080/api';

  // Переменные для хранения ID созданных объектов
  int? employeeId;
  int? positionId;
  int? rankTypeId;
  int? militaryRankId;
  int? orderId;
  int? serviceHistoryId;

  try {
    logger.info('Запуск тестирования API...');

    // Проверка доступности API
    logger.info('Проверка доступности API...');
    final healthResponse =
        await http.get(Uri.parse('http://localhost:8080/health'));
    if (healthResponse.statusCode != 200) {
      logger.severe('API недоступен. Убедитесь, что сервер запущен.');
      exit(1);
    }
    logger.info('API доступен. Статус: ${healthResponse.body}');

    // Тесты для типов званий (RankType)
    logger.info('Тестирование API для типов званий...');

    // Создание типа звания
    final rankTypeData = {
      'name': 'Тестовый тип звания',
      'serviceYearsRequired': 5,
      'category': 'Тестовая категория'
    };

    final createRankTypeResponse = await http.post(
      Uri.parse('$baseUrl/rank-types'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode(rankTypeData),
    );

    if (createRankTypeResponse.statusCode != 200) {
      logger.severe(
          'Ошибка при создании типа звания: ${createRankTypeResponse.body}');
    } else {
      final createRankTypeResult = jsonDecode(createRankTypeResponse.body);
      rankTypeId = createRankTypeResult['data']['id'];
      logger.info('Тип звания успешно создан. ID: $rankTypeId');
    }

    // Получение типа звания по ID
    if (rankTypeId != null) {
      final getRankTypeResponse =
          await http.get(Uri.parse('$baseUrl/rank-types/$rankTypeId'));

      if (getRankTypeResponse.statusCode != 200) {
        logger.severe(
            'Ошибка при получении типа звания: ${getRankTypeResponse.body}');
      } else {
        final getRankTypeResult = jsonDecode(getRankTypeResponse.body);
        logger.info(
            'Тип звания успешно получен: ${getRankTypeResult['data']['name']}');
      }
    }

    // Тесты для воинских званий (MilitaryRank)
    logger.info('Тестирование API для воинских званий...');

    // Создание воинского звания
    if (rankTypeId != null) {
      final militaryRankData = {
        'employeeId': 1, // Тестовый ID сотрудника
        'rankTypeId': rankTypeId,
        'dateAssigned': '2023-01-01'
      };

      final createMilitaryRankResponse = await http.post(
        Uri.parse('$baseUrl/military-ranks'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(militaryRankData),
      );

      if (createMilitaryRankResponse.statusCode != 200) {
        logger.severe(
            'Ошибка при создании воинского звания: ${createMilitaryRankResponse.body}');
      } else {
        final createMilitaryRankResult =
            jsonDecode(createMilitaryRankResponse.body);
        militaryRankId = createMilitaryRankResult['data']['id'];
        logger.info('Воинское звание успешно создано. ID: $militaryRankId');
      }
    }

    // Тесты для должностей (Position)
    logger.info('Тестирование API для должностей...');

    // Создание должности
    final positionData = {
      'title': 'Тестовая должность',
      'department': 'Тестовый отдел',
      'unitName': 'Тестовое подразделение',
      'womenAllowed': 1,
      'militaryRankStaffId': militaryRankId,
      'requiredEducationLevel': 3
    };

    final createPositionResponse = await http.post(
      Uri.parse('$baseUrl/positions'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode(positionData),
    );

    if (createPositionResponse.statusCode != 200) {
      logger.severe(
          'Ошибка при создании должности: ${createPositionResponse.body}');
    } else {
      final createPositionResult = jsonDecode(createPositionResponse.body);
      positionId = createPositionResult['data']['id'];
      logger.info('Должность успешно создана. ID: $positionId');
    }

    // Получение должности по ID
    if (positionId != null) {
      final getPositionResponse =
          await http.get(Uri.parse('$baseUrl/positions/$positionId'));

      if (getPositionResponse.statusCode != 200) {
        logger.severe(
            'Ошибка при получении должности: ${getPositionResponse.body}');
      } else {
        final getPositionResult = jsonDecode(getPositionResponse.body);
        logger.info(
            'Должность успешно получена: ${getPositionResult['data']['title']}');
      }
    }

    // Тесты для сотрудников (Employee)
    logger.info('Тестирование API для сотрудников...');

    // Создание сотрудника
    final employeeData = {
      'lastName': 'Тестов',
      'firstName': 'Тест',
      'middleName': 'Тестович',
      'gender': 1,
      'birthDate': '1990-01-01',
      'placeOfBirth': 'г. Тестовск',
      'nationality': 'Тестовец',
      'personalNumber': 'TEST-${DateTime.now().millisecondsSinceEpoch}',
      'childrenUnder16': 1,
      'academicDegree': 'Тестовая степень',
      'veteranSince': 2020
    };

    final createEmployeeResponse = await http.post(
      Uri.parse('$baseUrl/employees'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode(employeeData),
    );

    if (createEmployeeResponse.statusCode != 200) {
      logger.severe(
          'Ошибка при создании сотрудника: ${createEmployeeResponse.body}');
    } else {
      final createEmployeeResult = jsonDecode(createEmployeeResponse.body);
      employeeId = createEmployeeResult['data']['id'];
      logger.info('Сотрудник успешно создан. ID: $employeeId');
    }

    // Получение сотрудника по ID
    if (employeeId != null) {
      final getEmployeeResponse =
          await http.get(Uri.parse('$baseUrl/employees/$employeeId'));

      if (getEmployeeResponse.statusCode != 200) {
        logger.severe(
            'Ошибка при получении сотрудника: ${getEmployeeResponse.body}');
      } else {
        final getEmployeeResult = jsonDecode(getEmployeeResponse.body);
        logger.info(
            'Сотрудник успешно получен: ${getEmployeeResult['data']['lastName']} ${getEmployeeResult['data']['firstName']}');
      }
    }

    // Тесты для приказов (Order)
    logger.info('Тестирование API для приказов...');

    // Создание приказа
    final orderData = {
      'number': 'TEST-${DateTime.now().millisecondsSinceEpoch}',
      'date': '2023-01-01',
      'issuedBy': 'Тестовый руководитель',
      'description': 'Описание тестового приказа'
    };

    final createOrderResponse = await http.post(
      Uri.parse('$baseUrl/orders'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode(orderData),
    );

    if (createOrderResponse.statusCode != 200) {
      logger.severe('Ошибка при создании приказа: ${createOrderResponse.body}');
    } else {
      final createOrderResult = jsonDecode(createOrderResponse.body);
      orderId = createOrderResult['data']['id'];
      logger.info('Приказ успешно создан. ID: $orderId');
    }

    // Тесты для специфических операций
    if (employeeId != null && positionId != null) {
      logger.info('Тестирование специфических операций...');

      // Перемещение сотрудника на должность
      final moveData = {
        'positionId': positionId,
        'startDate': '2023-01-01',
        'acceptanceDate': '2023-01-02',
        'orderId': orderId,
        'notes': 'Тестовое перемещение'
      };

      final moveEmployeeResponse = await http.post(
        Uri.parse('$baseUrl/employees/$employeeId/move'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(moveData),
      );

      if (moveEmployeeResponse.statusCode != 200) {
        logger.severe(
            'Ошибка при перемещении сотрудника: ${moveEmployeeResponse.body}');
      } else {
        final moveEmployeeResult = jsonDecode(moveEmployeeResponse.body);
        serviceHistoryId = moveEmployeeResult['data']['serviceHistoryId'];
        logger.info(
            'Сотрудник успешно перемещен на должность. ID записи в послужном списке: $serviceHistoryId');
      }

      // Получение послужного списка сотрудника
      final getServiceHistoryResponse = await http
          .get(Uri.parse('$baseUrl/service-history/employee/$employeeId'));

      if (getServiceHistoryResponse.statusCode != 200) {
        logger.severe(
            'Ошибка при получении послужного списка: ${getServiceHistoryResponse.body}');
      } else {
        final getServiceHistoryResult =
            jsonDecode(getServiceHistoryResponse.body);
        final records = getServiceHistoryResult['data'];
        logger.info(
            'Послужной список успешно получен. Количество записей: ${records.length}');
      }
    }

    // Очистка тестовых данных
    logger.info('Очистка тестовых данных...');

    // Удаление записи в послужном списке
    if (serviceHistoryId != null) {
      final deleteServiceHistoryResponse = await http
          .delete(Uri.parse('$baseUrl/service-history/$serviceHistoryId'));

      if (deleteServiceHistoryResponse.statusCode != 200) {
        logger.severe(
            'Ошибка при удалении записи в послужном списке: ${deleteServiceHistoryResponse.body}');
      } else {
        logger.info('Запись в послужном списке успешно удалена');
      }
    }

    // Удаление сотрудника
    if (employeeId != null) {
      final deleteEmployeeResponse =
          await http.delete(Uri.parse('$baseUrl/employees/$employeeId'));

      if (deleteEmployeeResponse.statusCode != 200) {
        logger.severe(
            'Ошибка при удалении сотрудника: ${deleteEmployeeResponse.body}');
      } else {
        logger.info('Сотрудник успешно удален');
      }
    }

    // Удаление должности
    if (positionId != null) {
      final deletePositionResponse =
          await http.delete(Uri.parse('$baseUrl/positions/$positionId'));

      if (deletePositionResponse.statusCode != 200) {
        logger.severe(
            'Ошибка при удалении должности: ${deletePositionResponse.body}');
      } else {
        logger.info('Должность успешно удалена');
      }
    }

    // Удаление приказа
    if (orderId != null) {
      final deleteOrderResponse =
          await http.delete(Uri.parse('$baseUrl/orders/$orderId'));

      if (deleteOrderResponse.statusCode != 200) {
        logger
            .severe('Ошибка при удалении приказа: ${deleteOrderResponse.body}');
      } else {
        logger.info('Приказ успешно удален');
      }
    }

    // Удаление воинского звания
    if (militaryRankId != null) {
      final deleteMilitaryRankResponse = await http
          .delete(Uri.parse('$baseUrl/military-ranks/$militaryRankId'));

      if (deleteMilitaryRankResponse.statusCode != 200) {
        logger.severe(
            'Ошибка при удалении воинского звания: ${deleteMilitaryRankResponse.body}');
      } else {
        logger.info('Воинское звание успешно удалено');
      }
    }

    // Удаление типа звания
    if (rankTypeId != null) {
      final deleteRankTypeResponse =
          await http.delete(Uri.parse('$baseUrl/rank-types/$rankTypeId'));

      if (deleteRankTypeResponse.statusCode != 200) {
        logger.severe(
            'Ошибка при удалении типа звания: ${deleteRankTypeResponse.body}');
      } else {
        logger.info('Тип звания успешно удален');
      }
    }

    logger.info('Тестирование API завершено');
  } catch (e, stackTrace) {
    logger.severe('Ошибка при тестировании API: $e\n$stackTrace');
  }
}
