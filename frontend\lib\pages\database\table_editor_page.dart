import 'package:flutter/material.dart';
import 'package:frontend/components/app_drawer.dart';
import 'package:frontend/components/data_table_editor.dart';
import 'package:frontend/providers/table_provider.dart';
import 'package:provider/provider.dart';

class TableEditorPage extends StatefulWidget {
  final String tableName;

  const TableEditorPage({
    super.key,
    required this.tableName,
  });

  @override
  State<TableEditorPage> createState() => _TableEditorPageState();
}

class _TableEditorPageState extends State<TableEditorPage> {
  @override
  void initState() {
    super.initState();
    // Загружаем данные таблицы при инициализации страницы
    Future.microtask(() {
      if (mounted) {
        Provider.of<TableProvider>(context, listen: false).selectTable(widget.tableName);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Таблица: ${widget.tableName}'),
      ),
      drawer: const AppDrawer(),
      body: Consumer<TableProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (provider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Ошибка: ${provider.error}',
                    style: const TextStyle(color: Colors.red),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => provider.selectTable(widget.tableName),
                    child: const Text('Повторить'),
                  ),
                ],
              ),
            );
          }

          if (provider.tableData.isEmpty) {
            return const Center(
              child: Text('Нет данных в таблице'),
            );
          }

          final columns = List<String>.from(provider.tableData['columns'] ?? []);
          final rows = List<Map<String, dynamic>>.from(
            (provider.tableData['rows'] as List? ?? []).map((row) => Map<String, dynamic>.from(row)),
          );

          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: DataTableEditor(
              columns: columns,
              rows: rows,
              columnsMetadata: provider.columnsMetadata,
              onUpdateRow: (id, rowData) => provider.updateTableRow(id, rowData),
              onAddRow: (rowData) => provider.addTableRow(rowData),
              onDeleteRow: (id) => provider.deleteTableRow(id),
            ),
          );
        },
      ),
    );
  }
}
