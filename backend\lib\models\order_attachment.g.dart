// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_attachment.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrderAttachment _$OrderAttachmentFromJson(
  Map<String, dynamic> json,
) => OrderAttachment(
  id: (json['id'] as num?)?.toInt(),
  orderId: (json['orderId'] as num).toInt(),
  fileName: json['fileName'] as String,
  data: (json['data'] as List<dynamic>).map((e) => (e as num).toInt()).toList(),
  uploadedAt: json['uploadedAt'] as String?,
  createdAt: json['createdAt'] as String?,
  updatedAt: json['updatedAt'] as String?,
);

Map<String, dynamic> _$OrderAttachmentToJson(OrderAttachment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'orderId': instance.orderId,
      'fileName': instance.fileName,
      'data': instance.data,
      'uploadedAt': instance.uploadedAt,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
    };
