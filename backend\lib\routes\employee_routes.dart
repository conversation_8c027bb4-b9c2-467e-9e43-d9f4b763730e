import 'package:shelf_router/shelf_router.dart';
import 'package:backend/controllers/employee_controller.dart';
import 'package:backend/controllers/employee_operations_controller.dart';

/// Создает маршрутизатор для работы с сотрудниками
Router employeeRoutes() {
  final controller = EmployeeController();
  final operationsController = EmployeeOperationsController();
  final router = Router();

  // Получить всех сотрудников
  router.get('/', controller.getAll);

  // Получить сотрудника по ID
  router.get('/<id>', controller.getById);

  // Создать нового сотрудника
  router.post('/', controller.create);

  // Обновить сотрудника
  router.put('/<id>', controller.update);

  // Удалить сотрудника
  router.delete('/<id>', controller.delete);

  // Специфические операции с сотрудниками

  // Перемещение сотрудника на новую должность
  router.post('/<id>/move', operationsController.moveToPosition);

  // Присвоение воинского звания сотруднику
  router.post('/<id>/assign-rank', operationsController.assignMilitaryRank);

  return router;
}
