// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'service_history.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ServiceHistory _$ServiceHistoryFromJson(Map<String, dynamic> json) =>
    ServiceHistory(
      id: (json['id'] as num?)?.toInt(),
      employeeId: (json['employeeId'] as num).toInt(),
      positionId: (json['positionId'] as num?)?.toInt(),
      externalPositionTitle: json['externalPositionTitle'] as String?,
      startDate: json['startDate'] as String,
      endDate: json['endDate'] as String?,
      acceptanceDate: json['acceptanceDate'] as String?,
      handoverDate: json['handoverDate'] as String?,
      orderId: (json['orderId'] as num?)?.toInt(),
      externalOrderInfo: json['externalOrderInfo'] as String?,
      notes: json['notes'] as String?,
      createdAt: json['createdAt'] as String?,
      updatedAt: json['updatedAt'] as String?,
    );

Map<String, dynamic> _$ServiceHistoryToJson(ServiceHistory instance) =>
    <String, dynamic>{
      'id': instance.id,
      'employeeId': instance.employeeId,
      'positionId': instance.positionId,
      'externalPositionTitle': instance.externalPositionTitle,
      'startDate': instance.startDate,
      'endDate': instance.endDate,
      'acceptanceDate': instance.acceptanceDate,
      'handoverDate': instance.handoverDate,
      'orderId': instance.orderId,
      'externalOrderInfo': instance.externalOrderInfo,
      'notes': instance.notes,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
    };
