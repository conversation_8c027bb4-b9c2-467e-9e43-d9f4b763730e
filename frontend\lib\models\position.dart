class Position {
  final int? id;
  final String title;
  final String? department;
  final String? unitName;
  final int? womenAllowed;
  final int? militaryRankStaffId;
  final String? vus;
  final String? vusPss;
  final String? positionCodePss;
  final String? tariffCategory;
  final int? isFlightCrew;
  final int? antiCorruption;
  final int? requiredEducationLevel;
  final String? createdAt;
  final String? updatedAt;

  Position({
    this.id,
    required this.title,
    this.department,
    this.unitName,
    this.womenAllowed,
    this.militaryRankStaffId,
    this.vus,
    this.vusPss,
    this.positionCodePss,
    this.tariffCategory,
    this.isFlightCrew,
    this.antiCorruption,
    this.requiredEducationLevel,
    this.createdAt,
    this.updatedAt,
  });

  factory Position.fromJson(Map<String, dynamic> json) {
    return Position(
      id: json['id'],
      title: json['title'],
      department: json['department'],
      unitName: json['unitName'],
      womenAllowed: json['womenAllowed'],
      militaryRankStaffId: json['militaryRankStaffId'],
      vus: json['vus'],
      vusPss: json['vusPss'],
      positionCodePss: json['positionCodePss'],
      tariffCategory: json['tariffCategory'],
      isFlightCrew: json['isFlightCrew'],
      antiCorruption: json['antiCorruption'],
      requiredEducationLevel: json['requiredEducationLevel'],
      createdAt: json['createdAt'],
      updatedAt: json['updatedAt'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'department': department,
      'unitName': unitName,
      'womenAllowed': womenAllowed,
      'militaryRankStaffId': militaryRankStaffId,
      'vus': vus,
      'vusPss': vusPss,
      'positionCodePss': positionCodePss,
      'tariffCategory': tariffCategory,
      'isFlightCrew': isFlightCrew,
      'antiCorruption': antiCorruption,
      'requiredEducationLevel': requiredEducationLevel,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  String get fullTitle {
    if (department != null && department!.isNotEmpty) {
      return '$title ($department)';
    }
    return title;
  }

  /// Возвращает текстовое представление требуемого уровня образования
  String get requiredEducationLevelText {
    switch (requiredEducationLevel) {
      case 0:
        return 'Не требуется';
      case 1:
        return 'Основное общее образование';
      case 2:
        return 'Среднее общее образование';
      case 3:
        return 'Среднее профессиональное образование';
      case 4:
        return 'Высшее образование';
      default:
        return 'Не указано';
    }
  }

  Position copyWith({
    int? id,
    String? title,
    String? department,
    String? unitName,
    int? womenAllowed,
    int? militaryRankStaffId,
    String? vus,
    String? vusPss,
    String? positionCodePss,
    String? tariffCategory,
    int? isFlightCrew,
    int? antiCorruption,
    int? requiredEducationLevel,
    String? createdAt,
    String? updatedAt,
  }) {
    return Position(
      id: id ?? this.id,
      title: title ?? this.title,
      department: department ?? this.department,
      unitName: unitName ?? this.unitName,
      womenAllowed: womenAllowed ?? this.womenAllowed,
      militaryRankStaffId: militaryRankStaffId ?? this.militaryRankStaffId,
      vus: vus ?? this.vus,
      vusPss: vusPss ?? this.vusPss,
      positionCodePss: positionCodePss ?? this.positionCodePss,
      tariffCategory: tariffCategory ?? this.tariffCategory,
      isFlightCrew: isFlightCrew ?? this.isFlightCrew,
      antiCorruption: antiCorruption ?? this.antiCorruption,
      requiredEducationLevel: requiredEducationLevel ?? this.requiredEducationLevel,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
