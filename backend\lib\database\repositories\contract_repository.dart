import 'package:backend/database/database.dart';
import 'package:backend/models/contract.dart';
import 'package:logging/logging.dart';

class ContractRepository {
  static final Logger _logger = Logger('ContractRepository');

  /// Получить все контракты
  List<Contract> getAll() {
    _logger.info('Getting all contracts');

    final result = AppDatabase.select('SELECT * FROM contracts');

    return result.map((row) {
      return Contract(
        id: row['id'] as int,
        employeeId: row['employee_id'] as int,
        startDate: row['start_date'] as String,
        endDate: row['end_date'] as String?,
        orderId: row['order_id'] as int?,
        createdAt: row['created_at'] as String?,
        updatedAt: row['updated_at'] as String?,
      );
    }).toList();
  }

  /// Получить контракт по ID
  Contract? getById(int id) {
    _logger.info('Getting contract by ID: $id');

    final result = AppDatabase.select('SELECT * FROM contracts WHERE id = ?', [
      id,
    ]);

    if (result.isEmpty) {
      return null;
    }

    final row = result.first;

    return Contract(
      id: row['id'] as int,
      employeeId: row['employee_id'] as int,
      startDate: row['start_date'] as String,
      endDate: row['end_date'] as String?,
      orderId: row['order_id'] as int?,
      createdAt: row['created_at'] as String?,
      updatedAt: row['updated_at'] as String?,
    );
  }

  /// Получить контракты сотрудника
  List<Contract> getByEmployeeId(int employeeId) {
    _logger.info('Getting contracts for employee ID: $employeeId');

    final result = AppDatabase.select('SELECT * FROM contracts WHERE employee_id = ?', [
      employeeId,
    ]);

    return result.map((row) {
      return Contract(
        id: row['id'] as int,
        employeeId: row['employee_id'] as int,
        startDate: row['start_date'] as String,
        endDate: row['end_date'] as String?,
        orderId: row['order_id'] as int?,
        createdAt: row['created_at'] as String?,
        updatedAt: row['updated_at'] as String?,
      );
    }).toList();
  }

  /// Создать новый контракт
  int create(Contract contract) {
    _logger.info('Creating new contract: $contract');

    AppDatabase.execute(
      '''
      INSERT INTO contracts (
        employee_id, start_date, end_date, order_id
      ) VALUES (?, ?, ?, ?)
      ''',
      [
        contract.employeeId,
        contract.startDate,
        contract.endDate,
        contract.orderId,
      ],
    );

    return AppDatabase.database.lastInsertRowId;
  }

  /// Обновить контракт
  bool update(Contract contract) {
    _logger.info('Updating contract: $contract');

    if (contract.id == null) {
      throw ArgumentError('Contract ID cannot be null');
    }

    AppDatabase.execute(
      '''
      UPDATE contracts SET
        employee_id = ?, start_date = ?, end_date = ?, order_id = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
      ''',
      [
        contract.employeeId,
        contract.startDate,
        contract.endDate,
        contract.orderId,
        contract.id,
      ],
    );

    return AppDatabase.database.updatedRows > 0;
  }

  /// Удалить контракт
  bool delete(int id) {
    _logger.info('Deleting contract with ID: $id');

    AppDatabase.execute('DELETE FROM contracts WHERE id = ?', [id]);

    return AppDatabase.database.updatedRows > 0;
  }
}
