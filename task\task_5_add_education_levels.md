# Task: Добавление полей для уровней образования

**Goal:** Добавить поле `required_education_level` в таблицу `positions` и изменить тип поля `education_type` в таблице `education_details` для хранения информации об уровнях образования.

**Steps:**

1. ✅ Создать миграцию для добавления поля `required_education_level` в таблицу `positions`:
   - ✅ Создать файл `migrations/18_add_required_education_level_to_positions.sql`
   - ✅ Добавить SQL-запрос для добавления поля типа INTEGER с значением по умолчанию 0

2. ✅ Создать миграцию для обновления поля `education_type` в таблице `education_details`:
   - ✅ Создать файл `migrations/19_update_education_type_in_education_details.sql`
   - ✅ Добавить SQL-запрос для установки значения по умолчанию для NULL значений

3. ✅ Обновить модель `Position`:
   - ✅ Добавить поле `requiredEducationLevel` типа int?
   - ✅ Обновить конструктор для инициализации нового поля
   - ✅ Добавить геттер `requiredEducationLevelText` для получения текстового представления уровня образования

4. ✅ Обновить модель `EducationDetail`:
   - ✅ Изменить тип поля `educationType` с String? на int?
   - ✅ Обновить методы `fromJson` и `toJson` для корректной сериализации/десериализации
   - ✅ Добавить геттер `educationTypeText` для получения текстового представления типа образования

5. ✅ Обновить репозиторий `PositionRepository`:
   - ✅ Обновить методы `getAll` и `getById` для работы с новым полем
   - ✅ Обновить методы `create` и `update` для сохранения нового поля

6. ✅ Создать репозиторий `EducationDetailRepository`:
   - ✅ Реализовать методы `getAll`, `getById`, `getByEmployeeId`, `create`, `update` и `delete`
   - ✅ Добавить логику для преобразования строкового значения `education_type` в int

7. ✅ Создать тестовый скрипт для проверки внесенных изменений:
   - ✅ Создать файл `bin/test_education_levels.dart`
   - ✅ Реализовать создание, получение, обновление и удаление должностей и записей об образовании
   - ✅ Проверить работу с новыми полями и их текстовыми представлениями

**Result:**
- ✅ Добавлено поле `required_education_level` в таблицу `positions`
- ✅ Изменен тип поля `education_type` в таблице `education_details`
- ✅ Обновлены модели данных и репозитории для работы с новыми полями
- ✅ Создан тестовый скрипт для проверки внесенных изменений

**Status:** Completed
