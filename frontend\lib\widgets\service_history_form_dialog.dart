import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:frontend/models/service_history.dart';
import 'package:frontend/models/position.dart';
import 'package:intl/intl.dart';

class ServiceHistoryFormDialog extends StatefulWidget {
  final Position position;

  const ServiceHistoryFormDialog({Key? key, required this.position})
      : super(key: key);

  @override
  State<ServiceHistoryFormDialog> createState() =>
      _ServiceHistoryFormDialogState();
}

class _ServiceHistoryFormDialogState extends State<ServiceHistoryFormDialog> {
  final _formKey = GlobalKey<FormBuilderState>();

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Детали назначения на должность'),
      content: SingleChildScrollView(
        child: FormBuilder(
          key: _formKey,
          initialValue: const {},
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'Должность',
                  border: OutlineInputBorder(),
                ),
                child: Text(widget.position.title),
              ),
              const SizedBox(height: 16),
              FormBuilderDateTimePicker(
                name: 'startDate',
                inputType: InputType.date,
                format: DateFormat('dd.MM.yyyy'),
                decoration: const InputDecoration(
                  labelText: 'Дата начала работы в должности',
                  border: OutlineInputBorder(),
                ),
                validator: FormBuilderValidators.required(
                    errorText: 'Укажите дату начала работы'),
              ),
              const SizedBox(height: 16),
              FormBuilderDateTimePicker(
                name: 'acceptanceDate',
                inputType: InputType.date,
                format: DateFormat('dd.MM.yyyy'),
                decoration: const InputDecoration(
                  labelText: 'Дата принятия дел и должности',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              FormBuilderDateTimePicker(
                name: 'handoverDate',
                inputType: InputType.date,
                format: DateFormat('dd.MM.yyyy'),
                decoration: const InputDecoration(
                  labelText: 'Дата сдачи дел по предыдущей должности',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              FormBuilderTextField(
                name: 'orderId',
                decoration: const InputDecoration(
                  labelText: 'ID приказа о назначении',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),
              FormBuilderTextField(
                name: 'externalOrderInfo',
                decoration: const InputDecoration(
                  labelText: 'Номер и дата приказа',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              FormBuilderTextField(
                name: 'notes',
                decoration: const InputDecoration(
                  labelText: 'Примечания',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Отмена'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState?.saveAndValidate() ?? false) {
              final data = _formKey.currentState!.value;
              final serviceHistory = ServiceHistory(
                employeeId: 0, // заменим позже в вызове
                positionId: widget.position.id,
                externalPositionTitle: widget.position.title,
                startDate: DateFormat('yyyy-MM-dd').format(data['startDate']),
                endDate: data['handoverDate'] != null
                    ? DateFormat('yyyy-MM-dd').format(data['handoverDate'])
                    : null,
                acceptanceDate: data['acceptanceDate'] != null
                    ? DateFormat('yyyy-MM-dd').format(data['acceptanceDate'])
                    : null,
                orderId: data['orderId'] != null
                    ? int.tryParse(data['orderId'].toString())
                    : null,
                externalOrderInfo: data['externalOrderInfo'],
                notes: data['notes'],
              );
              Navigator.of(context).pop(serviceHistory);
            }
          },
          child: const Text('Сохранить'),
        ),
      ],
    );
  }
}
