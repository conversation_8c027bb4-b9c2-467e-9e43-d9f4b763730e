// ignore_for_file: use_build_context_synchronously, unused_local_variable, avoid_print

import 'package:flutter/material.dart';
import 'package:frontend/components/file_upload_dialog.dart';
import 'package:frontend/models/order.dart';
import 'package:frontend/models/order_attachment.dart';
import 'package:frontend/providers/order_provider.dart';
import 'package:frontend/providers/order_attachment_provider.dart';
import 'package:frontend/utils/download_utils.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

class OrderDetailsPage extends StatefulWidget {
  final int orderId;

  const OrderDetailsPage({
    super.key,
    required this.orderId,
  });

  @override
  State<OrderDetailsPage> createState() => _OrderDetailsPageState();
}

class _OrderDetailsPageState extends State<OrderDetailsPage> {
  Order? _order;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadOrder();
    // Деферируем загрузку вложений до окончания первого кадра, чтобы избежать notifyListeners во время сборки
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadAttachments();
    });
  }

  Future<void> _loadOrder() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final order = await Provider.of<OrderProvider>(context, listen: false)
          .getOrder(widget.orderId);

      setState(() {
        _order = order;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _loadAttachments() async {
    try {
      await Provider.of<OrderAttachmentProvider>(context, listen: false)
          .fetchAttachmentsByOrderId(widget.orderId);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Ошибка при загрузке вложений: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _uploadAttachment() async {
    showDialog(
      context: context,
      builder: (context) => FileUploadDialog(
        onUpload: (fileName, fileData) async {
          try {
            await Provider.of<OrderAttachmentProvider>(context, listen: false)
                .createAttachment(widget.orderId, fileName, fileData);

            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Файл успешно загружен'),
                  backgroundColor: Colors.green,
                ),
              );
            }
          } catch (e) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Ошибка при загрузке файла: $e'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        },
      ),
    );
  }

  Future<void> _downloadAttachment(OrderAttachment attachment) async {
    try {
      // Показываем индикатор загрузки
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Загрузка файла...'),
            duration: Duration(seconds: 1),
          ),
        );
      }

      if (attachment.id == null) {
        throw Exception('ID вложения не может быть null');
      }

      // Используем утилиту для скачивания файла
      await DownloadUtils.downloadAttachment(
          attachment.id!, attachment.fileName);

      // Показываем сообщение об успешном скачивании
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Файл скачивается...'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      print('Ошибка при скачивании файла: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Ошибка при скачивании файла: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteAttachment(OrderAttachment attachment) async {
    final confirmed = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Подтверждение'),
            content: Text(
                'Вы уверены, что хотите удалить файл "${attachment.fileName}"?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Отмена'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Удалить'),
              ),
            ],
          ),
        ) ??
        false;

    if (confirmed) {
      try {
        await Provider.of<OrderAttachmentProvider>(context, listen: false)
            .deleteAttachment(attachment.id!, widget.orderId);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Файл успешно удален'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Ошибка при удалении файла: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            context.go('/orders');
          },
        ),
        title: Text(
            _order != null ? 'Приказ №${_order!.number}' : 'Детали приказа'),
        actions: [
          if (_order != null)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () {
                context.go('/orders/${_order!.id}/edit');
              },
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(
                  child: Text(
                    'Ошибка: $_error',
                    style: const TextStyle(color: Colors.red),
                  ),
                )
              : _order == null
                  ? const Center(
                      child: Text('Приказ не найден'),
                    )
                  : SingleChildScrollView(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildInfoCard(),
                          const SizedBox(height: 16),
                          _buildDescriptionCard(),
                          const SizedBox(height: 16),
                          _buildAttachmentsCard(),
                          const SizedBox(height: 16),
                          _buildMetadataCard(),
                        ],
                      ),
                    ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Основная информация',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Divider(),
            _buildInfoRow('Номер приказа', _order!.number),
            _buildInfoRow('Дата', _order!.date),
            _buildInfoRow('Издан', _order!.issuedBy),
          ],
        ),
      ),
    );
  }

  Widget _buildDescriptionCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Описание',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Divider(),
            Text(_order!.description ?? 'Описание отсутствует'),
          ],
        ),
      ),
    );
  }

  Widget _buildMetadataCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Метаданные',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Divider(),
            _buildInfoRow('ID', _order!.id.toString()),
            if (_order!.createdAt != null)
              _buildInfoRow('Создан', _order!.createdAt!),
            if (_order!.updatedAt != null)
              _buildInfoRow('Обновлен', _order!.updatedAt!),
          ],
        ),
      ),
    );
  }

  Widget _buildAttachmentsCard() {
    final attachmentProvider = Provider.of<OrderAttachmentProvider>(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Вложения',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _uploadAttachment,
                  icon: const Icon(Icons.upload_file),
                  label: const Text('Загрузить'),
                ),
              ],
            ),
            const Divider(),
            attachmentProvider.isLoading
                ? const Center(child: CircularProgressIndicator())
                : attachmentProvider.error != null
                    ? Text(
                        'Ошибка: ${attachmentProvider.error}',
                        style: const TextStyle(color: Colors.red),
                      )
                    : attachmentProvider.attachments.isEmpty
                        ? const Text('Нет вложений')
                        : ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: attachmentProvider.attachments.length,
                            itemBuilder: (context, index) {
                              final attachment =
                                  attachmentProvider.attachments[index];
                              return ListTile(
                                leading: const Icon(Icons.description),
                                title: Text(attachment.fileName),
                                subtitle: attachment.uploadedAt != null
                                    ? Text(
                                        'Загружено: ${attachment.uploadedAt}')
                                    : null,
                                trailing: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    IconButton(
                                      icon: const Icon(Icons.download),
                                      onPressed: () =>
                                          _downloadAttachment(attachment),
                                      tooltip: 'Скачать',
                                    ),
                                    IconButton(
                                      icon: const Icon(Icons.delete),
                                      onPressed: () =>
                                          _deleteAttachment(attachment),
                                      tooltip: 'Удалить',
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}
